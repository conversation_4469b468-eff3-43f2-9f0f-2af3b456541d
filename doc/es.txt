/** 清空租户的商品索引 */
POST es_product_sku/_delete_by_query
{
  "query": {
    "term": {
      "tenantId": {
        "value": "152"
      }
    }
  }
}


POST prod-log-mall-server-2024.03.07/_delete_by_query

POST pre-log-mall-server-2024*/_delete_by_query
{
  "query": {
    "range": {
      "@timestamp": {
        "gte": "2024-03-01T00:00:00Z",
        "lte": "2024-03-10T23:59:59Z"
      }
    }
  }
}

DELETE prod-log-mall-server-2024.04*



POST es_product_sku/_delete_by_query
{
  "query": {
    "bool": {
      "must": [
        {
          "term": {
            "tenantId": {
              "value": "152"
            }
          }
        },
        {
          "term": {
            "supplierId": {
              "value": "8"
            }
          }
        }
      ]
    }
  }
}

#查询ES磁盘使用率：
GET /_cat/nodes?v&h=id,disk.avail,disk.used,disk.total,disk.percent,heap.percent,ram.percent

#增加ES单节点分片数量
PUT /_cluster/settings
{
  "persistent": {
    "cluster": {
      "max_shards_per_node":900000
    }
  }
}

#查询索引大小并排序
GET /_cat/indices?v&h=index,store.size&bytes=gb&s=store.size:desc

DELETE prod-log-mall-fl-server-2024.06*
DELETE prod-log-mall-fl-server-2024.07*
DELETE prod-log-system-server-2024.06*
DELETE prod-log-system-server-2024.07*
DELETE prod-log-infra-server-2024.06*
DELETE prod-log-infra-server-2024.07*

DELETE prod-log-jd-vop-client-2025.01*
DELETE prod-log-jd-vop-client-2025.02*
DELETE prod-log-jd-vop-client-2025.03*
DELETE prod-log-jd-vop-client-2025.04*

DELETE prod-log-mall-server-2025.01*
DELETE prod-log-mall-server-2025.02*
DELETE prod-log-mall-server-2025.03*
DELETE prod-log-mall-server-2025.04*

--- 清理缓存，生产环境慎用--
POST /_cache/clear?fielddata=true

----索引滚动清理策略
PUT _ilm/policy/prod-log-policy
{
  "policy": {
    "phases": {
      "hot": {
        "actions": {
          "rollover": {
            "max_size": "70GB",   # 索引达到 100GB 后触发滚动
            "max_age": "60d"       # 可选：即使未达 100GB，30天后也强制滚动
          }
        }
      },
      "delete": {
        "min_age": "5d",          # 滚动后保留 7 天再删除（按需调整）
        "actions": {
          "delete": {}            # 删除旧索引
        }
      }
    }
  }
}

PUT _index_template/prod-log-template
{
  "index_patterns": ["prod-log-mall-server*"],  # 匹配索引名称
  "template": {
    "settings": {
      "index.lifecycle.name": "prod-log-policy",    # 绑定 ILM 策略
      "index.lifecycle.rollover_alias": "prod-log",# 必须的别名（见下方说明）
      "number_of_shards": 2,                       # 分片数（按需调整）
      "number_of_replicas": 1                      # 副本数（按需调整）
    }
  }
}


output {
  elasticsearch {
    hosts => ["http://es-host:9200"]
    index => "prod-log"      # 写入到别名（非实际索引名）
    ilm_enabled => true
    ilm_policy => "prod-log-policy"  # 可选：显式指定策略（通常模板已关联）
  }
}



# 执行一条命令直接修改模板，修复message变成_msg问题；
PUT _index_template/logs_template
{
  "index_patterns": ["pre-log-*"],
  "template": {
    "mappings": {
      "properties": {
        "message": { "type": "text" }
      }
    }
  },
  "priority": 100
}