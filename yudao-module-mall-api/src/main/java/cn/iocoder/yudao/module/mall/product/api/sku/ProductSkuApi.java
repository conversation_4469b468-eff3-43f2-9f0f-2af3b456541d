package cn.iocoder.yudao.module.mall.product.api.sku;

import cn.iocoder.yudao.module.mall.product.api.sku.dto.*;

import java.util.Collection;
import java.util.List;

/**
 * 商品 SKU API 接口
 *
 * <AUTHOR>
 * @since 2022-08-26
 */
public interface ProductSkuApi {

    /**
     * 查询 SKU 信息
     *
     * @param skuId SKU 编号
     * @param isJd 是否京东
     * @return SKU 信息
     */
    ProductSkuRespDTO getSku(Long skuId, Boolean isJd);

    List<ProductSkuRespDTO> getSkus(List<Long> skuIds, Boolean isJd);

    List<ProductSkuRespDTO> getLocalSkus(List<Long> skuIds);

    List<ProductSkuRespDTO> getLocalSkusByIds(Collection<Long> skuIds);

    ProductSkuRespDTO getLocalSku(Long skuId);

    List<ProductSkuRespDTO> getSimpleSkusByInnerIds(List<String> innerIds, Long supplierId);

    /**
     * 批量查询 SKU 数组
     *
     * @param ids SKU 编号列表
     * @return SKU 数组
     */
    List<ProductSkuRespDTO> getSkuList(Collection<Long> ids);

    /**
     * 更新 SKU 库存
     *
     * @param updateStockReqDTO 更新请求
     */
    void updateSkuStock(ProductSkuUpdateStockReqDTO updateStockReqDTO);


    /**
     * 校验京东商品是否可售
     * @param reqs
     * @param area
     * @return
     */
    List<CanSaleRespDTO> getJdSkusAllSaleState(List<SkuBaseReqDTO> reqs, AreaDTO area);


    /**
     * 查询商品运费
     * @param reqs
     * @param area
     * @param paymentType
     * @return
     */
    FreightRespDTO querySkuFreight(List<SkuBaseReqDTO> reqs, AreaDTO area, Integer paymentType);

}
