package cn.iocoder.yudao.module.mall.trade.enums.order;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Objects;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/8/9 9:23
 */

@RequiredArgsConstructor
@Getter
public enum InvoiceStatusEnum implements IntArrayValuable  {

    // 发票状态： 0 未开票 1 已申请开票 2 开票完成
    NOT_APPLY_INVOICE(0, "未开票"),
    HAS_APPLY_INVOICE(1, "已申请开票"),
    APPLY_INVOICE_SUCCESS(2, "开票完成"),
    APPLY_INVOICE_FAIL(3, "开票失败"),
    VALIDATE_DOING(4, "验真处理中"),
    VALIDATE_SUCCESS(5, "验真完成"),
    VALIDATE_FAIL(6, "验真失败"),
    ;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(InvoiceStatusEnum::getStatus).toArray();

    /**
     * 状态值
     */
    private final Integer status;
    /**
     * 状态名
     */
    private final String name;

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static String getInvoiceDesc(Integer status) {
        for (InvoiceStatusEnum invoiceStatusEnum : InvoiceStatusEnum.values()) {
            if (Objects.equals(status, invoiceStatusEnum.getStatus())) {
                return invoiceStatusEnum.getName();
            }
        }
        return "";
    }

}
