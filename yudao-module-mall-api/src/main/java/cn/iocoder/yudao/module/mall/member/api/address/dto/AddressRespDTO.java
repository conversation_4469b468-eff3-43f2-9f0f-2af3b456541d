package cn.iocoder.yudao.module.mall.member.api.address.dto;

import lombok.Data;

/**
 * 用户收件地址 Response DTO
 *
 * <AUTHOR>
 */
@Data
public class AddressRespDTO {

    /**
     * 编号
     */
    private Long id;

    /**
     * 用户编号
     */
    private Long userId;

    /**
     * 收件人名称
     */
    private String name;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 省编号
     */
    private Long provinceId;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市编号
     */
    private Long cityId;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区县编号
     */
    private Long countyId;

    /**
     * 区名称
     */
    private String countyName;
    /**
     * 乡镇编号
     */
    private Long townId;

    /**
     * 乡镇名称
     */
    private String townName;

    /**
     * 邮编
     */
    private String consigneeZip;

    /**
     * 收件详细地址
     */
    private String consigneeAddress;

    /**
     * 是否默认
     *
     * true - 默认收件地址
     */
    private Boolean defaulted;

}
