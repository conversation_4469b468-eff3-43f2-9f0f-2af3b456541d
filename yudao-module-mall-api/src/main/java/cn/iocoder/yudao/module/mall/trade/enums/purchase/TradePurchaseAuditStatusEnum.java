package cn.iocoder.yudao.module.mall.trade.enums.purchase;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * 采购单审批状态枚举
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Getter
public enum TradePurchaseAuditStatusEnum implements IntArrayValuable {

    NO_APPROVE(-2, "未审批"),
    APPROVING(-1, "审批中"),
    NONE(0, "撤销"),
    PASS(1, "审批通过"),
    REJECT(2, "审批不通过"),;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(TradePurchaseAuditStatusEnum::getStatus).toArray();

    /**
     * 审批状态
     */
    private final Integer status;
    /**
     * 状态名
     */
    private final String name;

    @Override
    public int[] array() {
        return ARRAYS;
    }

}
