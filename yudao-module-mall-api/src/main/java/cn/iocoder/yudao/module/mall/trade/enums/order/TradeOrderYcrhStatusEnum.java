package cn.iocoder.yudao.module.mall.trade.enums.order;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 业财融合中订单状态
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Getter
public enum TradeOrderYcrhStatusEnum  {

    INIT("-2","未推送"),
    CANCEL("0","已取消"),
    PUSH("1","已推送"),
    APPROVING("2", "审批中"),
    APPROVE_COMPLETE("3", "已审批"),
    CREATED("4", "已下单"),
    WAIT_SETTLE("5", "待结算"),
    SETTLING("6", "结算中"),
    SETTLE_COMPLETE("7", "已结算"),
    SPLIT("11", "已拆分"),
    REJECT("21", "已驳回");

    /**
     * 状态值
     */
    private final String status;
    /**
     * 状态名
     */
    private final String name;
}
