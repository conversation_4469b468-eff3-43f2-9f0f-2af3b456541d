package cn.iocoder.yudao.module.mall.trade.enums.order;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import cn.iocoder.yudao.framework.common.util.object.ObjectUtils;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * 业财融合审核状态
 */
@RequiredArgsConstructor
@Getter
public enum TradeAuditStatusEnum implements IntArrayValuable {
    /**
     * 审批状态
     */
    NOT_AUDIT(1, "未审批"),
    DO_AUDITING(2, "审批中"),
    REJECT(3, "审批驳回"),
    APPROVED(4, "审批通过"),
    ;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(TradeAuditStatusEnum::getStatus).toArray();

    /**
     * 状态值
     */
    private final Integer status;
    /**
     * 状态名
     */
    private final String name;
    @Override
    public int[] array() {
        return ARRAYS;
    }

    /**
     * 判断是否审批驳回或审批中
     * @param status
     * @return
     */
    public static boolean rejectedOrAuditing(int status) {
        return ObjectUtils.equalsAny(status, DO_AUDITING.status, REJECT.status);
    }
}
