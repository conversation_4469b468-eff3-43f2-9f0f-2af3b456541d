package cn.iocoder.yudao.module.mall.product.api.sku.dto;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商品 SKU 信息 Response DTO
 *
 * <AUTHOR>
 * @since 2022-08-26
 */
@Data
public class ProductSkuRespDTO {

    /**
     * 商品 SKU 编号，自增
     */
    private Long id;
    /**
     * SPU 编号
     */
    private Long spuId;
    /**
     * SPU 名字
     */
    private String spuName;

    /**
     * 商品id
     */
    @Schema(description = "商品id", example = "芋道")
    private Long skuId;
    /**
     * 供应商商品id
     */
    @Schema(description = "供应商商品id", example = "芋道")
    private String skuInnerId;

    /**
     * 商品 SKU 名字
     */
    @Schema(description = "商品 SKU 名字", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋道")
    private String skuName;

    /**
     * 属性数组，JSON 格式
     */
    private List<Property> properties;
    /**
     * 销售价格
     */
    private BigDecimal salePrice;
    /**
     * 市场价
     */
    private BigDecimal marketPrice;
    /**
     * 成本价
     */
    private BigDecimal costPrice;
    /**
     * SKU 的条形码
     */
    private String barCode;
    /**
     * 图片地址
     */
    private String picUrl;
    /**
     * SKU 状态
     * <p>
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;
    /**
     * SKU 最低购买量
     */
    private Integer lowestBuy;
    /**
     * 库存
     */
    private Integer stock;
    /**
     * 预警预存
     */
    private Integer warnStock;
    /**
     * 商品重量，单位：kg 千克
     */
    private Double weight;
    /**
     * 商品体积，单位：m^3 平米
     */
    private Double volume;

    /**
     * 商品分类
     */
    private String categoryCode;
    /**
     * 商品分类Id全路径，中划线链接
     */
    private String fullCategoryId;
    /**
     * 商品分类名称全路径，中划线链接
     */
    private String fullCategoryName;
    /**
     * 商品分类名称
     */
    private String categoryName;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商类型
     */
    private Integer supplierType;

    /**
     * 商品属性
     */
    @Data
    public static class Property {

        /**
         * 属性编号
         */
        private Long propertyId;
        /**
         * 属性值编号
         */
        private Long valueId;

    }


}
