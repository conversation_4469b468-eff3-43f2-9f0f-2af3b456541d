package cn.iocoder.yudao.module.mall.product.api.sku.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商品运费信息
 * <AUTHOR>
 * @date 2023/7/7
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "商品运费信息")
public class FreightRespDTO {

    /**
     * 基础运费
     */
    @Schema(description = "基础运费")
    private BigDecimal baseFreight;

    /**
     * 偏远地区收取运费SKU
     */
    @Schema(description = "偏远地区收取运费SKU")
    private List<Long> remoteRegionSkuIdList;

    /**
     * 运费总计
     */
    @Schema(description = "运费总计")
    private BigDecimal totalFreight;

    /**
     * 偏远地区运费
     */
    @Schema(description = "偏远地区运费")
    private BigDecimal remoteRegionFreight;

    /**
     * 续重运费
     */
    @Schema(description = "续重运费")
    private BigDecimal continueWeightFreight;
}
