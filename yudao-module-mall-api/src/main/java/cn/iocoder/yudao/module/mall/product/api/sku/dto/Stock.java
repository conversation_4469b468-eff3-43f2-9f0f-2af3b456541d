package cn.iocoder.yudao.module.mall.product.api.sku.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户APP - 商品库存
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户APP - 商品库存")
public class Stock {

    /**
     * 商品id
     */
    @Schema(description = "商品id")
    private Long skuId;

    /**
     * 库存状态类型，参考枚举值： 33,39,40,36,34,99
     */
    @Schema(description = "库存状态类型 参考枚举值： 33,39,40,36,34,99", requiredMode = Schema.RequiredMode.REQUIRED, example = "33")
    private Integer stockStateType;

    /**
     * 库存状态描述。以下为stockStateId不同时，此字段不同的返回值： 33 有货 现货-下单立即发货 39 有货 在途-正在内部配货，预计2\\\\x7e6天到达本仓库 40 有货 可配货-下单后从有货仓库配货 36 预订 34 无货 99 无货开预定
     */
    @Schema(description = "库存状态描述。以下为stockStateId不同时，此字段不同的返回值： 33 有货 现货-下单立即发货 39 有货 在途-正在内部配货，预计2\\\\\\\\x7e6天到达本仓库 40 有货 可配货-下单后从有货仓库配货 36 预订 34 无货 99 无货开预定", requiredMode = Schema.RequiredMode.REQUIRED, example = "有货")
    private String stockStateDesc;

    /**
     * 剩余数量。当此值为-1时，为未查询到。StockStateDesc为33：入参的skuNums字段，skuId对应的num小于50，此字段为实际库存。入参的skuNums字段，skuId对应的num大于等50于并小于100，此字段为-1。入参的skuNums字段，skuId对应的num大于100，此字段等于num。(此种情况并未返回真实京东库存)
     */
    @Schema(description = "剩余数量。当此值为-1时，为未查询到。StockStateDesc为33：入参的skuNums字段，skuId对应的num小于50，此字段为实际库存。入参的skuNums字段，skuId对应的num大于等50于并小于100，此字段为-1。入参的skuNums字段，skuId对应的num大于100，此字段等于num。(此种情况并未返回真实京东库存)", requiredMode = Schema.RequiredMode.REQUIRED, example = "有货")
    private Integer remainNumInt;
}
