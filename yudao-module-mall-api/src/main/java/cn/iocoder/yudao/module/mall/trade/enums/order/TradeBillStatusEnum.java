package cn.iocoder.yudao.module.mall.trade.enums.order;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * 账单状态
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Getter
public enum TradeBillStatusEnum implements IntArrayValuable {
    /**
     * 账单状态 0-新建 1-已推送 2-已取消 3-已完成
     */
    INIT(0,"新建"),
    PUSH(1,"已推送"),
    CANCEL(2, "已取消"),
    COMPLETE(3, "已完成");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(TradeBillStatusEnum::getStatus).toArray();

    /**
     * 状态值
     */
    private final Integer status;
    /**
     * 状态名
     */
    private final String name;

    @Override
    public int[] array() {
        return ARRAYS;
    }
}
