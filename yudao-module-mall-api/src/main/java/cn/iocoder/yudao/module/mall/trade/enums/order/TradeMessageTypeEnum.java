package cn.iocoder.yudao.module.mall.trade.enums.order;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/9/18
 */
@RequiredArgsConstructor
@Getter
public enum TradeMessageTypeEnum implements IntArrayValuable {

    /**
     * 消息读取状态
     */
    CREATE_ORDER(1, "创建订单"),
    CONFIRM_ORDER(2, "确认订单"),
    CHECK_AND_ACCEPT(5, "已签收"),
    COMPLETE_ORDER(8,"完成订单"),
    CANCEL_ORDER(9,"取消订单"),
    AFTER_SALE_NEW(20, "订单售后申请"),
    AFTER_SALE_DELIVERY(21, "订单售后发货"),
    AFTER_SALE_CANCEL(22, "订单售后取消"),
    AFTER_SALE_AGREE(23, "订单售后通过"),
    AFTER_SALE_DISAGREE(24, "订单售后驳回"),
    AFTER_SALE_RECEIVE(25, "订单售后确认收货"),
    AFTER_SALE_REFUSE(26, "订单售后拒绝收货"),
    INVOICE_APPLY(30, "发票申请"),
    INVOICE_CANCEL(31, "发票冲红");

    private final Integer code;

    private final String desc;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(TradeMessageTypeEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }
}
