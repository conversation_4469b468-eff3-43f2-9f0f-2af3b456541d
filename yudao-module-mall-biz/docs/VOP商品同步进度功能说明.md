# VOP商品同步进度功能说明

## 功能概述

为`syncVopProductByCategory`方法添加了进度保存和恢复功能，支持程序重启后继续上次的同步进度，并确保同时只能执行一个相同参数的同步任务。

## 主要特性

1. **实时进度保存**：同步过程中实时将进度保存到Redis
2. **断点续传**：程序重启后可以从上次中断的位置继续同步
3. **参数一致性检查**：只有相同参数的调用才能继续上次的进度
4. **并发控制**：使用分布式锁确保相同参数的同步任务同时只能执行一次
5. **自动清理**：同步完成后自动清除进度信息
6. **手动管理**：提供API接口查询和清除进度

## 并发控制机制

### 分布式锁设计

使用Redisson分布式锁实现并发控制：

```java
// 锁Key格式
String lockKey = "vop_sync_lock:{tenantId}:{categoryId}:{maxPageSize}";

// 控制器层并发控制
if (!lock.tryLock(1, TimeUnit.SECONDS)) {
    return success("同步任务已在运行中，请稍后再试");
}

// 服务层并发控制  
if (!syncLock.tryLock(5, TimeUnit.SECONDS)) {
    log.info("同步任务已在运行中，跳过执行");
    return;
}
```

### 并发场景处理

1. **相同参数并发调用**：第二次调用直接返回"任务已在运行中"
2. **不同参数并发调用**：可以同时执行（使用不同的锁Key）
3. **API和直接调用**：两层锁保护，确保任何调用方式都受控制
4. **异常情况**：finally块确保锁被正确释放

## 核心组件

### 1. VopProductSyncProgress 进度数据结构

```java
public class VopProductSyncProgress {
    private Long tenantId;              // 租户ID
    private Long categoryId;            // 分类ID参数
    private Long maxPageSize;           // 最大页数参数
    private BigDecimal minPrice;        // 最小价格参数
    private BigDecimal maxPrice;        // 最大价格参数
    private Integer currentCategoryIndex; // 当前处理的分类索引
    private Integer currentPageIndex;   // 当前处理的页码
    private Integer totalCategoryCount; // 总分类数量
    private Long syncedProductCount;    // 已同步的商品总数
    private Boolean completed;          // 是否完成
    // ... 其他字段
}
```

### 2. 并发控制方法

```java
// 生成锁Key
private String generateSyncLockKey(Long tenantId, Long categoryId, Long maxPageSize) {
    return String.format("vop_sync_lock:%d:%s:%s", 
        tenantId, 
        categoryId != null ? categoryId.toString() : "all",
        maxPageSize != null ? maxPageSize.toString() : "default");
}
```

### 3. 双层锁保护

- **控制器层**：防止API重复调用
- **服务层**：防止直接调用服务方法

## 使用方式

### 1. 启动同步任务

```java
// 同步所有分类
vopGoodsBridgeService.syncVopProductByCategory(null, 100L);

// 同步指定分类
vopGoodsBridgeService.syncVopProductByCategory(123L, 50L);
```

### 2. 通过HTTP API管理

```bash
# 启动同步（如果已有任务运行会返回提示）
POST /mall/vop-product-sync/start?categoryId=123&maxPageSize=50
# 返回: "同步任务已启动" 或 "同步任务已在运行中，请稍后再试"

# 查询进度
GET /mall/vop-product-sync/progress?categoryId=123&maxPageSize=50

# 清除进度
DELETE /mall/vop-product-sync/progress?categoryId=123&maxPageSize=50
```

## 工作流程

### 1. 并发控制流程

```
请求 -> 尝试获取分布式锁 -> 成功？
                           |
                          是 -> 检查现有进度 -> 启动同步任务
                           |
                          否 -> 返回"任务已在运行中"
```

### 2. 同步执行流程

```
获取锁 -> 检查进度 -> 参数匹配？
                      |
                     是 -> 继续上次进度
                      |  
                     否 -> 重新开始
                      |
                     完成 -> 释放锁 -> 清除进度
```

## 锁机制详解

### 锁Key设计

```
vop_sync_lock:{tenantId}:{categoryId}:{maxPageSize}
```

**示例：**
- `vop_sync_lock:1:123:50` - 租户1，分类123，最大页数50
- `vop_sync_lock:1:all:100` - 租户1，所有分类，最大页数100
- `vop_sync_lock:2:123:50` - 租户2，分类123，最大页数50

### 锁超时设置

- **控制器层**：1秒超时，快速失败
- **服务层**：5秒超时，允许短暂等待

### 锁释放机制

1. **正常完成**：同步完成后自动释放
2. **异常情况**：finally块确保释放
3. **线程中断**：捕获InterruptedException并释放

## 测试验证

### 并发测试用例

1. **相同参数并发调用**：验证只有一个成功
2. **不同参数并发调用**：验证可以同时执行
3. **锁超时测试**：验证超时机制正常工作
4. **异常释放测试**：验证异常情况下锁被正确释放

### 测试代码示例

```java
@Test
public void testConcurrentSyncPrevention() {
    // 模拟多个并发请求
    int threadCount = 5;
    AtomicInteger successCount = new AtomicInteger(0);
    AtomicInteger rejectedCount = new AtomicInteger(0);
    
    // 验证只有一个请求成功，其他被拒绝
    assertEquals(1, successCount.get());
    assertEquals(threadCount - 1, rejectedCount.get());
}
```

## 注意事项

1. **锁粒度**：基于租户+参数组合，不同参数可并发执行
2. **锁超时**：避免死锁，设置合理的超时时间
3. **异常处理**：确保任何情况下锁都能被正确释放
4. **日志记录**：详细记录锁的获取和释放过程
5. **性能影响**：分布式锁有一定性能开销，但可接受

## 监控和调试

### 关键日志

- "获取同步锁成功"
- "同步任务已在运行中，跳过执行"
- "释放同步锁"
- "VOP商品同步任务已在运行中"

### Redis监控

```bash
# 查看当前活跃的锁
redis-cli keys "vop_sync_lock:*"

# 查看锁的TTL
redis-cli ttl "vop_sync_lock:1:123:50"
```

## 扩展性

该并发控制设计支持：
1. 多租户隔离
2. 不同同步任务类型
3. 灵活的锁粒度控制
4. 集群环境下的一致性保证
