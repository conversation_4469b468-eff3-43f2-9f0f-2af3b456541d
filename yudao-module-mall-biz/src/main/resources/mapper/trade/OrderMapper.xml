<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.mall.trade.dal.mysql.order.TradeOrderMapper">

    <resultMap id="TradeOrderItemDO" type="cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderItemDO">
        <result column="id" property="id"></result>
        <result column="user_id" property="userId"></result>
        <result column="supplier_id" property="supplierId"></result>
        <result column="supplier_type" property="supplierType"></result>
        <result column="order_id" property="orderId"></result>
        <result column="sku_id" property="skuId"></result>
        <result column="sku_name" property="skuName"></result>
        <result column="pic_url" property="picUrl"></result>
        <result column="count" property="count"></result>
        <result column="commented" property="commented"></result>
        <result column="sku_price" property="skuPrice"></result>
        <result column="sku_total_price" property="skuTotalPrice"></result>
        <result column="after_sale_status" property="afterSaleStatus"></result>
        <result column="after_sale_memo" property="afterSaleMemo"></result>
        <result column="after_sale_count" property="afterSaleCount"></result>
        <result column="category_code" property="categoryCode"></result>
        <result column="category_name" property="categoryName"></result>
        <result column="asset_status" property="assetStatus"></result>
        <result column="create_time" property="createTime"></result>
    </resultMap>

    <resultMap id="TradeOrderDO" type="cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderDO">
        <result column="id" property="id"></result>
        <result column="no" property="no"></result>
        <result column="type" property="type"></result>
        <result column="parent_type" property="parentType"></result>
        <result column="parent_order_id" property="parentOrderId"></result>
        <result column="parent_no" property="parentNo"></result>
        <result column="user_id" property="userId"></result>
        <result column="user_name" property="userName"></result>
        <result column="supplier_id" property="supplierId"></result>
        <result column="supplier_type" property="supplierType"></result>
        <result column="supplier_name" property="supplierName"></result>
        <result column="user_ip" property="userIp"></result>
        <result column="status" property="status"></result>
        <result column="product_count" property="productCount"></result>
        <result column="finish_time" property="finishTime"></result>
        <result column="cancel_time" property="cancelTime"></result>
        <result column="cancel_type" property="cancelType"></result>
        <result column="cancel_reason" property="cancelReason"></result>
        <result column="user_remark" property="userRemark"></result>
        <result column="submit_time" property="submitTime"></result>
        <result column="confirm_time" property="confirmTime"></result>
        <result column="payment_method" property="paymentMethod"></result>
        <result column="pay_order_id" property="payOrderId"></result>
        <result column="payed" property="payed"></result>
        <result column="pay_time" property="payTime"></result>
        <result column="pay_channel_code" property="payChannelCode"></result>
        <result column="product_price" property="productPrice"></result>
        <result column="delivery_price" property="deliveryPrice"></result>
        <result column="order_price" property="orderPrice"></result>
        <result column="pay_price" property="payPrice"></result>
        <result column="delivery_status" property="deliveryStatus"></result>
        <result column="delivery_time" property="deliveryTime"></result>
        <result column="receive_time" property="receiveTime"></result>
        <result column="receiver_name" property="receiverName"></result>
        <result column="receiver_mobile" property="receiverMobile"></result>
        <result column="receiver_consignee_zip" property="receiverConsigneeZip"></result>
        <result column="receiver_address_id" property="receiverAddressId"></result>
        <result column="receiver_province" property="receiverProvince"></result>
        <result column="receiver_province_name" property="receiverProvinceName"></result>
        <result column="receiver_city" property="receiverCity"></result>
        <result column="receiver_city_name" property="receiverCityName"></result>
        <result column="receiver_county" property="receiverCounty"></result>
        <result column="receiver_county_name" property="receiverCountyName"></result>
        <result column="receiver_town" property="receiverTown"></result>
        <result column="receiver_town_name" property="receiverTownName"></result>
        <result column="receiver_detail_address" property="receiverDetailAddress"></result>
        <result column="after_sale_status" property="afterSaleStatus"></result>
        <result column="refund_price" property="refundPrice"></result>
        <result column="tip" property="tip"></result>
        <result column="vop_order_status" property="vopOrderStatus"></result>
        <result column="third_order_id" property="thirdOrderId"></result>
        <result column="parent_third_order_id" property="parentThirdOrderId"></result>
        <result column="settle_status" property="settleStatus"></result>
        <result column="audit_status" property="auditStatus"></result>
        <result column="audit_submit_time" property="auditSubmitTime"></result>
        <result column="audit_complete_time" property="auditCompleteTime"></result>
        <result column="bill_status" property="billStatus"></result>
        <result column="bill_submit_time" property="billSubmitTime"></result>
        <result column="bill_complete_time" property="billCompleteTime"></result>
        <result column="invoice_status" property="invoiceStatus"></result>
        <result column="asset_status" property="assetStatus"></result>
        <result column="user_deleted" property="userDeleted"></result>
        <result column="ycrh_need" property="ycrhNeed"></result>
        <result column="ycrh_status" property="ycrhStatus"></result>
        <result column="platform" property="platform"></result>
        <result column="purchase_id" property="purchaseId"></result>
        <result column="need_purchase" property="needPurchase"></result>
        <result column="offline_settlement" property="offlineSettlement"></result>
        <result column="split_status" property="splitStatus"></result>
        <result column="split_time" property="splitTime"></result>
        <result column="team_order_id" property="teamOrderId"></result>
        <result column="create_time" property="createTime"></result>
        <result column="update_time" property="updateTime"></result>
    </resultMap>

    <resultMap id="orderFullMap1" extends="TradeOrderDO" type="cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderDO">
    </resultMap>

    <resultMap id="orderFullMap2" extends="TradeOrderDO" type="cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderDO">
        <collection property="itemList" resultMap="TradeOrderItemDO" javaType="java.util.ArrayList" columnPrefix="oi_" ofType="cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderItemDO">
        </collection>
    </resultMap>

    <select id="getOrderNos" parameterType="cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo.TradeOrderPageReqVO" resultType="String">
        select
            t1.no
        from trade_order t1
        <where>
            t1.deleted = 0
            <if test="params.no != null and params.no != ''">
                and ( t1.no = #{params.no} or t1.parent_no = #{params.no} or t1.third_order_id = #{params.no} )
            </if>
            <if test="params.parentNo != null and params.parentNo != ''">
                and t1.parent_no = #{params.parentNo}
            </if>
            <if test="params.bpmNo != null and params.bpmNo != ''">
                t1.bpm_no = #{params.bpmNo}
            </if>
            <if test="params.orderCode != null and params.orderCode != ''">
                and (
                t1.no LIKE CONCAT('%', #{params.orderCode}, '%')
                or t1.parent_no LIKE CONCAT('%', #{params.orderCode}, '%')
                or t1.third_order_id LIKE CONCAT('%', #{params.orderCode}, '%')
                or t1.parent_third_order_id LIKE CONCAT('%', #{params.orderCode}, '%')
                or t1.bpm_no LIKE CONCAT('%', #{params.orderCode}, '%')
                )
            </if>
            <if test="params.userMobile != null and params.userMobile != ''">
                and exists(select 1 from member_user tmu where t1.user_id = tmu.id and tmu.deleted = 0 and tmu.mobile = #{params.userMobile})
            </if>
            <if test="params.userNickname != null and params.userNickname != ''">
                and exists(select 1 from member_user tmu where t1.user_id = tmu.id and tmu.deleted = 0 and tmu.nickname LIKE CONCAT('%', #{params.userNickname}, '%'))
            </if>
            <if test="params.userId != null">
                and t1.user_id = #{params.userId}
            </if>
            <if test="params.userName != null and params.userName != ''">
                and t1.user_name LIKE CONCAT('%', #{params.userName}, '%')
            </if>
            <if test="params.paymentMethod != null">
                and t1.payment_method = #{params.paymentMethod}
            </if>
            <if test="params.notPaymentMethod != null">
                and t1.payment_method = #{params.notPaymentMethod}
            </if>
            <if test="params.platform != null">
                and t1.platform = #{params.platform}
            </if>
            <if test="params.userIds != null and params.userIds.size() > 0">
                and t1.user_id in
                <foreach item="itemId" collection="params.userIds" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="params.billId != null">
                and exists(select 1 from trade_bill_order tbo where tbo.deleted = 0 and tbo.order_id = t1.id and tbo.bill_id = #{params.billId})
            </if>
            <if test="params.notBillId != null">
                and not exists(select 1 from trade_bill_order tbo where tbo.deleted = 0 and tbo.order_id = t1.id and tbo.bill_id = #{params.notBillId})
            </if>
            <if test="params.receiverName != null and params.receiverName != ''">
                and t1.receiver_name like concat(#{params.receiverName}, '%')
            </if>
            <if test="params.receiverMobile != null and params.receiverMobile != ''">
                and t1.receiver_mobile like concat(#{params.receiverMobile}, '%')
            </if>
            <if test="params.offlineSettlement != null">
                and t1.offline_settlement = #{params.offlineSettlement}
            </if>
            <if test="params.status != null">
                <choose>
                    <when test="params.status == 6">
                        and t1.status not in (9) and exists (select 1 from trade_order_item toi2 where toi2.deleted = 0 and toi2.order_id = t1.id and toi2.after_sale_status = 1)
                    </when>
                    <when test="params.status == 7">
                        and t1.status not in (9) and exists (select 1 from trade_order_item toi2 where toi2.deleted = 0 and toi2.order_id = t1.id and toi2.after_sale_status = 2)
                    </when>
                    <otherwise>
                        and t1.status = #{params.status}
                    </otherwise>
                </choose>
            </if>
            <if test="params.auditStatusList != null and params.auditStatusList.size() > 0">
                and t1.audit_status in
                <foreach item="itemId" collection="params.auditStatusList" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="params.invoiceStatus != null">
                and t1.invoice_status = #{params.invoiceStatus}
            </if>
            <if test="params.billStatus != null">
                and t1.bill_status = #{params.billStatus}
            </if>
            <if test="params.statusList != null and params.statusList.size() > 0">
                and t1.status in
                <foreach item="itemId" collection="params.statusList" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="params.assetStatus != null">
                and t1.asset_status = #{params.assetStatus}
            </if>
            <if test="params.assetStatusList != null and params.assetStatusList.size() > 0">
                and t1.asset_status in
                <foreach item="itemId" collection="params.assetStatusList" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="params.thirdOrderId != null and params.thirdOrderId != ''">
                and t1.third_order_id = #{params.thirdOrderId}
            </if>
            <if test="params.supplierId != null">
                and t1.supplier_id = #{params.supplierId}
            </if>
            <if test="params.payChannelCode != null and params.payChannelCode != ''">
                and t1.pay_channel_code = #{params.payChannelCode}
            </if>
            <if test="params.ycrhNeed!= null">
                and t1.ycrh_need = #{params.ycrhNeed}
            </if>
            <if test="params.ycrhStatus!= null">
                and t1.ycrh_status = #{params.ycrhStatus}
            </if>
            <if test="params.voucherNo!= null">
                and t1.voucher_no = #{params.voucherNo}
            </if>
            <if test="params.parentType!= null">
                and t1.parent_type = #{params.parentType}
            </if>
            <if test="params.createTime != null and params.createTime.length == 2">
                and t1.create_time between #{params.createTime[0]} and #{params.createTime[1]}
            </if>
            <if test="params.submitTime != null and params.submitTime.length == 2">
                and t1.submit_time between #{params.submitTime[0]} and #{params.submitTime[1]}
            </if>
            <if test="params.confirmTime != null and params.confirmTime.length == 2">
                and t1.confirm_time between #{params.confirmTime[0]} and #{params.confirmTime[1]}
            </if>
            <if test="params.updateTime != null and params.updateTime.length == 2">
                and t1.update_time between #{params.updateTime[0]} and #{params.updateTime[1]}
            </if>
            <if test="params.receiveTime != null and params.receiveTime.length == 2">
                and t1.receive_time between #{params.receiveTime[0]} and #{params.receiveTime[1]}
            </if>
            <if test="params.finishTime != null and params.finishTime.length == 2">
                and t1.finish_time between #{params.finishTime[0]} and #{params.finishTime[1]}
            </if>
            <if test="params.auditCompleteTime != null and params.auditCompleteTime.length == 2">
                and t1.audit_complete_time between #{params.auditCompleteTime[0]} and #{params.auditCompleteTime[1]}
            </if>
            <if test="params.notZero != null">
                <choose>
                    <when test="params.notZero">
                        and t1.order_price > 0 and t1.order_price > t1.refund_price
                    </when>
                    <otherwise>
                        and (t1.order_price = 0 or t1.order_price = t1.refund_price)
                    </otherwise>
                </choose>
            </if>
            <if test="params.skuId != null">
                and exists(select 1 from trade_order_item t2 where t1.id = t2.order_id and t2.deleted = 0 and t2.sku_id = #{params.skuId} )
            </if>
            <if test="params.thirdSkuId != null and params.thirdSkuId != ''">
                and exists(select 1 from trade_order_item t2 where t1.id = t2.order_id and t2.deleted = 0 and t2.sku_inner_id = #{params.thirdSkuId} )
            </if>
            <if test="params.skuCode != null">
                and exists(
                select 1 from trade_order_item t2
                where t1.id = t2.order_id
                and t2.deleted = 0
                and (
                CAST(t2.sku_id AS CHAR) LIKE CONCAT('%', #{params.skuCode}, '%')
                OR t2.sku_inner_id LIKE CONCAT('%', #{params.skuCode}, '%')
                OR t2.sku_name LIKE CONCAT('%', #{params.skuCode}, '%')
                )
                )
            </if>
            <if test="params.skuName != null and params.skuName != ''">
                and exists(select 1 from trade_order_item t2 where t1.id = t2.order_id and t2.deleted = 0 and t2.sku_name LIKE CONCAT('%', #{params.skuName}, '%') )
            </if>
            <if test="params.skuTags != null and params.skuTags.size() > 0">
                and exists(select 1 from trade_order_item_tag t31 where t31.deleted = 0 and  t31.order_id = t1.id
                and t31.product_tag_id in
                <foreach item="itemId" collection="params.skuTags" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
                )
            </if>
            <if test="params.teamOrderNo != null and params.teamOrderNo != ''">
                and exists(select 1 from trade_team_order t32
                where t32.deleted = 0 and t32.no = #{params.teamOrderNo}
                and (t32.user_id = #{params.teamUserId} or t32.team_user_id = #{params.teamUserId})
                and t1.team_order_id = t32.id
                )
            </if>
        </where>
        <if test="params.sortType!= null">
            <choose>
                <when test="params.sortType == 1">
                    order by submit_time desc, id desc
                </when>
                <when test="params.sortType == 2">
                    order by finish_time desc, id desc
                </when>
                <when test="params.sortType == 11">
                    order by submit_time asc, id asc
                </when>
                <when test="params.sortType == 22">
                    order by finish_time asc, id asc
                </when>
                <otherwise>
                    order by submit_time desc, id desc
                </otherwise>
            </choose>
        </if>
    </select>

    <select id="selectPage2" parameterType="cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo.TradeOrderPageReqVO" resultMap="orderFullMap1">
        select
            t1.id,
            t1.no,
            t1.type,
            t1.parent_type,
            t1.parent_order_id,
            t1.parent_no,
            t1.bpm_no,
            t1.user_id,
            t1.user_name,
            t1.supplier_id,
            t1.supplier_type,
            t1.supplier_name,
            t1.status,
            t1.product_count,
            t1.finish_time,
            t1.cancel_time,
            t1.user_remark,
            t1.submit_time,
            t1.payment_method,
            t1.pay_order_id,
            t1.payed,
            t1.pay_time,
            t1.pay_channel_code,
            t1.product_price,
            t1.delivery_price,
            t1.order_price,
            t1.pay_price,
            t1.delivery_status,
            t1.delivery_time,
            t1.receive_time,
            t1.receiver_name,
            t1.receiver_mobile,
            t1.receiver_province_name,
            t1.receiver_city_name,
            t1.receiver_county_name,
            t1.receiver_detail_address,
            t1.after_sale_status,
            t1.refund_price,
            t1.third_order_id,
            t1.parent_third_order_id,
            t1.settle_status,
            t1.audit_status,
            t1.audit_complete_time,
            t1.bill_status,
            t1.bill_submit_time,
            t1.bill_complete_time,
            t1.invoice_status,
            t1.asset_status,
            t1.user_deleted,
            t1.ycrh_need,
            t1.ycrh_status,
            t1.need_purchase,
            t1.offline_settlement,
            t1.purchase_id,
            t1.voucher_no,
            t1.platform,
            t1.split_status,
            t1.split_time,
            t1.create_time,
            t1.update_time,
            t1.team_order_id
        from trade_order t1
        <where>
            t1.deleted = 0
            <if test="params.no != null and params.no != ''">
                and ( t1.no = #{params.no} or t1.parent_no = #{params.no} or t1.third_order_id = #{params.no} )
            </if>
            <if test="params.parentNo != null and params.parentNo != ''">
                and t1.parent_no = #{params.parentNo}
            </if>
            <if test="params.bpmNo != null and params.bpmNo != ''">
                t1.bpm_no = #{params.bpmNo}
            </if>
            <if test="params.orderCode != null and params.orderCode != ''">
                and (
                t1.id = #{params.orderCode}
                or t1.parent_order_id = #{params.orderCode}
                or t1.no LIKE CONCAT('%', #{params.orderCode}, '%')
                or t1.parent_no LIKE CONCAT('%', #{params.orderCode}, '%')
                or t1.third_order_id LIKE CONCAT('%', #{params.orderCode}, '%')
                or t1.parent_third_order_id LIKE CONCAT('%', #{params.orderCode}, '%')
                or t1.bpm_no LIKE CONCAT('%', #{params.orderCode}, '%')
                )
            </if>
            <if test="params.userMobile != null and params.userMobile != ''">
                and exists(select 1 from member_user tmu where t1.user_id = tmu.id and tmu.deleted = 0 and tmu.mobile = #{params.userMobile})
            </if>
            <if test="params.userNickname != null and params.userNickname != ''">
                and exists(select 1 from member_user tmu where t1.user_id = tmu.id and tmu.deleted = 0 and tmu.nickname LIKE CONCAT('%', #{params.userNickname}, '%'))
            </if>
            <if test="params.userId != null">
                and t1.user_id = #{params.userId}
            </if>
            <if test="params.userName != null and params.userName != ''">
                and t1.user_name LIKE CONCAT('%', #{params.userName}, '%')
            </if>
            <if test="params.paymentMethod != null">
                and t1.payment_method = #{params.paymentMethod}
            </if>
            <if test="params.notPaymentMethod != null">
                and t1.payment_method = #{params.notPaymentMethod}
            </if>
            <if test="params.platform != null">
                and t1.platform = #{params.platform}
            </if>
            <if test="params.userIds != null and params.userIds.size() > 0">
                and t1.user_id in
                <foreach item="itemId" collection="params.userIds" open="(" separator="," close=")">
                #{itemId}
                </foreach>
            </if>
            <if test="params.billId != null">
                and exists(select 1 from trade_bill_order tbo where tbo.deleted = 0 and tbo.order_id = t1.id and tbo.bill_id = #{params.billId})
            </if>
            <if test="params.notBillId != null">
                and not exists(select 1 from trade_bill_order tbo where tbo.deleted = 0 and tbo.order_id = t1.id and tbo.bill_id = #{params.notBillId})
            </if>
            <if test="params.receiverName != null and params.receiverName != ''">
                and t1.receiver_name like concat(#{params.receiverName}, '%')
            </if>
            <if test="params.receiverMobile != null and params.receiverMobile != ''">
                and t1.receiver_mobile like concat(#{params.receiverMobile}, '%')
            </if>
            <if test="params.offlineSettlement != null">
                and t1.offline_settlement = #{params.offlineSettlement}
            </if>
            <if test="params.status != null">
                <choose>
                    <when test="params.status == 6">
                        and t1.status not in (9) and exists (select 1 from trade_order_item toi2 where toi2.deleted = 0 and toi2.order_id = t1.id and toi2.after_sale_status = 1)
                    </when>
                    <when test="params.status == 7">
                        and t1.status not in (9) and exists (select 1 from trade_order_item toi2 where toi2.deleted = 0 and toi2.order_id = t1.id and toi2.after_sale_status = 2)
                    </when>
                    <otherwise>
                        and t1.status = #{params.status}
                    </otherwise>
                </choose>
            </if>
            <if test="params.auditStatusList != null and params.auditStatusList.size() > 0">
                and t1.audit_status in
                <foreach item="itemId" collection="params.auditStatusList" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="params.invoiceStatus != null">
                and t1.invoice_status = #{params.invoiceStatus}
            </if>
            <if test="params.billStatus != null">
                and t1.bill_status = #{params.billStatus}
            </if>
            <if test="params.statusList != null and params.statusList.size() > 0">
                and t1.status in
                <foreach item="itemId" collection="params.statusList" open="(" separator="," close=")">
                #{itemId}
                </foreach>
            </if>
            <if test="params.assetStatus != null">
                and t1.asset_status = #{params.assetStatus}
            </if>
            <if test="params.assetStatusList != null and params.assetStatusList.size() > 0">
                and t1.asset_status in
                <foreach item="itemId" collection="params.assetStatusList" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="params.thirdOrderId != null and params.thirdOrderId != ''">
                and t1.third_order_id = #{params.thirdOrderId}
            </if>
            <if test="params.supplierId != null">
                and t1.supplier_id = #{params.supplierId}
            </if>
            <if test="params.payChannelCode != null and params.payChannelCode != ''">
                and t1.pay_channel_code = #{params.payChannelCode}
            </if>
            <if test="params.ycrhNeed!= null">
                and t1.ycrh_need = #{params.ycrhNeed}
            </if>
            <if test="params.ycrhStatus!= null">
                and t1.ycrh_status = #{params.ycrhStatus}
            </if>
            <if test="params.voucherNo!= null">
                and t1.voucher_no = #{params.voucherNo}
            </if>
            <if test="params.parentType!= null">
                and t1.parent_type = #{params.parentType}
            </if>
            <if test="params.createTime != null and params.createTime.length == 2">
                and t1.create_time between #{params.createTime[0]} and #{params.createTime[1]}
            </if>
            <if test="params.submitTime != null and params.submitTime.length == 2">
                and t1.submit_time between #{params.submitTime[0]} and #{params.submitTime[1]}
            </if>
            <if test="params.confirmTime != null and params.confirmTime.length == 2">
                and t1.confirm_time between #{params.confirmTime[0]} and #{params.confirmTime[1]}
            </if>
            <if test="params.updateTime != null and params.updateTime.length == 2">
                and t1.update_time between #{params.updateTime[0]} and #{params.updateTime[1]}
            </if>
            <if test="params.receiveTime != null and params.receiveTime.length == 2">
                and t1.receive_time between #{params.receiveTime[0]} and #{params.receiveTime[1]}
            </if>
            <if test="params.finishTime != null and params.finishTime.length == 2">
                and t1.finish_time between #{params.finishTime[0]} and #{params.finishTime[1]}
            </if>
            <if test="params.auditCompleteTime != null and params.auditCompleteTime.length == 2">
                and t1.audit_complete_time between #{params.auditCompleteTime[0]} and #{params.auditCompleteTime[1]}
            </if>
            <if test="params.notZero != null">
                <choose>
                    <when test="params.notZero">
                        and t1.order_price > 0 and t1.order_price > t1.refund_price
                    </when>
                    <otherwise>
                        and (t1.order_price = 0 or t1.order_price = t1.refund_price)
                    </otherwise>
                </choose>
            </if>
            <if test="params.skuId != null">
                and exists(select 1 from trade_order_item t2 where t1.id = t2.order_id and t2.deleted = 0 and t2.sku_id = #{params.skuId} )
            </if>
            <if test="params.thirdSkuId != null and params.thirdSkuId != ''">
                and exists(select 1 from trade_order_item t2 where t1.id = t2.order_id and t2.deleted = 0 and t2.sku_inner_id = #{params.thirdSkuId} )
            </if>
            <if test="params.skuCode != null">
                and exists(
                    select 1 from trade_order_item t2
                    where t1.id = t2.order_id
                    and t2.deleted = 0
                    and (
                        CAST(t2.sku_id AS CHAR) LIKE CONCAT('%', #{params.skuCode}, '%')
                        OR t2.sku_inner_id LIKE CONCAT('%', #{params.skuCode}, '%')
                        OR t2.sku_name LIKE CONCAT('%', #{params.skuCode}, '%')
                    )
                )
            </if>
            <if test="params.skuName != null and params.skuName != ''">
                and exists(select 1 from trade_order_item t2 where t1.id = t2.order_id and t2.deleted = 0 and t2.sku_name LIKE CONCAT('%', #{params.skuName}, '%') )
            </if>
            <if test="params.skuTags != null and params.skuTags.size() > 0">
                and exists(select 1 from trade_order_item_tag t31 where t31.deleted = 0 and  t31.order_id = t1.id
                    and t31.product_tag_id in
                    <foreach item="itemId" collection="params.skuTags" open="(" separator="," close=")">
                        #{itemId}
                    </foreach>
                )
            </if>
            <if test="params.teamOrderNo != null and params.teamOrderNo != ''">
                and exists(select 1 from trade_team_order t32
                    where t32.deleted = 0 and t32.no = #{params.teamOrderNo}
                    and (t32.user_id = #{params.teamUserId} or t32.team_user_id = #{params.teamUserId})
                    and t1.team_order_id = t32.id
                )
            </if>
        </where>
        <if test="params.sortType!= null">
            <choose>
                <when test="params.sortType == 1">
                    order by submit_time desc, id desc
                </when>
                <when test="params.sortType == 2">
                    order by finish_time desc, id desc
                </when>
                <when test="params.sortType == 11">
                    order by submit_time asc, id asc
                </when>
                <when test="params.sortType == 22">
                    order by finish_time asc, id asc
                </when>
                <otherwise>
                    order by submit_time desc, id desc
                </otherwise>
            </choose>
        </if>
    </select>

    <select id="selectPage3" parameterType="cn.iocoder.yudao.module.mall.trade.controller.app.order.vo.AppTradeOrderPageReqVO" resultMap="orderFullMap1">
        select
        t1.id,
        t1.no,
        t1.bpm_no,
        t1.type,
        t1.parent_type,
        t1.parent_order_id,
        t1.parent_no,
        t1.user_id,
        t1.user_name,
        t1.supplier_id,
        t1.supplier_type,
        t1.supplier_name,
        t1.status,
        t1.product_count,
        t1.finish_time,
        t1.cancel_time,
        t1.user_remark,
        t1.submit_time,
        t1.payment_method,
        t1.pay_order_id,
        t1.payed,
        t1.pay_time,
        t1.pay_channel_code,
        t1.product_price,
        t1.delivery_price,
        t1.order_price,
        t1.pay_price,
        t1.delivery_status,
        t1.delivery_time,
        t1.receive_time,
        t1.receiver_name,
        t1.receiver_mobile,
        t1.receiver_province_name,
        t1.receiver_city_name,
        t1.receiver_county_name,
        t1.receiver_detail_address,
        t1.after_sale_status,
        t1.tip,
        t1.refund_price,
        t1.third_order_id,
        t1.parent_third_order_id,
        t1.settle_status,
        t1.audit_status,
        t1.bill_status,
        t1.bill_submit_time,
        t1.bill_complete_time,
        t1.invoice_status,
        t1.asset_status,
        t1.user_deleted,
        t1.ycrh_need,
        t1.ycrh_status,
        t1.need_purchase,
        t1.offline_settlement,
        t1.purchase_id,
        t1.platform,
        t1.split_status,
        t1.split_time,
        t1.create_time,
        t1.update_time
        from trade_order t1
        <where>
            t1.deleted = 0
            and t1.parent_type = 0
            and t1.user_deleted = 0
            <if test="params.orderNo != null and params.orderNo != ''">
                and (
                t1.no LIKE CONCAT('%', #{params.orderNo}, '%')
                or t1.parent_no LIKE CONCAT('%', #{params.orderNo}, '%')
                or t1.third_order_id LIKE CONCAT('%', #{params.orderNo}, '%')
                or t1.parent_third_order_id LIKE CONCAT('%', #{params.orderNo}, '%')
                or t1.bpm_no LIKE CONCAT('%', #{params.orderNo}, '%')
                )
            </if>
            <if test="params.userId != null">
                and t1.user_id = #{params.userId}
            </if>
            <if test="params.paymentMethod != null">
                and t1.payment_method = #{params.paymentMethod}
            </if>
            <if test="params.auditStatus != null">
                and t1.audit_status = #{params.auditStatus}
            </if>
            <if test="params.needPurchase != null">
                and t1.need_purchase = #{params.needPurchase}
            </if>
            <if test="params.offlineSettlement != null">
                and t1.offline_settlement = #{params.offlineSettlement}
            </if>
            <if test="params.status != null">
                <choose>
                    <when test="params.status == 6">
                        and t1.status not in (9) and exists (select 1 from trade_order_item toi2 where toi2.deleted = 0 and toi2.order_id = t1.id and toi2.after_sale_status = 1)
                    </when>
                    <when test="params.status == 7">
                        and t1.status not in (9) and exists (select 1 from trade_order_item toi2 where toi2.deleted = 0 and toi2.order_id = t1.id and toi2.after_sale_status = 2)
                    </when>
                    <otherwise>
                        and t1.status = #{params.status}
                    </otherwise>
                </choose>
            </if>
            <if test="params.statusList != null and params.statusList.size() > 0">
                and t1.status in
                <foreach item="itemId" collection="params.statusList" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="params.keyword != null and params.keyword != ''">
                and (
                exists (select 1 from trade_order_item t2 where t1.id = t2.order_id and t2.deleted = 0 and
                (t2.sku_id = #{params.keyword} or t2.sku_name like concat('%', #{params.keyword},'%') )
                )
                or ( t1.no like concat('%', #{params.keyword},'%')  )
                )
            </if>
            <if test="params.teamOrderNo != null and params.teamOrderNo != ''">
                and exists(select 1 from trade_team_order t32
                where t32.deleted = 0 and t32.no = #{params.teamOrderNo}
                and (t32.user_id = #{params.teamUserId} or t32.team_user_id = #{params.teamUserId})
                and t1.team_order_id = t32.id
                )
            </if>
        </where>
        order by submit_time desc, id desc
    </select>

    <select id="selectAfterSalePage" parameterType="cn.iocoder.yudao.module.mall.trade.controller.app.order.vo.AppTradeOrderPageReqVO" resultMap="orderFullMap1">
        select
        t1.id,
        t1.no,
        t1.type,
        t1.parent_type,
        t1.parent_order_id,
        t1.parent_no,
        t1.user_id,
        t1.user_name,
        t1.supplier_id,
        t1.supplier_type,
        t1.supplier_name,
        t1.status,
        t1.product_count,
        t1.finish_time,
        t1.cancel_time,
        t1.user_remark,
        t1.submit_time,
        t1.payment_method,
        t1.pay_order_id,
        t1.payed,
        t1.pay_time,
        t1.product_price,
        t1.delivery_price,
        t1.order_price,
        t1.pay_price,
        t1.delivery_status,
        t1.delivery_time,
        t1.receiver_name,
        t1.after_sale_status,
        t1.third_order_id,
        t1.parent_third_order_id,
        t1.audit_status,
        t1.invoice_status,
        t1.asset_status,
        t1.need_purchase,
        t1.offline_settlement,
        t1.purchase_id,
        t1.platform,
        t1.create_time,
        t1.update_time
        from trade_order t1
        <where>
            t1.deleted = 0
            and t1.status not in (9)
            and t1.user_deleted != 1
            <if test="params.userId!= null">
                and t1.user_id = #{params.userId}
            </if>
            <if test="params.teamOrderNo != null and params.teamOrderNo != ''">
                and exists(select 1 from trade_team_order t32
                where t32.deleted = 0 and t32.no = #{params.teamOrderNo}
                and (t32.user_id = #{params.teamUserId} or t32.team_user_id = #{params.teamUserId})
                and t1.team_order_id = t32.id
                )
            </if>
            and exists(
                select 1 from trade_order_item t2 where t2.deleted = 0 and t2.order_id = t1.id and t2.after_sale_status = #{params.status}
            )
        </where>
        order by submit_time desc, id desc
    </select>

    <select id="getOrderAuditStatusByUser" parameterType="java.util.Map" resultType="cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo.TradeOrderStatusResult">
        SELECT
            t1.audit_status as status,
           COUNT(*) as count
        FROM trade_order t1
        <where>
            t1.parent_type = 0
            and t1.deleted = 0
            and t1.user_deleted = 0
            and t1.status = 1
            and t1.need_purchase = 1
            <if test="params.userId!= null">
                and t1.user_id = #{params.userId}
            </if>
        </where>
        GROUP BY t1.audit_status
    </select>

    <select id="selectAfterSaleStats" parameterType="java.util.Map" resultType="cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo.TradeOrderStatusResult">
        select case t2.after_sale_status
                 when 1 then 6
                 when 2 then 7
               end as status,
               count(distinct t1.id) as count
        from trade_order t1 left join trade_order_item t2 on t1.id = t2.order_id
        <where>
        t1.deleted = 0
        and t2.deleted = 0
        and t1.status not in (9)
        <if test="params.userId!= null">
            and t1.user_id = #{params.userId}
        </if>
        and t2.after_sale_status in (1,2)
        </where>
        group by t2.after_sale_status
    </select>

    <select id="selectDeletedChildIds" parameterType="java.util.List" resultType="java.lang.Long">
        select t1.id from trade_order t1 where
            t1.deleted = 1
            and t1.parent_order_id in
            <foreach item="itemId" collection="parentIds" open="(" separator="," close=")">
                #{itemId}
            </foreach>
    </select>

</mapper>