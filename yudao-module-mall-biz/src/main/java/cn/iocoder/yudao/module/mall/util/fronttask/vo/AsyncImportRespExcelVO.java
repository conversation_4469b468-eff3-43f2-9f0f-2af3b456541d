package cn.iocoder.yudao.module.mall.util.fronttask.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 异步导入任务结果信息封闭类
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AsyncImportRespExcelVO {

    public static final String TYPE_CREATE = "1";
    public static final String TYPE_UPDATE = "2";
    public static final String RESULT_SUCCESS = "1";
    public static final String RESULT_FAIL = "2";

    @Excel(name = "唯一标识", width = 40)
    private String id;

    @Excel(name = "处理类型", width = 15, replace = {"新增_1", "更新_2"})
    private String type;

    @Excel(name = "导入结果", width = 15, replace = {"成功_1", "失败_2"})
    private String result;

    @Excel(name = "描述", width = 40)
    private String msg;

}
