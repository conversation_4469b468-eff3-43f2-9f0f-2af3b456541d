package cn.iocoder.yudao.module.mall.trade.service.aftersale;

import cn.iocoder.yudao.module.mall.vop.service.messagehandle.messagevo.OrderAfterSellComponentVO;
import cn.iocoder.yudao.module.mall.vop.service.messagehandle.messagevo.OrderApplyRefundCompleteVO;

/**
 * 售后问题
 */
public interface VopAfterSalesBridgeService {

    boolean afterSaleComponentOrder4Vop(OrderAfterSellComponentVO afterSellComponentVO);

    boolean handleApplyRefundComplete(OrderApplyRefundCompleteVO applyRefundCompleteVO);

}
