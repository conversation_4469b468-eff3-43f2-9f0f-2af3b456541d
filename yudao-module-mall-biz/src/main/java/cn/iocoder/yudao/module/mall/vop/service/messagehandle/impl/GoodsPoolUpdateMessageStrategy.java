package cn.iocoder.yudao.module.mall.vop.service.messagehandle.impl;

import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.supplier.SupplierDO;
import cn.iocoder.yudao.module.mall.basis.service.supplier.SupplierService;
import cn.iocoder.yudao.module.mall.mq.producer.product.ProductSkuProducer;
import cn.iocoder.yudao.module.mall.product.service.sku.ProductSkuService;
import cn.iocoder.yudao.module.mall.product.service.spu.ProductSpuService;
import cn.iocoder.yudao.module.mall.product.service.vopgoods.VopGoodsBridgeService;
import cn.iocoder.yudao.module.mall.vop.apiserver.VopGoodsService;
import cn.iocoder.yudao.module.mall.vop.dal.dataobject.VopMessageDO;
import cn.iocoder.yudao.module.mall.vop.service.VopMessageService;
import cn.iocoder.yudao.module.mall.vop.service.VopSkuPoolService;
import cn.iocoder.yudao.module.mall.vop.service.messagehandle.VopMessageTypeEnum;
import cn.iocoder.yudao.module.mall.vop.service.messagehandle.MessageHandleStrategy;
import cn.iocoder.yudao.module.mall.vop.service.messagehandle.messagevo.GoodsPoolUpdateVO;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("goodsPoolUpdateMessageStrategy")
@Slf4j
public class GoodsPoolUpdateMessageStrategy implements MessageHandleStrategy {

    @Resource
    private VopGoodsBridgeService vopGoodsBridgeService;
    @Resource
    private ProductSpuService productSpuService;
    @Resource
    private ProductSkuService productSkuService;
    @Resource
    private SupplierService supplierService;
    @Resource
    private ProductSkuProducer productSkuProducer;
    @Resource
    private VopSkuPoolService vopSkuPoolService;
    @Resource
    private VopGoodsService vopGoodsService;
    @Resource
    private VopMessageService vopMessageService;

    @Override
    public VopMessageTypeEnum getType() {
        return VopMessageTypeEnum.GOODS_POOL_UPDATE_MESSAGE;
    }

    /** 
    * @Description: 商品池添加、删除消息
    * @Param: [messageDO]
    * @return: java.lang.Boolean
    * @Author: lujun
    * @Date: 2023/12/7 17:21
    */
    @Override
    public Boolean handle(VopMessageDO messageDO) {
        // 商品池添加、删除消息, poolType: p_skupool 用户的私有商品池； cate_pool 分类商品池 recommend 主推商品池；hot_sale 热销商品池； p_custom_skupool 用户的私有定制商品池
        GoodsPoolUpdateVO goodsPoolUpdateVO = JsonUtils.parseObject2(messageDO.getContent(), GoodsPoolUpdateVO.class);

        String keyword = String.valueOf(goodsPoolUpdateVO.getPage_num());
        LambdaUpdateWrapper<VopMessageDO> updateWrapper = new LambdaUpdateWrapper<VopMessageDO>()
                .eq(VopMessageDO::getMessageId, messageDO.getMessageId())
                .set(VopMessageDO::getKeyword, keyword);
        vopMessageService.update(updateWrapper);

        SupplierDO supplier = supplierService.getSupplierJD();
        if(supplier == null) {
            log.info("租户VOP未初始化, 忽略处理, poolType:{}", goodsPoolUpdateVO.getPoolType());
            return false;
        }

        // 商品池删除，同时会收到商品池内商品移除消息，存在重复处理情况
        if(goodsPoolUpdateVO.getState() == 2){
            log.info("商品池删除消息，忽略不处理");
//            List<String> skuIdList = vopSkuPoolService.getSkuIdsByPoolId(String.valueOf(goodsPoolUpdateVO.getPage_num()));
//            for (String skuId: skuIdList) {
//                ProductSkuDO productSkuDO = productSkuService.getSkuByInnerIdAndSupplierId(skuId, supplier.getId());
//                if(productSkuDO != null){
//                    List<ProductSkuDO> productSkuDOS = productSkuService.getSkuListBySpuId(productSkuDO.getSpuId());
//                    if(productSkuDOS.size() <= 1){
//                        productSpuService.disableSpu(productSkuDO.getSpuId());
//                    }
//                    else {
//                        productSkuDO.setStatus(ProductSpuStatusEnum.DISABLE.getStatus());
//                        productSkuService.saveOrUpdate(productSkuDO);
//                        productSkuProducer.sendProductSkuIndexSync(Arrays.asList(productSkuDO.getId()));
//                    }
//                }
//            }
        }
        //商品池添加
        else if(goodsPoolUpdateVO.getState() == 1) {
            log.info("商品池添加消息，忽略不处理");
//            List<Long> skuIdList = vopGoodsBridgeService.getSkuIdListByPoolId(String.valueOf(goodsPoolUpdateVO.getPage_num()));
//            log.info("商品池待添加商品数量:{}", skuIdList.size());
//            String poolName = vopGoodsService.getPoolInfo(String.valueOf(goodsPoolUpdateVO.getPage_num()));
//            productSkuProducer.sendVopProductFetch(poolName, skuIdList.toArray(new Long[0]));
//            for (Long skuId: skuIdList) {
//                productSkuService.updateStatusByInnerId(String.valueOf(skuId), ProductSpuStatusEnum.ENABLE.getStatus(), supplier.getId());
//            }
//            List<String> skuIds = skuIdList.stream().map(String::valueOf).collect(Collectors.toList());
//            vopSkuPoolService.batchSaveSkuPool(skuIds, String.valueOf(goodsPoolUpdateVO.getPage_num()));
        }
        else {
            log.error("商品池内商品添加、删除消息, poolType:{}, page_num:{}, 错误的类型state:{}",
                    goodsPoolUpdateVO.getPoolType(), goodsPoolUpdateVO.getPage_num(), goodsPoolUpdateVO.getState());
            return false;
        }

        return true;
    }
}
