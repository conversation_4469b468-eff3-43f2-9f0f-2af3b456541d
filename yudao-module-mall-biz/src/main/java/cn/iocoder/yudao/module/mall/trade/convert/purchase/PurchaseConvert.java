package cn.iocoder.yudao.module.mall.trade.convert.purchase;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.external.hrms.dto.ExtMemberDTO;
import cn.iocoder.yudao.module.mall.external.ycrh.dto.YgInfoRespDTO;
import cn.iocoder.yudao.module.mall.member.dal.dataobject.user.MemberUserDO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.purchase.vo.PurchaseDetailRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.purchase.vo.PurchasePageItemRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.purchase.vo.PurchaseRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.order.vo.AppPurchaseRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.purchase.vo.AppHrmsUserRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.purchase.vo.AppPurchaseDetailRespVO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.purchase.PurchaseDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 采购 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface PurchaseConvert {

    PurchaseConvert INSTANCE = Mappers.getMapper(PurchaseConvert.class);

    @Mapping(source = "id", target = "purchaseId")
    PurchaseRespVO convert(PurchaseDO bean);

    PurchaseDetailRespVO convert2PurchaseDetailRespVO(PurchaseDO bean);

    AppPurchaseDetailRespVO convert2AppPurchaseDetailRespVO(PurchaseDO bean);

    @Mapping(source = "id", target = "purchaseId")
    AppPurchaseRespVO convert2(PurchaseDO bean);

    @Mappings({
            @Mapping(source = "ygNo", target = "userNo"),
            @Mapping(source = "ygName", target = "userName"),
            @Mapping(source = "departmentNo", target = "deptNo"),
            @Mapping(source = "departmentName", target = "deptName"),
            @Mapping(source = "ygTypeName", target = "userType"),
    })
    AppHrmsUserRespVO convert(YgInfoRespDTO bean);

    List<AppHrmsUserRespVO> convertList02(List<YgInfoRespDTO> list);

    @Mappings({
            @Mapping(source = "userNo", target = "userNo"),
            @Mapping(source = "name", target = "userName"),
            @Mapping(source = "deptCode", target = "deptNo"),
            @Mapping(source = "deptName", target = "deptName"),
    })
    AppHrmsUserRespVO convert(ExtMemberDTO bean);

    List<AppHrmsUserRespVO> convertList03(List<ExtMemberDTO> list);

    default AppHrmsUserRespVO convert04(MemberUserDO bean) {
        AppHrmsUserRespVO respVO = new AppHrmsUserRespVO();
        respVO.setUserName(bean.getNameOrNickname());
        respVO.setUserNo(bean.getUserNo());
        respVO.setDeptNo(bean.getDeptCode());
        respVO.setDeptName(bean.getDeptName());
        respVO.setUserType(bean.getUserType());

        return respVO;
    }

    List<AppHrmsUserRespVO> convertList04(List<MemberUserDO> list);

    PageResult<PurchasePageItemRespVO> convertPage(PageResult<PurchaseDO> pageResult);
}
