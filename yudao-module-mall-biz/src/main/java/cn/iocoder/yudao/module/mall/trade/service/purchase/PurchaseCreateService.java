package cn.iocoder.yudao.module.mall.trade.service.purchase;

import cn.iocoder.yudao.module.mall.trade.controller.app.purchase.vo.PurchaseSubmitReqVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.purchase.vo.PurchaseValidateReqVO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.purchase.PurchaseDO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 采购-提交 Service 接口
 *
 * <AUTHOR>
 */
public interface PurchaseCreateService extends IService<PurchaseDO> {

    /**
     * 创建采购单
     * @param reqVO
     * @return
     */
    Long createPurchase(PurchaseSubmitReqVO reqVO);

    /**
     * 校验采购单
     * @param reqVO
     */
    void validatePurchase(PurchaseValidateReqVO reqVO);

    /**
     * 补偿处理推送订单到业财融合
     * @param purchaseId
     */
    boolean pushOrder2YcrhManual(Long purchaseId);

    /**
     * 推审批流信息，主要用在补偿场景
     * @param purchaseId
     * @return
     */
    boolean pushBpmInfo(Long purchaseId);

}
