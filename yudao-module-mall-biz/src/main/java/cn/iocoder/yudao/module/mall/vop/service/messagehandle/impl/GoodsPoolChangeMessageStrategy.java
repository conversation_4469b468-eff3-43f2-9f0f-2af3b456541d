package cn.iocoder.yudao.module.mall.vop.service.messagehandle.impl;

import cn.hutool.core.util.NumberUtil;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.supplier.SupplierDO;
import cn.iocoder.yudao.module.mall.basis.service.supplier.SupplierService;
import cn.iocoder.yudao.module.mall.mq.producer.product.ProductSkuProducer;
import cn.iocoder.yudao.module.mall.product.service.sku.ProductSkuService;
import cn.iocoder.yudao.module.mall.product.service.spu.ProductSpuService;
import cn.iocoder.yudao.module.mall.product.service.vopgoods.VopGoodsBridgeService;
import cn.iocoder.yudao.module.mall.trade.service.cart.TradeCartService;
import cn.iocoder.yudao.module.mall.trade.service.cart.TradeCollectService;
import cn.iocoder.yudao.module.mall.trade.service.order.TradeOrderService;
import cn.iocoder.yudao.module.mall.vop.apiserver.VopGoodsService;
import cn.iocoder.yudao.module.mall.vop.dal.dataobject.VopMessageDO;
import cn.iocoder.yudao.module.mall.vop.service.VopConfigService;
import cn.iocoder.yudao.module.mall.vop.service.VopMessageService;
import cn.iocoder.yudao.module.mall.vop.service.VopSkuPoolService;
import cn.iocoder.yudao.module.mall.vop.service.messagehandle.VopMessageTypeEnum;
import cn.iocoder.yudao.module.mall.vop.service.messagehandle.MessageHandleStrategy;
import cn.iocoder.yudao.module.mall.vop.service.messagehandle.messagevo.GoodsPoolChangeVO;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service("goodsPoolChangeMessageStrategy")
@Slf4j
public class GoodsPoolChangeMessageStrategy implements MessageHandleStrategy {

    @Resource
    private ProductSpuService productSpuService;
    @Resource
    private ProductSkuService productSkuService;
    @Resource
    private SupplierService supplierService;
    @Resource
    private ProductSkuProducer productSkuProducer;
    @Resource
    private VopSkuPoolService vopSkuPoolService;
    @Resource
    private VopGoodsService vopGoodsService;
    @Resource
    private TradeCartService tradeCartService;
    @Resource
    private TradeCollectService tradeCollectService;
    @Resource
    private TradeOrderService tradeOrderService;
    @Resource
    private VopMessageService vopMessageService;
    @Resource
    private VopConfigService vopConfigService;
    @Lazy
    @Resource
    private VopGoodsBridgeService vopGoodsBridgeService;

    @Override
    public VopMessageTypeEnum getType() {
        return VopMessageTypeEnum.GOODS_POOL_CHANGE_MESSAGE;
    }

    /** 
    * @Description: 商品池内商品添加、删除消息
    * @Param: [messageDO]
    * @return: java.lang.Boolean
    * @Author: lujun
    * @Date: 2023/12/7 17:21
    */
    @Override
    public Boolean handle(VopMessageDO messageDO) {
        // 商品池内商品添加、删除消息，page_num:商品池编号；state:1代表添加；state:2代表删除
        GoodsPoolChangeVO goodsPoolChangeVO = JsonUtils.parseObject2(messageDO.getContent(), GoodsPoolChangeVO.class);

        String keyword = String.valueOf(goodsPoolChangeVO.getSkuId());
        LambdaUpdateWrapper<VopMessageDO> updateWrapper = new LambdaUpdateWrapper<VopMessageDO>()
                .eq(VopMessageDO::getMessageId, messageDO.getMessageId())
                .set(VopMessageDO::getKeyword, keyword);
        vopMessageService.update(updateWrapper);

        SupplierDO supplier = supplierService.getSupplierJD();
        if(supplier == null) {
            log.info("租户VOP未初始化, 忽略处理, skuId:{}", goodsPoolChangeVO.getSkuId());
            return false;
        }
        if(!NumberUtil.isLong(goodsPoolChangeVO.getSkuId())) {
            log.info("vop消息，忽略处理无效sku：{}", goodsPoolChangeVO.getSkuId());
            return false;
        }
        Long jdSkuId = Long.valueOf(goodsPoolChangeVO.getSkuId());
        // 商品删除
        if(goodsPoolChangeVO.getState() == 2){
            vopGoodsBridgeService.deleteGoods(jdSkuId, supplier.getId());
        }
        //商品添加
        else if(goodsPoolChangeVO.getState() == 1) {
            productSkuService.updateStatusByInnerId(String.valueOf(goodsPoolChangeVO.getSkuId()), 1, supplier.getId());
            String poolName = vopGoodsService.getPoolInfo(goodsPoolChangeVO.getPage_num());
            productSkuProducer.sendVopProductFetch(poolName, jdSkuId);
            List<String> skuIds = new ArrayList<>();
            skuIds.add(String.valueOf(goodsPoolChangeVO.getSkuId()));
            if(!vopConfigService.getVopConfig().getFullPoolSwitch()){
                vopSkuPoolService.batchSaveSkuPool(skuIds, goodsPoolChangeVO.getPage_num(), poolName);
            }
        }
        else {
            log.error("商品池内商品添加、删除消息, skuId:{}, page_num:{}, 错误的类型state:{}", goodsPoolChangeVO.getSkuId(), goodsPoolChangeVO.getPage_num(), goodsPoolChangeVO.getState());
            return false;
        }

        return true;
    }
}
