package cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 交易统计明细的分页项 Response VO")
@Data
public class StatsSaleTotalRespVO {

    /**
     * 总金额
     */
    private BigDecimal totalPrice;
    /**
     * 净收入金额
     */
    private BigDecimal netTotalPrice;
    /**
     * 取消订单总金额
     */
    private BigDecimal cancelTotalPrice;
    /**
     * 总订单数
     */
    private Long totalCount;
    /**
     * 售后总金额
     */
    private BigDecimal afterSaleTotalPrice;
    /**
     * 售后总订单数
     */
    private BigDecimal afterSaleTotalCount;


}
