package cn.iocoder.yudao.module.mall.basis.convert.stats;

import cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.*;
import cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.OrderSaleDaySummaryRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.OrderSaleMonthSummaryRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.StatsSaleTotalRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.StatsSupplierSaleRespVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 销量统计 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface StatsOrderConvert {

    StatsOrderConvert INSTANCE = Mappers.getMapper(StatsOrderConvert.class);


    @Mappings(value = {
            @Mapping(source = "statsDateStr", target = "timeString"),
            @Mapping(source = "totalCount", target = "count"),
            @Mapping(source = "netTotalPrice", target = "amount")
    })
    OrderSaleDaySummaryRespVO convertDailySummary(StatsOrderSaleVO statsOrderSaleVO);

    @Mappings(value = {
            @Mapping(source = "statsMonth", target = "month"),
            @Mapping(source = "totalCount", target = "count"),
            @Mapping(source = "netTotalPrice", target = "amount")
    })
    OrderSaleMonthSummaryRespVO convertMonthlySummary(StatsOrderSaleVO statsOrderSaleVO);

    StatsSaleTotalRespVO convertTotalSale(StatsOrderSaleVO statsOrderSaleVO);

    StatsSupplierSaleRespVO convertSupplierSale(StatsOrderSaleVO statsOrderSaleVO);

    List<StatsSupplierSaleRespVO> convertSupplierSaleList(List<StatsOrderSaleVO> statsOrderSaleVO);

    List<OrderSaleDaySummaryRespVO> convertDailySummaryList(List<StatsOrderSaleVO> statsOrderSaleVOList);

    List<OrderSaleMonthSummaryRespVO> convertMonthlySummaryList(List<StatsOrderSaleVO> statsOrderSaleVOList);

    List<StatsSaleDateExportVO> convertExportList01(List<StatsSaleDateRespVO> list);

    List<StatsSaleYearMonthExportVO> convertExportList02(List<StatsSaleDateRespVO> list);

    List<StatsSaleSkuExportVO> convertExportList03(List<StatsSaleSkuRespVO> list);


}
