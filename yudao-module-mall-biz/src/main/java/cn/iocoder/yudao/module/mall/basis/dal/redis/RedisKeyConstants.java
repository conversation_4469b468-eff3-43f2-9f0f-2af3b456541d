package cn.iocoder.yudao.module.mall.basis.dal.redis;

/**
 * System Redis Key 枚举类
 *
 * <AUTHOR>
 */
public interface RedisKeyConstants {

    String MALL_VOP_ACCESS_TOKEN = "mall_vop_access_token";
    String MALL_BASIS_CONFIG = "mall_basis_config_v3";
    String MALL_BASIS_CONFIG_ALL = "mall_basis_config_all";
    String MALL_BASIS_DOMAIN = "mall_basis_domain";
    String MALL_BPM_CONFIG = "mall_bpm_config_v4";
    String MALL_INVOICE_CONFIG = "mall_invoice_config_v4";
    String MALL_SSO_CONFIG = "mall_sso_config_v4";
    String MALL_YCRH_CONFIG = "mall_ycrh_config_v4";
    String MALL_OPEN_APP = "mall_open_app_v4";
    String MALL_OPEN_PERMISSION = "mall_open_permission_v4";
    String MALL_SUPPLIER = "mall_supplier";
    String MALL_SUPPLIER_ACCOUNT = "mall_supplier_account_v4";
    String MALL_PRODUCT_CATEGORY = "mall_product_category";
    String MALL_PRODUCT_BRAND = "mall_product_brand";
    String MALL_PRODUCT_HOT_SEARCH = "mall_product_hot_search";
    String MALL_HOME_CONFIG = "mall_home_config_v4";
    String MALL_GOODS_POOL_SYNC = "mall_goods_pool_sync";
    String MALL_VOP_PRODUCT_SYNC_PROGRESS = "mall_vop_product_sync_progress";
    String MALL_ECONOMIC_CLASSIFICATION = "mall_economic_classification_v4";
    String MALL_ASSETS_CONFIG = "mall_assets_config_v4";
    String MALL_TRADE_CART = "mall_trade_cart";
    String MALL_AREA = "mall_area_v2";
    String MALL_PRODUCT_DETAIL = "mall_product_detail_v2";
    String MALL_PRODUCT_BROTHER_SKUS = "mall_product_brother_skus";
    String MALL_PRODUCT_IMG = "mall_product_img";
    String MALL_PRODUCT_SUGGEST = "mall_product_suggest";
    String MALL_USER_DEFAULT_ADDRESS = "mall_user_default_addr";
    String MALL_PROJECT_RULE = "mall_project_rule:%d";
    String MALL_PROJECT_RULE_CATEGORY = "mall_project_rule_category:%d:%d";
    String MALL_PROJECT_WHITE_SKU = "mall_project_white_sku:%d";
    String MALL_HRMS_CONFIG = "mall_hrms_config_v4";
    String MALL_ADV_POSITION = "mall_adv_position_v4";
}
