package cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo;

import cn.hutool.core.date.DateUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class StatsOrderSaleVO {

    private LocalDateTime statsDate;
    private String statsMonth;
    private Long supplierId;
    private String supplierName;
    private Long totalCount;
    private BigDecimal totalPrice;
    private BigDecimal deliveryPrice;
    private BigDecimal skuPrice;
    private BigDecimal afterSaleTotalPrice;
    private Long afterSaleTotalCount;
    private BigDecimal netTotalPrice;

    public String getStatsDateStr() {
        if(statsDate != null) {
            return DateUtil.format(statsDate, "yyyy-MM-dd");
        }

        return "";
    }

}
