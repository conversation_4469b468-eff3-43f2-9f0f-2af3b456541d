package cn.iocoder.yudao.module.mall.product.service.spu;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.supplier.SupplierDO;
import cn.iocoder.yudao.module.mall.basis.service.supplier.SupplierService;
import cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.ProductSkuCreateOrUpdateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.ProductSkuRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.spec.vo.ProductSpecValueRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.spu.vo.*;
import cn.iocoder.yudao.module.mall.product.convert.spec.ProductSpecConvert;
import cn.iocoder.yudao.module.mall.product.convert.spu.ProductSpuConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.productoperatelog.ProductOperateLogDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.spec.ProductSpuSpecDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.spu.ProductSpuDO;
import cn.iocoder.yudao.module.mall.product.dal.mysql.spu.ProductSpuMapper;
import cn.iocoder.yudao.module.mall.product.enums.spu.ProductOperateTypeEnum;
import cn.iocoder.yudao.module.mall.product.enums.spu.ProductSpuStatusEnum;
import cn.iocoder.yudao.module.mall.product.service.brand.ProductBrandService;
import cn.iocoder.yudao.module.mall.product.service.category.ProductCategoryService;
import cn.iocoder.yudao.module.mall.product.service.productoperatelog.ProductOperateLogService;
import cn.iocoder.yudao.module.mall.product.service.sku.ProductSkuService;
import cn.iocoder.yudao.module.mall.product.service.spec.ProductSpuSpecService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserName;
import static cn.iocoder.yudao.module.mall.product.enums.ErrorCodeConstants.CATEGORY_NOT_EXISTS;
import static cn.iocoder.yudao.module.mall.product.enums.ErrorCodeConstants.SPU_NOT_EXISTS;

/**
 * 商品 SPU Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProductSpuServiceImpl extends ServiceImpl<ProductSpuMapper, ProductSpuDO> implements ProductSpuService {

    @Resource
    private ProductSpuMapper productSpuMapper;
    /**
     * 循环依赖，避免报错
     */
    @Resource
    @Lazy
    private ProductSkuService productSkuService;
    @Resource
    private ProductBrandService brandService;
    @Resource
    private ProductCategoryService categoryService;
    @Resource
    private SupplierService supplierService;
    @Resource
    private ProductSpuSpecService spuSpecService;
    @Resource
    private ProductOperateLogService productOperateLogService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createSpu(ProductSpuCreateReqVO createReqVO) {
        // 校验分类
        validateCategory(createReqVO.getFullCategoryId());
        // 校验供应商
        supplierService.validateSupplier(createReqVO.getSupplierId());
        // 校验品牌
        brandService.validateProductBrand(createReqVO.getBrandId());
        // 校验SKU
        List<ProductSkuCreateOrUpdateReqVO> skuSaveReqList = createReqVO.getSkus();
        productSkuService.validateSkuList(skuSaveReqList, createReqVO.getSpecType());

        // 插入 SPU
        ProductSpuDO spu = ProductSpuConvert.INSTANCE.convert(createReqVO);

        SupplierDO supplierDO = supplierService.getSupplier(createReqVO.getSupplierId());
        spu.setSupplierType(supplierDO.getType());

        // 生成内部ID
        spu.setSpuInnerId(generateMallInnerId());

        productSpuMapper.insert(spu);
        //添加操作日志
        productOperateLogService.saveCreateOperateSpuLog(spu);

        // 处理 SPU 属性
        spuSpecService.handleSpuSpec(createReqVO.getSpuSpecValueList(), spu.getId());

        // 插入 SKU
        skuSaveReqList.forEach(item -> {
            item.setSupplierName(createReqVO.getSupplierName());
            item.setSupplierType(supplierDO.getType());
            item.setSkuInnerId(generateMallInnerId());
        });
        //校验商品上架数以及商品总数
        productSkuService.createSkuList(spu, skuSaveReqList);
        // 返回
        return spu.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSpu(ProductSpuUpdateReqVO updateReqVO) {
        // 校验 SPU 是否存在
        validateSpuExists(updateReqVO.getId());
        // 校验分类
        validateCategory(updateReqVO.getFullCategoryId());
        // 校验供应商
        supplierService.validateSupplier(updateReqVO.getSupplierId());
        // 校验品牌
        brandService.validateProductBrand(updateReqVO.getBrandId());
        // 校验SKU
        List<ProductSkuCreateOrUpdateReqVO> skuSaveReqList = updateReqVO.getSkus();
        productSkuService.validateSkuList(skuSaveReqList, updateReqVO.getSpecType());
        // 校验规则
        for(ProductSkuCreateOrUpdateReqVO sku : updateReqVO.getSkus()) {
            productSkuService.validProductRule(sku.getSkuInnerId(), updateReqVO.getFullCategoryId(), sku.getSalePrice(),
                    Arrays.asList(updateReqVO.getSpuName(), sku.getSpuName(), sku.getSkuName()));
        }

        // 更新 SPU
        ProductSpuDO updateObj = ProductSpuConvert.INSTANCE.convert(updateReqVO);
        productSpuMapper.updateById(updateObj);
        //productOperateLogService.saveUpdateOperateSpuLog(productSpuDO, updateObj);

        // 处理 SPU 属性
        spuSpecService.handleSpuSpec(updateReqVO.getSpuSpecValueList(), updateObj.getId());

        // 分离需要更新和新增的SKU
        Map<Boolean, List<ProductSkuCreateOrUpdateReqVO>> partitionedSkus = updateReqVO.getSkus().stream()
                .collect(Collectors.partitioningBy(sku -> sku.getId() == null));

        List<ProductSkuCreateOrUpdateReqVO> updateSkuList = partitionedSkus.get(false);
        List<ProductSkuCreateOrUpdateReqVO> newSkuList = partitionedSkus.get(true);

        // 设置供应商名称
        updateReqVO.getSkus().forEach(sku -> sku.setSupplierName(updateReqVO.getSupplierName()));

        // 批量更新SKU
        if (CollUtil.isNotEmpty(updateSkuList)) {
            productSkuService.updateSkuList(updateObj, updateSkuList);
        }

        // 批量新增SKU
        if (CollUtil.isNotEmpty(newSkuList)) {
            newSkuList.forEach(sku -> sku.setSkuInnerId(generateMallInnerId()));
            productSkuService.createSkuList(updateObj, newSkuList);
        }
    }

    @Override
    public void saveOrUpdateBatchV2(List<ProductSpuDO> list) {
        List<ProductSpuDO> insertList = list.stream().filter(item -> item.getId() == null).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(insertList)) {
            productSpuMapper.insertBatch(insertList);
        }
        List<ProductSpuDO> updateList = list.stream().filter(item -> item.getId() != null).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(updateList)) {
            updateList.forEach(item -> {
                productSpuMapper.update(item, Wrappers.lambdaQuery(ProductSpuDO.class)
                        .eq(ProductSpuDO::getId, item.getId())
                        .eq(ProductSpuDO::getSupplierId, item.getSupplierId()));
            });
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSpuStatus(@Valid ProductSpuStatusUpdateReqVO statusUpdateReqVO) {
        // 校验 SPU 是否存在
        ProductSpuDO productSpuDO = validateSpuExists(statusUpdateReqVO.getId());

        // 更新 SPU
        ProductSpuDO updateObj = ProductSpuConvert.INSTANCE.convert(statusUpdateReqVO);
        productSpuMapper.updateById(updateObj);

        // 更新SKU
        productSkuService.updateSkuStatusBySpu(statusUpdateReqVO.getId(), statusUpdateReqVO.getStatus());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSpuShowStatus(List<ProductSpuShowStatusUpdateReqVO> showStatusUpdateReqVOS) {
        // 更新 SPU
        List<ProductSpuDO> productSpuDOS = ProductSpuConvert.INSTANCE.convertSpus(showStatusUpdateReqVOS);
        for (ProductSpuDO productSpuDO : productSpuDOS) {
            Long userId = getLoginUserId();
            String userName = getLoginUserName();
            ProductOperateLogDO operateLog = new ProductOperateLogDO();
            operateLog.setSpuId(productSpuDO.getId());
            operateLog.setContent("修改商品列出状态");
            operateLog.setUserId(userId);
            operateLog.setUserName(userName);
            operateLog.setOperateType(ProductOperateTypeEnum.UPDATE.getCode());
            productOperateLogService.saveProductOperateLog(operateLog);
        }
        productSpuMapper.updateBatch(productSpuDOS,500);
        // 更新SKU
        for (ProductSpuShowStatusUpdateReqVO showStatusUpdateReqVO : showStatusUpdateReqVOS) {
            productSkuService.updateSkuShowStatusBySpu(showStatusUpdateReqVO.getId(), showStatusUpdateReqVO.getShowStatus());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSpuPlatformStatus(@Valid ProductSpuStatusUpdateReqVO statusUpdateReqVO) {
        // 校验 SPU 是否存在
        ProductSpuDO productSpuDO = validateSpuExists(statusUpdateReqVO.getId());

        // 更新 SPU
        ProductSpuDO updateObj = new ProductSpuDO();
        updateObj.setId(statusUpdateReqVO.getId());
        updateObj.setPlatformStatus(statusUpdateReqVO.getStatus());
        productSpuMapper.updateById(updateObj);

        // 更新SKU
        productSkuService.updateSkuPlatformStatusBySpu(statusUpdateReqVO.getId(), statusUpdateReqVO.getStatus());
    }

    /**
     * 校验商品分类是否合法
     *
     * @param categoryPath 商品分类编号，以-分隔
     */
    private void validateCategory(String categoryPath) {
        if (StringUtils.isBlank(categoryPath)) {
            throw exception(CATEGORY_NOT_EXISTS);
        }
        String[] ids = categoryPath.split("-");
        if (!StringUtils.isNumeric(ids[0])) {
            throw exception(CATEGORY_NOT_EXISTS);
        }
        Long id = Long.valueOf(ids[0]);
        categoryService.validateCategory(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSpu(Long id) {
        // 校验存在
        ProductSpuDO productSpuDO = productSpuMapper.selectById(id);
        if (productSpuDO != null) {
            // 删除 SPU
            productSpuMapper.deleteById(id);
        }
        // 删除关联的 SKU
        productSkuService.deleteSkuBySpuId(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disableSpu(Long id){
        // 校验存在
        ProductSpuDO productSpuDO = productSpuMapper.selectById(id);
        if (productSpuDO != null) {
            // 下架 SPU
            productSpuDO.setStatus(ProductSpuStatusEnum.DISABLE.getStatus());
            productSpuMapper.updateById(productSpuDO);
        }
        // 禁用关联的 SKU
        productSkuService.disableSkuBySpuId(id);
    }

    private ProductSpuDO validateSpuExists(Long id) {
        ProductSpuDO productSpuDO = productSpuMapper.selectById(id);
        if (productSpuDO == null) {
            throw exception(SPU_NOT_EXISTS);
        }
        return productSpuDO;
    }

    @Override
    public ProductSpuDO getSpu(Long id) {
        return productSpuMapper.selectById(id);
    }

    @Override
    public ProductSpuDO getSimpleSpu(Long id) {
        return productSpuMapper.selectOne(Wrappers.lambdaQuery(ProductSpuDO.class)
                .eq(ProductSpuDO::getId, id)
                .select(field -> !field.getProperty().equals("description") && !field.getProperty().equals("descriptionH5")));
    }

    @Override
    public ProductSpuDetailRespVO getSpuDetail(Long id) {
        ProductSpuDO spuDO = productSpuMapper.selectById(id);
        List<ProductSkuRespVO> skuRespVOList = productSkuService.getSkuDetailListBySpu(id);
        List<ProductSpuSpecDO> spuSpecValueList = spuSpecService.getSpuSpecListBySpuId(id);

        List<ProductSpecValueRespVO> specValueVOList = ProductSpecConvert.INSTANCE.convertVOList06(spuSpecValueList);

        ProductSpuDetailRespVO spuDetailRespVO = ProductSpuConvert.INSTANCE.convert03(spuDO);
        spuDetailRespVO.setSkus(skuRespVOList);
        spuDetailRespVO.setSpuSpecValueList(specValueVOList);

        return spuDetailRespVO;
    }

    @Override
    public List<ProductSpuDO> getSpuList(Collection<Long> ids, boolean needDescription) {
        if(CollectionUtils.isNotEmpty(ids)) {
            return productSpuMapper.selectList(Wrappers.lambdaQuery(ProductSpuDO.class)
                    .in(ProductSpuDO::getId, ids)
                    .select(field -> !field.getProperty().equals("description") && !field.getProperty().equals("descriptionH5")));
        }

        return null;
    }

    @Override
    public List<ProductSpuDO> getSpuList() {
        return productSpuMapper.selectList();
    }

    @Override
    public PageResult<ProductSpuDO> getSpuPage(ProductSpuPageReqVO pageReqVO) {
        // 分页查询
        return productSpuMapper.selectPage(pageReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSpuStock(Map<Long, Integer> stockIncrCounts) {
        stockIncrCounts.forEach((id, incCount) -> productSpuMapper.updateStock(id, incCount));
    }

    @Override
    public ProductSpuDO getSpuByInnerIdAndSupplierId(String innerId, Long supplierId) {
        return productSpuMapper.selectOne(Wrappers.lambdaQuery(ProductSpuDO.class)
                .eq(ProductSpuDO::getSpuInnerId, innerId)
                .eq(ProductSpuDO::getSupplierId, supplierId)
                .orderByAsc(ProductSpuDO::getCreateTime)
                .last(" limit 1"));
    }

    @Override
    public ProductSpuDO getSpuCategoryByInnerIdAndSupplierId(String innerId, Long supplierId) {
        return productSpuMapper.selectOne(Wrappers.lambdaQuery(ProductSpuDO.class)
                .eq(ProductSpuDO::getSpuInnerId, innerId)
                .eq(ProductSpuDO::getSupplierId, supplierId)
                .orderByAsc(ProductSpuDO::getCreateTime)
                .last(" limit 1")
                .select(
                        ProductSpuDO::getId,
                        ProductSpuDO::getSpuInnerId,
                        ProductSpuDO::getCategory1Id,
                        ProductSpuDO::getCategory1Name,
                        ProductSpuDO::getCategory2Id,
                        ProductSpuDO::getCategory2Name,
                        ProductSpuDO::getCategory3Id,
                        ProductSpuDO::getCategory3Name));
    }

    @Override
    public ProductSpuDO getSimpleSpuBySupplierInnerId(Long supplierId, String spuInnerId) {
        return productSpuMapper.selectOne(Wrappers.lambdaQuery(ProductSpuDO.class)
                .eq(ProductSpuDO::getSupplierId, supplierId)
                .eq(ProductSpuDO::getSpuInnerId, spuInnerId)
                .orderByAsc(ProductSpuDO::getCreateTime)
                .select(ProductSpuDO::getId, ProductSpuDO::getSupplierId, ProductSpuDO::getSpuInnerId)
                .last(" limit 1 "));
    }

    @Override
    public List<ProductSpuDO> getSimpleSpuBySupplierInnerId(Long supplierId, List<String> spuInnerIds) {
        return productSpuMapper.selectList(Wrappers.lambdaQuery(ProductSpuDO.class)
                .eq(ProductSpuDO::getSupplierId, supplierId)
                .in(ProductSpuDO::getSpuInnerId, spuInnerIds)
                .select(ProductSpuDO::getId, ProductSpuDO::getSupplierId, ProductSpuDO::getSpuInnerId));
    }

    @Override
    public List<ProductSpuDO> getSimpleSpuById(Collection<Long> ids) {
        return productSpuMapper.selectList(Wrappers.lambdaQuery(ProductSpuDO.class)
                .in(ProductSpuDO::getId, ids)
                .select(ProductSpuDO::getId, ProductSpuDO::getSupplierId, ProductSpuDO::getSpuInnerId, ProductSpuDO::getFullCategoryId));
    }

    private String generateMallInnerId() {
        return null;
//        return "mall-" + IdUtil.getSnowflakeNextIdStr();
    }

}
