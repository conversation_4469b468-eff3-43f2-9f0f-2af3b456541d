package cn.iocoder.yudao.module.mall.basis.convert.supplier;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.basis.controller.admin.supplier.vo.SupplierCreateReqVO;
import cn.iocoder.yudao.module.mall.basis.controller.admin.supplier.vo.SupplierRespVO;
import cn.iocoder.yudao.module.mall.basis.controller.admin.supplier.vo.SupplierSimpleRespVO;
import cn.iocoder.yudao.module.mall.basis.controller.admin.supplier.vo.SupplierUpdateReqVO;
import cn.iocoder.yudao.module.mall.basis.controller.admin.supplier.vo.profile.SupplierProfileRespVO;
import cn.iocoder.yudao.module.mall.basis.controller.admin.supplier.vo.profile.SupplierProfileUpdateReqVO;
import cn.iocoder.yudao.module.mall.basis.controller.app.supplier.vo.AppSupplierRespVO;
import cn.iocoder.yudao.module.mall.basis.controller.app.supplier.vo.AppSupplierServiceRespVO;
import cn.iocoder.yudao.module.mall.basis.controller.app.supplier.vo.AppSupplierSimpleRespVO;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.supplier.SupplierDO;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.SupplierSearchAggGoodsResp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 供应商 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface SupplierConvert {

    SupplierConvert INSTANCE = Mappers.getMapper(SupplierConvert.class);

    SupplierDO convert(SupplierCreateReqVO bean);

    SupplierDO convert(SupplierUpdateReqVO bean);

    SupplierRespVO convert(SupplierDO bean);

    AppSupplierRespVO convert02(SupplierDO bean);

    SupplierProfileRespVO convert03(SupplierDO bean);

    SupplierDO convert04(SupplierProfileUpdateReqVO bean);

    List<SupplierRespVO> convertList(List<SupplierDO> list);

    List<AppSupplierSimpleRespVO> convertList02(List<SupplierDO> list);

    List<AppSupplierServiceRespVO> convertList03(List<SupplierDO> list);

    List<SupplierSimpleRespVO> convertList04(List<SupplierDO> list);

    PageResult<SupplierRespVO> convertPage(PageResult<SupplierDO> page);

    PageResult<AppSupplierServiceRespVO> convertPage02(PageResult<SupplierDO> page);

    @Mappings(value = {
            @Mapping(source = "id", target = "supplierId")
    })
    SupplierSearchAggGoodsResp convertRsp(SupplierDO supplierDO);

    List<SupplierSearchAggGoodsResp> convertRsps(List<SupplierDO> supplierDOS);

}
