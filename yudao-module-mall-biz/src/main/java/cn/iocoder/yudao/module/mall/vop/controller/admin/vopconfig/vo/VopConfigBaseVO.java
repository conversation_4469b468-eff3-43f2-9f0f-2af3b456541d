package cn.iocoder.yudao.module.mall.vop.controller.admin.vopconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * VOP配置信息 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class VopConfigBaseVO {

    @Schema(description = "app_key", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "app_key不能为空")
    private String appKey;

    @Schema(description = "app_secret", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "app_secret不能为空")
    private String appSecret;

    @Schema(description = "私钥", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "私钥不能为空")
    private String privateKeyStr;

    @Schema(description = "公钥", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "公钥不能为空")
    private String publicKeyStr;

    @Schema(description = "供应商账户", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "供应商账户不能为空")
    private String username;

    @Schema(description = "供应商密码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "供应商密码不能为空")
    private String password;

    @Schema(description = "支付类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "支付类型不能为空")
    private Integer paymentType;

    @Schema(description = "商品池校验", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean poolNameValidate;

    @Schema(description = "全量商品池开关", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean fullPoolSwitch;

}
