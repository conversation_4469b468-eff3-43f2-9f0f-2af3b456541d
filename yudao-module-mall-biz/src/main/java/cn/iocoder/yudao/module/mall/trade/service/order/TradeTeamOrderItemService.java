package cn.iocoder.yudao.module.mall.trade.service.order;

import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeTeamOrderItemDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Collection;
import java.util.List;

/**
 * 协同订单项 Service 接口
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
public interface TradeTeamOrderItemService extends IService<TradeTeamOrderItemDO> {

    /**
     * 查询协同订单项列表
     * @param teamOrderId
     * @return
     */
    List<TradeTeamOrderItemDO> getItemListByTeamOrder(Long teamOrderId);

    List<TradeTeamOrderItemDO> getItemListByTeamOrder(Long teamOrderId, Long userId);

    TradeTeamOrderItemDO getByUser(Long id, Long userId);

    TradeTeamOrderItemDO getByTeamUser(Long id, Long teamUserId);

    void deleteByUser(Long id, Long userId);

    void deleteByTeamUser(Long id, Long teamUserId);

    TradeTeamOrderItemDO updateCountByUser(Long id, Integer count, Long userId);

    TradeTeamOrderItemDO updateCountByTeamUser(Long id, Integer count, Long teamUserId);

    /**
     * 查询协同订单项列表
     * @param teamOrderIds
     * @return
     */
    List<TradeTeamOrderItemDO> getItemListByTeamOrder(Collection<Long> teamOrderIds);

}
