package cn.iocoder.yudao.module.mall.trade.controller.app.orderassets.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "前台APP - 订单明细固资信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppTradeOrderItemAssetsRespVO extends AppTradeOrderItemAssetsBaseVO {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

    @Schema(description = "完成时间", required = true)
    private LocalDateTime assetFinishTime;

}
