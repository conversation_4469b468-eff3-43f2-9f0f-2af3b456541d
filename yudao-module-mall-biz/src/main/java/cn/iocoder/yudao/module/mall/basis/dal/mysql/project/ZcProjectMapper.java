package cn.iocoder.yudao.module.mall.basis.dal.mysql.project;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.mall.basis.controller.admin.project.vo.ZcProjectExportReqVO;
import cn.iocoder.yudao.module.mall.basis.controller.admin.project.vo.ZcProjectPageReqVO;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.project.ZcProjectDO;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 直采项目经费卡 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ZcProjectMapper extends BaseMapperX<ZcProjectDO> {

    default PageResult<ZcProjectDO> selectPage(ZcProjectPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ZcProjectDO>()
                .betweenIfPresent(ZcProjectDO::getCreateTime, reqVO.getCreateTime())
                .like(StringUtils.isNotBlank(reqVO.getName()), ZcProjectDO::getName, reqVO.getName())
                .eq(StringUtils.isNotBlank(reqVO.getNo()), ZcProjectDO::getNo, reqVO.getNo())
                .eq(StringUtils.isNotBlank(reqVO.getTypeCode()), ZcProjectDO::getTypeCode, reqVO.getTypeCode())
                .like(StringUtils.isNotBlank(reqVO.getTypeName()), ZcProjectDO::getTypeName, reqVO.getTypeName())
                .eq(StringUtils.isNotBlank(reqVO.getChargeNo()), ZcProjectDO::getChargeNo, reqVO.getChargeNo())
                .like(StringUtils.isNotBlank(reqVO.getChargeName()), ZcProjectDO::getChargeName, reqVO.getChargeName())
                .like(StringUtils.isNotBlank(reqVO.getDepartmentName()), ZcProjectDO::getDepartmentName, reqVO.getDepartmentName())
                .eq(StringUtils.isNotBlank(reqVO.getDepartmentNo()), ZcProjectDO::getDepartmentNo, reqVO.getDepartmentNo())
                .orderByDesc(ObjectUtil.equal(reqVO.getSortType(), 10), ZcProjectDO::getCreateTime)
                .orderByAsc(ObjectUtil.equal(reqVO.getSortType(), 11), ZcProjectDO::getCreateTime)
                .orderByDesc(ObjectUtil.equal(reqVO.getSortType(), 20), ZcProjectDO::getBalance)
                .orderByAsc(ObjectUtil.equal(reqVO.getSortType(), 21), ZcProjectDO::getBalance)
                .orderByDesc(reqVO.getSortType() == null, ZcProjectDO::getId));
    }

    default List<ZcProjectDO> selectList(ZcProjectExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ZcProjectDO>()
                .betweenIfPresent(ZcProjectDO::getCreateTime, reqVO.getCreateTime())
                .like(StringUtils.isNotBlank(reqVO.getName()), ZcProjectDO::getName, reqVO.getName())
                .eq(StringUtils.isNotBlank(reqVO.getNo()), ZcProjectDO::getNo, reqVO.getNo())
                .eq(StringUtils.isNotBlank(reqVO.getTypeCode()), ZcProjectDO::getTypeCode, reqVO.getTypeCode())
                .like(StringUtils.isNotBlank(reqVO.getTypeName()), ZcProjectDO::getTypeName, reqVO.getTypeName())
                .eq(StringUtils.isNotBlank(reqVO.getChargeNo()), ZcProjectDO::getChargeNo, reqVO.getChargeNo())
                .like(StringUtils.isNotBlank(reqVO.getChargeName()), ZcProjectDO::getChargeName, reqVO.getChargeName())
                .like(StringUtils.isNotBlank(reqVO.getDepartmentName()), ZcProjectDO::getDepartmentName, reqVO.getDepartmentName())
                .eq(StringUtils.isNotBlank(reqVO.getDepartmentNo()), ZcProjectDO::getDepartmentNo, reqVO.getDepartmentNo())
                .orderByDesc(ZcProjectDO::getId));
    }

}
