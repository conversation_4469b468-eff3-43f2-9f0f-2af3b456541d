package cn.iocoder.yudao.module.mall.trade.controller.admin.bill.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 账单 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BillRespVO extends BillBaseVO {

    /**
     * id
     */
    @Schema(description = "id", required = true)
    private Long id;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

    /**
     * 创建时间
     */
    @Schema(description = "结算订单数量", required = true)
    private Integer settlementCount;

}
