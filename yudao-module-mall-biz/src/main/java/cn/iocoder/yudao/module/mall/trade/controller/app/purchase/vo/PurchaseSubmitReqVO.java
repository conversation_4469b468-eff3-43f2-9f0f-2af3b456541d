package cn.iocoder.yudao.module.mall.trade.controller.app.purchase.vo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 用户 App - 采购提交 Request VO
 * <AUTHOR>
 */
@Schema(description = "用户 App - 采购提交 Request VO")
@Data
public class PurchaseSubmitReqVO {

    /**
     * 订单id集合
     */
    @Valid
    @Schema(description = "订单id集合")
    @NotEmpty(message = "订单id不能为空")
    private List<Long> ids;

    /**
     * 经济分类
     */
    @Schema(description = "经济分类编码")
    private String economyClass;

    /**
     * 经济分类
     */
    @Schema(description = "经济分类名称")
    private String economyClassName;

    /**
     * 项目信息
     */
    @Schema(description = "项目信息")
    @Valid
    private ProjectInfoDTO projectInfo;

    /**
     * 采购原因
     */
    @Schema(description = "采购原因")
    private String purchaseReason;

    /**
     * 附件
     */
    @Schema(description = "附件")
    private List<String> attachments;

    /**
     * 附件2
     */
    @Schema(description = "附件2")
    private List<PurchaseAttachmentDTO> attachments2;

    /**
     * 审批人信息
     */
    @Schema(description = "审批人信息")
    private List<ApprovalUserInfoDTO> approvalUserInfos;

    /**
     * 验收人姓名
     */
    @Schema(description = "验收人姓名")
    private String accepterName;

    /**
     * 验收人手机号
     */
    @Schema(description = "验收人手机号")
    private String accepterMobile;

    /**
     * 验收人邮箱
     */
    @Schema(description = "验收人邮箱")
    private String accepterEmail;

    /**
     * 时间戳
     */
    @Schema(description = "时间戳")
    private Long timestamp;

    /**
     * 生成审批流参数
     * @return
     */
    @JsonIgnore
    public String genBpmParam() {
        if(CollUtil.isNotEmpty(approvalUserInfos)) {
            return JsonUtils.toJsonString(approvalUserInfos);
        }
        return null;
    }

    @JsonIgnore
    public String getAttachment2JsonStr() {
        if(CollUtil.isNotEmpty(attachments2)) {
            return JsonUtils.toJsonString(attachments2);
        }
        return null;
    }

    @JsonIgnore
    public static List<ApprovalUserInfoDTO> parseBpmParam(String bpmParam) {
        List<ApprovalUserInfoDTO> approvalUserInfos;
        if(StrUtil.isBlank(bpmParam)) {
            return null;
        }
        return JsonUtils.parseArray(bpmParam, ApprovalUserInfoDTO.class);
    }

}
