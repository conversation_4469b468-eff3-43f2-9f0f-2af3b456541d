package cn.iocoder.yudao.module.mall.trade.controller.app.purchase.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 用户 App - 采购信息校验 Request VO
 * <AUTHOR>
 */
@Schema(description = "用户 App - 采购提交 Request VO")
@Data
public class PurchaseValidateReqVO {

    /**
     * 商品信息
     */
    @Valid
    @Schema(description = "商品信息")
    @NotEmpty(message = "商品信息不能为空")
    private List<PurchaseSkuInfoVO> skuInfoList;

    /**
     * 经济分类
     */
    @Schema(description = "经济分类编码")
    private String economyClass;

    /**
     * 项目信息
     */
    @Schema(description = "项目信息")
    @Valid
    private ProjectInfoDTO projectInfo;

    /**
     * 采购原因
     */
    @Schema(description = "采购原因")
    private String purchaseReason;

    /**
     * 附件
     */
    @Schema(description = "附件")
    private List<String> attachments;

    /**
     * 验收人姓名
     */
    @Schema(description = "验收人姓名")
    private String accepterName;

    /**
     * 验收人手机号
     */
    @Schema(description = "验收人手机号")
    private String accepterMobile;

    /**
     * 验收人邮箱
     */
    @Schema(description = "验收人邮箱")
    private String accepterEmail;

    /**
     * 时间戳
     */
    @Schema(description = "时间戳")
    private Long timestamp;

}
