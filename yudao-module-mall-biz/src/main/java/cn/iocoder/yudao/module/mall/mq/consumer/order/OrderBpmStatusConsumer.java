package cn.iocoder.yudao.module.mall.mq.consumer.order;

import cn.iocoder.yudao.module.mall.external.bpm.BpmClient;
import cn.iocoder.yudao.module.mall.mq.message.order.OrderBpmStatusMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.function.Consumer;

/**
 * 订单审批流回调 的消费者
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class OrderBpmStatusConsumer implements Consumer<OrderBpmStatusMessage> {

    @Resource
    private BpmClient bpmClient;

    @Override
    public void accept(OrderBpmStatusMessage message) {
        log.info("[accept][消息内容-订单审批状态更新处理:{}]", message);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            bpmClient.pullBpmStatus(message.getBpmNo());
        } catch(Exception e) {
            log.error("订单审批状态更新处理失败:", e);
        }
        stopWatch.stop();
        log.info("[accept][消息内容-订单审批状态更新处理完成，耗时：{}秒", stopWatch.getTotalTimeSeconds());
    }

}
