package cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 交易统计明细的分页项 Response VO")
@Data
public class StatsSaleSkuRespVO {

    /**
     * 订单数量
     */
    private Long orderCount;
    /**
     * 总金额
     */
    private BigDecimal totalPrice;
    /**
     * 取消订单总金额
     */
    private BigDecimal cancelTotalPrice;
    /**
     * 收入总金额
     */
    private BigDecimal netTotalPrice;
    /**
     * 售后总金额
     */
    private BigDecimal afterSaleTotalPrice;
    /**
     * 售后总订单数
     */
    private BigDecimal afterSaleTotalCount;
    /**
     * 平台SKU ID
     */
    private Long skuId;
    /**
     * 三方SKU ID
     */
    private String thirdSkuId;
    /**
     * SKU 名称
     */
    private String skuName;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 运营标签
     */
    private List<String> skuTags;

    @JsonIgnore
    private String tagNames;


}
