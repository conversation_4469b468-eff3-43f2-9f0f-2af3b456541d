package cn.iocoder.yudao.module.mall.trade.controller.app.order.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

@Schema(description = "用户 App - 订单交易的明细 Response VO")
@Data
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class AppTradeOrderDetailOpenRespVO {

    // ========== 订单基本信息 ==========

    /**
     * 订单编号
     */
    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    /**
     * 订单流水号
     */
    @Schema(description = "订单流水号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1146347329394184195")
    private String no;

    /**
     * 下单时间
     */
    @Schema(description = "下单时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime createTime;

    /**
     * 用户备注
     */
    @Schema(description = "用户备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你猜")
    private String userRemark;

    /**
     * 订单状态
     */
    @Schema(description = "订单状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;

    /**
     * 购买的商品数量
     */
    @Schema(description = "购买的商品数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "10")
    private Integer productCount;

    /**
     * 订单完成时间
     */
    @Schema(description = "订单完成时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime finishTime;

    /**
     * 订单取消时间
     */
    @Schema(description = "订单取消时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime cancelTime;

    /**
     * 用户取消原因
     */
    @Schema(description = "用户取消原因", example = "你猜")
    private String cancelReason;

    // ========== 价格 + 支付基本信息 ==========

    /**
     * 付款时间
     */
    @Schema(description = "付款时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime payTime;

    /**
     * 商品价格
     */
    @Schema(description = "商品价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "1000")
    private BigDecimal productPrice;

    /**
     * 订单价格
     */
    @Schema(description = "订单价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "1000")
    private BigDecimal orderPrice;


    /**
     * 运费金额
     */
    @Schema(description = "运费金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private BigDecimal deliveryPrice;

    /**
     * 应付金额（总）
     */
    @Schema(description = "应付金额（总）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1000")
    private BigDecimal payPrice;

    // ========== 收件 + 物流基本信息 ==========

    /**
     * 收件人地址信息
     */
    private AppOpenAddressRespVO address;

    /**
     * 发货物流单号
     */
    @Schema(description = "发货物流单号", example = "1024")
    private String logisticsNo;

    /**
     * 发货时间
     */
    @Schema(description = "发货时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime deliveryTime;

    /**
     * 收货时间
     */
    @Schema(description = "收货时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime receiveTime;


    // ========== 售后基本信息 ==========
    /**
     * 订单项数组
     */
    private List<Item> items;

    @Schema(description = "用户 App - 交易订单的分页项的订单项目")
    @Data
    public static class Item {


        /**
         * id
         */
        @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        private Long id;


        // ========== 商品基本信息 ==========

        /**
         * 商品 SKU 编号
         */
        @Schema(description = "商品 SKU 编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        private Long skuId;

        /**
         * 商品名称
         */
        @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        private String skuName;

        /**
         * 商品图片
         */
        @Schema(description = "商品图片", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn/1.png")
        private String picUrl;

        /**
         * 购买数量
         */
        @Schema(description = "购买数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        private Integer count;

        // ========== 价格 + 支付基本信息 ==========

        /**
         * 商品单价
         *
         */
        @Schema(description = "商品单价", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        private BigDecimal skuPrice;

        /**
         * 商品总价
         *
         */
        @Schema(description = "商品总价", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        private BigDecimal skuTotalPrice;

    }

}
