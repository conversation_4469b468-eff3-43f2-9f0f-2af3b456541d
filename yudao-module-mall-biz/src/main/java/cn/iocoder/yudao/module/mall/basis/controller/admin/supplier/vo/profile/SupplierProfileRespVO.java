package cn.iocoder.yudao.module.mall.basis.controller.admin.supplier.vo.profile;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 供应商 Response VO")
@Data
@ToString(callSuper = true)
public class SupplierProfileRespVO {

    @Schema(description = "全称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String fullName;

    @Schema(description = "编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String code;

    @Schema(description = "简称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @Schema(description = "Logo地址", requiredMode = Schema.RequiredMode.REQUIRED)
    private String logoUrl;

    @Schema(description = "联系人", requiredMode = Schema.RequiredMode.REQUIRED)
    private String contactName;

    @Schema(description = "联系人电话", requiredMode = Schema.RequiredMode.REQUIRED)
    private String contactMobile;

    @Schema(description = "客服电话", requiredMode = Schema.RequiredMode.REQUIRED)
    private String servicePhone;

    @Schema(description = "退货地址", requiredMode = Schema.RequiredMode.REQUIRED)
    private String returnAddress;

    @Schema(description = "状态：0-未配置，1-启用，2-停用", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer status;

    @Schema(description = "类型：1-京东", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer type;

    @Schema(description = "包邮金额", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer freightThreshold;

    @Schema(description = "邮费", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer freight;

    @Schema(description = "起售金额", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer saleAmountMin;

    @Schema(description = "上架商品最大数量", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer onSaleSkuLimit;

    @Schema(description = "商品最大数量", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer skuLimit;

    @Schema(description = "客服名称")
    private String serviceAgent;

    @Schema(description = "客服微信号")
    private String serviceWechatId;

    @Schema(description = "客服QQ号")
    private String serviceQqId;

    @Schema(description = "是否订阅订单消息")
    private Boolean subMessage;

    @Schema(description = "多规格显示开关")
    private Boolean multipleSpec;

    @Schema(description = "更新时间", required = true)
    private LocalDateTime updateTime;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
