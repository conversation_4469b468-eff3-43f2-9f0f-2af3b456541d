package cn.iocoder.yudao.module.mall.basis.dal.mysql.stats;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.OrderStatsReqVO;
import cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.StatsOrderSaleVO;
import cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.StatsSkuSaleVO;
import cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo.StatsTopSkuSaleVO;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.stats.StatsOrderDO;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 订单销量统计 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface StatsOrderMapper extends BaseMapperX<StatsOrderDO> {

    default boolean exitsByOrderId(Long orderId) {
        return selectCount(StatsOrderDO::getOrderId, orderId) > 0;
    }

    default StatsOrderDO getByOrderId(Long orderId) {
        return selectOne(StatsOrderDO::getOrderId, orderId);
    }

    default List<StatsOrderDO> getByOrderId(List<Long> orderId) {
        return selectList(Wrappers.lambdaQuery(StatsOrderDO.class).in(StatsOrderDO::getOrderId, orderId));
    }

    void deleteByOrderV1(@Param("params") Map<String, Object> params);

    void deleteItemsByOrderV1(@Param("params") Map<String, Object> params);

    void deleteByOrderCancelV1(@Param("params") Map<String, Object> params);

    List<StatsOrderSaleVO> queryOrderSaleOnDate(@Param("params")OrderStatsReqVO reqVO);

    List<StatsOrderSaleVO> queryOrderSaleOnMonth(@Param("params")OrderStatsReqVO reqVO);

    StatsOrderSaleVO queryTotalOrderSaleOnDate(@Param("params")OrderStatsReqVO reqVO);

    List<StatsOrderSaleVO> querySupplierSaleStats(@Param("params")OrderStatsReqVO reqVO);

    List<StatsSkuSaleVO> querySkuSaleOnDate(@Param("params")OrderStatsReqVO reqVO);

    List<StatsTopSkuSaleVO> queryTopSaleSku(@Param("params")OrderStatsReqVO reqVO);

    List<StatsTopSkuSaleVO> queryTopSaleCategory2(@Param("params")OrderStatsReqVO reqVO);

    List<StatsTopSkuSaleVO> queryTopSaleCategory3(@Param("params")OrderStatsReqVO reqVO);


}
