package cn.iocoder.yudao.module.mall.trade.controller.admin.bill.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 账单 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class BillExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("用户id")
    private Long userId;

    @ExcelProperty("账单名称")
    private String billName;

    @ExcelProperty("订单数")
    private Integer orderCount;

    @ExcelProperty("账单总金额")
    private BigDecimal totalAmount;

    @ExcelProperty("账单状态 0-新建 1-已推送 2-已取消 3-已完成")
    private Integer billStatus;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
