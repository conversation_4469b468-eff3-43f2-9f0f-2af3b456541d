package cn.iocoder.yudao.module.mall.trade.controller.app.cart.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Collection;

/**
 * 用户 App - 购物车更新是否选中 Request VO
 */
@Schema(description = "用户 App - 购物车更新是否选中 Request VO")
@Data
public class AppTradeCartItemUpdateSelectedReqVO {

    /**
     * "商品 SKU 编号列表
     */
    @Schema(description = "商品 SKU 编号列表", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024,2048")
    @NotEmpty(message = "商品 SKU 编号列表不能为空")
    private Collection<Long> skuIds;

    /**
     * 是否选中
     */
    @Schema(description = "是否选中", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    @NotNull(message = "是否选中不能为空")
    private Boolean selected;

}
