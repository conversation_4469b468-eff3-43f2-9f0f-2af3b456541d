package cn.iocoder.yudao.module.mall.vop.req;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/6/13 16:39
 */

@Data
public class VopInvoiceSubmitInvoiceApplyReq {

    private String invoiceDate;
    private String markId;
    private Integer invoiceOrg;
    private BigDecimal totalBatchInvoiceAmount;
    private String billToContact;
    private String title;
    private Integer currentBatch;
    private String enterpriseTaxpayer;
    private Integer billingType;
    private String settlementId;
    private Integer invoiceNum;
    private String enterpriseRegAddress;
    private Integer billToProvince;
    private Integer isMerge;
    private String enterpriseBankName;
    private Integer billToCounty;
    private String enterpriseBankAccount;
    private Integer invoiceType;
    private Integer billToTown;
    private String billToAddress;
    private String poNo;
    private BigDecimal invoicePrice;
    private String supplierOrder;
    private String repaymentDate;
    private String invoiceRemark;
    private Integer totalBatch;
    private String billToer;
    private Integer billToCity;
    private Integer bizInvoiceContent;
    private Integer settlementNum;
    private BigDecimal settlementNakedPrice;
    private BigDecimal settlementTaxPrice;
    private String billToParty;
    private BigDecimal totalBatchInvoiceNakedAmount;
    private BigDecimal invoiceNakedPrice;
    private BigDecimal invoiceTaxPrice;
    private BigDecimal totalBatchInvoiceTaxAmount;
    private String enterpriseRegPhone;
    private Integer deliveryType;

}
