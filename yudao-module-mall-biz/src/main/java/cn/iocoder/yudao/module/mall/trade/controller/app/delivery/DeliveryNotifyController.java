package cn.iocoder.yudao.module.mall.trade.controller.app.delivery;

import cn.iocoder.yudao.framework.common.util.tenant.TenantIdUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.trade.controller.admin.delivery.vo.DeliveryCallbackRespVO;
import cn.iocoder.yudao.module.mall.trade.service.delivery.DeliveryService;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Tag(name = "物流 - 快递100物流通知回调")
@RestController
@RequestMapping("/trade/delivery/notify")
@Validated
@Slf4j
public class DeliveryNotifyController {

    @Resource
    private DeliveryService deliveryService;

    /**
     * 订阅物流回调
     * @param sign 签名
     * @param param 物流详情form
     * @return
     */
    @PostMapping("/callback")
    @Operation(summary = "订阅物流回调")
    @ResponseBody
    public String deliveryCallback(@RequestParam(required = false) String sign, @RequestParam(required = true) String param, String tcode) {
        if(StringUtils.isBlank(tcode)) {
            log.error("kuaidi100-delivery-callback tcode null");
        } else {
            // 设置租户ID上下文
            TenantContextHolder.setTenantId(TenantIdUtils.decryptTenantId(tcode));
            TenantContextHolder.setIgnore(false);
        }

        Boolean ret = deliveryService.deliveryCallback(param);
        if(ret){
            return JSON.toJSONString(DeliveryCallbackRespVO.success());
        }
        else {
            return JSON.toJSONString(DeliveryCallbackRespVO.error());
        }
    }
}
