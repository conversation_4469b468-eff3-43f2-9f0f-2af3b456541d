package cn.iocoder.yudao.module.mall.util.fronttask.vo;

import cn.iocoder.yudao.module.mall.util.fronttask.AsyncTaskTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 异步导出任务
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class AsyncExportTask extends AsyncFrontTask {

    @Override
    public void init(Long userId, String taskId, AsyncTaskTypeEnum taskTypeEnum) {
        super.init(userId, taskId, taskTypeEnum);
    }

}
