package cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 运营统计中日期维度销量导出
 *
 * <AUTHOR>
 * @Date 2024/6/13
 */
@Schema(description = "管理后台 - 运营统计中日期维度销量导出excel VO")
@Data
public class StatsSaleDateExportVO {

    @Excel(name = "统计日期", exportFormat = "yyyy-MM-dd", format = "yyyy-MM-dd", width = 20)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime statsDate;

    @Excel(name = "订单总数", width = 20)
    private Long orderCount;

    @Excel(name = "订单总金额", width = 20)
    private BigDecimal totalPrice;

    @Excel(name = "取消订单总金额", width = 20)
    private BigDecimal cancelTotalPrice;

    @Excel(name = "售后总金额", width = 20)
    private BigDecimal afterSaleTotalPrice;

    @Excel(name = "售后总订单数", width = 20)
    private Long afterSaleTotalCount;

    @Excel(name = "净销售金额", width = 20)
    private BigDecimal netTotalPrice;

}
