package cn.iocoder.yudao.module.mall.basis.controller.admin.supplier.vo.form;

import cn.iocoder.yudao.module.mall.framework.file.InfraFileInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 供应商入驻申请 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SupplierFormDetailRespVO extends SupplierFormBaseVO {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "法人照片")
    private List<InfraFileInfo> fileLegalRep;

    @Schema(description = "营业执照")
    private List<InfraFileInfo> fileLicense;

    @Schema(description = "资质文件")
    private List<InfraFileInfo> fileQualification;

    @Schema(description = "补充材料")
    private List<InfraFileInfo> fileOther;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;


}
