package cn.iocoder.yudao.module.mall.trade.controller.app.orderassets.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单明细固资信息 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class AppTradeOrderItemAssetsBaseVO {

    @Schema(description = "办理人工号")
    private String userNo;

    @Schema(description = "办理人用户名")
    private String userName;

    @Schema(description = "办理人部门编号")
    private String deptNo;

    @Schema(description = "办理人部门名称")
    private String deptName;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "订单ID")
    private Long orderId;

    @Schema(description = "订单明细ID")
    private Long orderItemId;

    @Schema(description = "供应商名称")
    private String supplier;

    @Schema(description = "商品名称")
    private String skuName;

    @Schema(description = "单价")
    private BigDecimal price;

    @Schema(description = "数量")
    private Integer quantity;

    @Schema(description = "型号")
    private String model;

    @Schema(description = "规格")
    private String spec;

    @Schema(description = "联系人手机号")
    private String phone;

    @Schema(description = "发票号码")
    private String invoiceNo;

    @Schema(description = "发票地址")
    private String invoiceUrl;

    @Schema(description = "购买日期")
    private LocalDateTime buyDate;

    @Schema(description = "状态")
    private Integer assetStatus;

    @Schema(description = "外部类别编码")
    private String extCategoryCode;

    @Schema(description = "外部类别名称")
    private String extCategoryName;

}
