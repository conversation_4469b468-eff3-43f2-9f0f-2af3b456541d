package cn.iocoder.yudao.module.mall.basis.convert.project;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.basis.controller.admin.project.vo.ProjectWhiteSkuCreateReqVO;
import cn.iocoder.yudao.module.mall.basis.controller.admin.project.vo.ProjectWhiteSkuRespVO;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.project.ProjectWhiteSkuDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 项目经费卡SKU白名单 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ProjectWhiteSkuConvert {

    ProjectWhiteSkuConvert INSTANCE = Mappers.getMapper(ProjectWhiteSkuConvert.class);

    ProjectWhiteSkuDO convert(ProjectWhiteSkuCreateReqVO bean);

    List<ProjectWhiteSkuDO> convert(List<ProjectWhiteSkuCreateReqVO> bean);

    ProjectWhiteSkuRespVO convert(ProjectWhiteSkuDO bean);

    List<ProjectWhiteSkuRespVO> convertList(List<ProjectWhiteSkuDO> list);

    PageResult<ProjectWhiteSkuRespVO> convertPage(PageResult<ProjectWhiteSkuDO> page);

}
