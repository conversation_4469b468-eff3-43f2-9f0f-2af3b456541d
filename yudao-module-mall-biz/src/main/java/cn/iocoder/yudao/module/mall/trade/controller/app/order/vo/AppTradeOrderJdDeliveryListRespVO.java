package cn.iocoder.yudao.module.mall.trade.controller.app.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "用户 App - 订单列表jd物流信息 Response VO")
@Data
public class AppTradeOrderJdDeliveryListRespVO {

    /**
     * 订单编号
     */
    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    /**
     * 订单提示信息
     */
    @Schema(description = "订单提示信息", requiredMode = Schema.RequiredMode.REQUIRED, example = "您的快件已到达【武汉亚一一期接货仓】")
    private String tip;

}
