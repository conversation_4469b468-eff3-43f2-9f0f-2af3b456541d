package cn.iocoder.yudao.module.mall.trade.service.billorder;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.trade.controller.admin.billorder.vo.BillOrderCreateReqVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.billorder.vo.BillOrderExportReqVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.billorder.vo.BillOrderPageReqVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.billorder.vo.BillOrderUpdateReqVO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.billorder.BillOrderDO;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 账单订单 Service 接口
 *
 * <AUTHOR>
 */
public interface BillOrderService extends IService<BillOrderDO> {

    /**
     * 创建账单订单
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createBillOrder(@Valid BillOrderCreateReqVO createReqVO);

    /**
     * 更新账单订单
     *
     * @param updateReqVO 更新信息
     */
    void updateBillOrder(@Valid BillOrderUpdateReqVO updateReqVO);

    /**
     * 删除账单订单
     *
     * @param id 编号
     */
    void deleteBillOrder(Long id);

    void deleteBillOrder(List<Long> ids);

    /**
     * 获得账单订单
     *
     * @param id 编号
     * @return 账单订单
     */
    BillOrderDO getBillOrder(Long id);


    /**
     * 根据账单id获取账单订单列表
     * @param billId
     * @return
     */
    List<BillOrderDO> getBillOrdersByBillId(Long billId);

    /**
     * 获取订单对应的账单关系
     * @param orderId
     * @return
     */
    BillOrderDO getBillOrderByOrder(Long orderId);

    List<BillOrderDO> getBillOrderListByOrder(List<Long> orderIds);

    List<BillOrderDO> getBillOrdersByBillIds(List<Long> billIds);

    /**
     * 获得账单订单列表
     *
     * @param ids 编号
     * @return 账单订单列表
     */
    List<BillOrderDO> getBillOrderList(Collection<Long> ids);

    List<BillOrderDO> getBillOrderList(Long billId, Collection<Long> orderIds);

    /**
     * 获得账单订单分页
     *
     * @param pageReqVO 分页查询
     * @return 账单订单分页
     */
    PageResult<BillOrderDO> getBillOrderPage(BillOrderPageReqVO pageReqVO);

    /**
     * 获得账单订单列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 账单订单列表
     */
    List<BillOrderDO> getBillOrderList(BillOrderExportReqVO exportReqVO);


    /**
     * 根据账单id删除账单订单
     * @param billId
     */
    void removeByBillId(Long billId);

}
