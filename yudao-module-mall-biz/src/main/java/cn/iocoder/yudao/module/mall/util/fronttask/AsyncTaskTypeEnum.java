package cn.iocoder.yudao.module.mall.util.fronttask;

import cn.hutool.core.util.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum AsyncTaskTypeEnum {

    PRODUCT_SKU_EXPORT("productSkuExport", "商品SKU导出", 1),
    PRODUCT_SEO_SKU_EXPORT("productSeoSkuExport", "运营商品SKU导出", 1),
    ORDER_LIST("order", "订单导出", 1),
    ORDER_ASSETS_LIST("orderAssets", "订单固资建档信息导出", 1),
    SETTLE_ORDER_LIST("settleOrder", "结算订单导出", 1),
    BILL_ORDER_LIST("billOrder", "账单导出", 1),

    PRODUCT_SKU_SEO_IMPORT("productSkuSeo", "商品运营SKU导入", 2),
    PRODUCT_SKU_TAG_IMPORT("productSkuTag", "商品运营SKU标签导入", 2),

    PRODUCT_CATEGORY_IMPORT("productCategory", "商品分类导入", 2),

    PRODUCT_VOP_CATEGORY_MAPPING_IMPORT("productVopCategoryMapping", "京东分类映射导入", 2),

    VOP_SKU_CATEGORY_IMPORT("vopSkuCategory", "京东商品导入", 2),
    STATS_ORDER_ITEM_V1("statsOrderItem", "销售订单明细导出", 1),
    STATS_AFTER_SALE_V1("statsAfterSale", "销售售后明细导出", 1),
    STATS_SALE_ON_TYPE_V1("statsSaleOnType", "运营销量统计导出", 1),

    ORDER_DELIVERY_IMPORT("orderDelivery", "订单物流导入", 2);

    private String taskCode;
    private String taskName;
    /**
     * 任务类型 1-导出 2-导入
     */
    private Integer type;

    public boolean isExport() {
        return ObjectUtil.equals(type, 1);
    }

}
