package cn.iocoder.yudao.module.mall.trade.service.delivery.impl;

import cn.iocoder.yudao.module.mall.trade.dal.dataobject.delivery.DeliveryTrackDO;
import cn.iocoder.yudao.module.mall.trade.dal.mysql.delivery.DeliveryTrackMapper;
import cn.iocoder.yudao.module.mall.trade.service.delivery.DeliveryTrackService;
import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.mall.trade.enums.ErrorCodeConstants.DELIVERY_TRACK_NOT_EXISTS;

/**
 * 物流轨迹 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DeliveryTrackServiceImpl extends ServiceImpl<DeliveryTrackMapper, DeliveryTrackDO> implements DeliveryTrackService {
    @Resource
    private DeliveryTrackMapper deliveryTrackMapper;

    @Override
    public void deleteDeliveryTrack(Long id) {
        // 校验存在
        validateDeliveryTrackExists(id);
        // 删除
        deliveryTrackMapper.deleteById(id);
    }

    private void validateDeliveryTrackExists(Long id) {
        if (deliveryTrackMapper.selectById(id) == null) {
            throw exception(DELIVERY_TRACK_NOT_EXISTS);
        }
    }

    @Override
    public DeliveryTrackDO getDeliveryTrack(Long id) {
        return deliveryTrackMapper.selectById(id);
    }

    @Override
    public List<DeliveryTrackDO> getDeliveryTrackList(Collection<Long> ids) {
        return deliveryTrackMapper.selectBatchIds(ids);
    }

    @Override
    public List<DeliveryTrackDO> getListByDelivery(String deliveryNum) {
        return deliveryTrackMapper.selectList(Wrappers.lambdaQuery(
                DeliveryTrackDO.class).eq(DeliveryTrackDO::getNum, deliveryNum).orderByAsc(DeliveryTrackDO::getId));
    }

    //获取最新的物流轨迹
    @Override
    public DeliveryTrackDO getLatestByDelivery(Wrapper<DeliveryTrackDO> queryWrapper) {
        return deliveryTrackMapper.selectOne(queryWrapper);
    }

    @Override
    public Boolean sync(List<DeliveryTrackDO> newItems, Wrapper<DeliveryTrackDO> queryWrapper) {
        List<Long> deleteIds = new ArrayList<>();
        List<DeliveryTrackDO> updates = new ArrayList<>();
        List<DeliveryTrackDO> inserts = new ArrayList<>();
//        newItems = newItems.stream().filter(item -> StringUtils.isNotBlank(item.getStatus())).collect(Collectors.toList());
        List<DeliveryTrackDO> oldItems = deliveryTrackMapper.selectList(queryWrapper);
        // 新的数据长度大于表中的长度，更新和插入操作
        if(newItems.size() > oldItems.size()){
            int i = 0;
            for (; i < oldItems.size(); i++) {
                newItems.get(i).setId(oldItems.get(i).getId());
                updates.add(newItems.get(i));
            }
            inserts.addAll(newItems.subList(i, newItems.size()));
        } else {
            // 新的数据长度小于表中的长度，删除和更新操作
            Integer sub = oldItems.size() - newItems.size();
            for (int i = 0; i < oldItems.size(); i++) {
                if(i <  sub){
                    deleteIds.add(oldItems.get(i).getId());
                }
                else {
                    newItems.get(i - sub).setId(oldItems.get(i).getId());
                    updates.add(newItems.get(i - sub));
                }
            }
        }

        if(updates.size() > 0){
            this.saveOrUpdateBatch(updates);
        }

        if(deleteIds.size() > 0){
            this.removeByIds(deleteIds);
        }

        if(inserts.size() > 0){
            this.saveOrUpdateBatch(inserts);
        }

        return true;
    }

}
