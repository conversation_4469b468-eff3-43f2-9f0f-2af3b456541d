package cn.iocoder.yudao.module.mall.trade.controller.app.purchase.vo;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/6
 */
@Data
public class ProjectDetailRespVO {

    /**
     * 部门编号
     */
    private String departmentNo;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 项目编号
     */
    private String projectNo;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目类型
     */
    private String projectType;

    /**
     * 项目类型名称
     */
    private String projectTypeName;

    /**
     * 是否国库
     * 1:是 0:否
     */
    private String isTreasury ;

    /**
     * 国库信息码
     */
    private String treasuryNo;

    /**
     * 项目大类 01-预算 02-科研 03-专项 04-基建 05-收入 06-基金 07-代管 99-其他
     */
    private String projectClass;

    /**
     * 主负责人编号
     */
    private String chargeNo;

    /**
     * 主负责人姓名
     */
    private String chargeName;

    /**
     * 允许支出经济分类 逗号分割
     */
    private String economyClass;

    /**
     * 不允许支出经济分类 逗号分割
     */
    private String noEconomyClass;

    /**
     * 财政支出
     */
    private String isFinanceExpense;

    /**
     * 是否启用 1:是 0:否
     */
    private String isEnable;

    /**
     * 绑定的商品分类
     */
    private List<ProjectSkuCategoryVO> skuCategoryList;

    /**
     * 绑定的商品SKU
     */
    private List<String> skuIdList;

    /**
     * 总余额阈值，默认为0
     */
    private Integer totalBalanceThreshold;

    /**
     * 授权余额阈值，默认为0
     */
    private Integer authorizedBalanceThreshold;


    private static final String CACHE_KEY_TPL = "project-scope:%d:%s:%s:%s";
    private static final String LOCK_KEY_TPL = "lock:project-scope:%d:%s:%s:%s";

    @JsonIgnore
    public static String generateCacheKey(String projectNo, String projectDeptNo, String userNo) {
        return String.format(CACHE_KEY_TPL, TenantContextHolder.getTenantId(), projectNo, projectDeptNo, userNo);
    }

    @JsonIgnore
    public static String generateLockKey(String projectNo, String projectDeptNo, String userNo) {
        return String.format(LOCK_KEY_TPL, TenantContextHolder.getTenantId(), projectNo, projectDeptNo, userNo);
    }

}
