package cn.iocoder.yudao.module.mall.product.controller.admin.vopcategorymapping;

import cn.iocoder.yudao.module.mall.util.fronttask.AsyncFrontTask;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncFrontTaskContext;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncTaskTypeEnum;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.operatelog.core.annotations.OperateLog;
import static cn.iocoder.yudao.framework.operatelog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.mall.product.controller.admin.vopcategorymapping.vo.*;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.vopcategorymapping.VopCategoryMappingDO;
import cn.iocoder.yudao.module.mall.product.convert.vopcategorymapping.VopCategoryMappingConvert;
import cn.iocoder.yudao.module.mall.product.service.vopcategorymapping.VopCategoryMappingService;
import org.springframework.web.multipart.MultipartFile;

@Tag(name = "管理后台 - 京东分类映射")
@RestController
@RequestMapping("/mall/vop-category-mapping")
@Validated
public class VopCategoryMappingController {

    @Resource
    private VopCategoryMappingService vopCategoryMappingService;

    @PostMapping("/create")
    @Operation(summary = "创建京东分类映射")
    @PreAuthorize("@ss.hasPermission('mall:vop-category-mapping:create')")
    public CommonResult<Long> createVopCategoryMapping(@Valid @RequestBody VopCategoryMappingCreateReqVO createReqVO) {
        return success(vopCategoryMappingService.createVopCategoryMapping(createReqVO, null, null));
    }

    @PutMapping("/update")
    @Operation(summary = "更新京东分类映射")
    @PreAuthorize("@ss.hasPermission('mall:vop-category-mapping:update')")
    public CommonResult<Boolean> updateVopCategoryMapping(@Valid @RequestBody VopCategoryMappingUpdateReqVO updateReqVO) {
        vopCategoryMappingService.updateVopCategoryMapping(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除京东分类映射")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('mall:vop-category-mapping:delete')")
    public CommonResult<Boolean> deleteVopCategoryMapping(@RequestParam("id") Long id) {
        vopCategoryMappingService.deleteVopCategoryMapping(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得京东分类映射")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mall:vop-category-mapping:query')")
    public CommonResult<VopCategoryMappingRespVO> getVopCategoryMapping(@RequestParam("id") Long id) {
        VopCategoryMappingDO vopCategoryMapping = vopCategoryMappingService.getVopCategoryMapping(id);
        return success(VopCategoryMappingConvert.INSTANCE.convert(vopCategoryMapping));
    }

    @GetMapping("/list")
    @Operation(summary = "获得京东分类映射列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('mall:vop-category-mapping:query')")
    public CommonResult<List<VopCategoryMappingRespVO>> getVopCategoryMappingList(@RequestParam("ids") Collection<Long> ids) {
        List<VopCategoryMappingDO> list = vopCategoryMappingService.getVopCategoryMappingList(ids);
        return success(VopCategoryMappingConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得京东分类映射分页")
    @PreAuthorize("@ss.hasPermission('mall:vop-category-mapping:query')")
    public CommonResult<PageResult<VopCategoryMappingRespVO>> getVopCategoryMappingPage(@Valid VopCategoryMappingPageReqVO pageVO) {
        PageResult<VopCategoryMappingDO> pageResult = vopCategoryMappingService.getVopCategoryMappingPage(pageVO);
        return success(VopCategoryMappingConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出京东分类映射 Excel")
    @PreAuthorize("@ss.hasPermission('mall:vop-category-mapping:export')")
    @OperateLog(type = EXPORT)
    public void exportVopCategoryMappingExcel(@Valid VopCategoryMappingExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<VopCategoryMappingDO> list = vopCategoryMappingService.getVopCategoryMappingList(exportReqVO);
        // 导出 Excel
        List<VopCategoryMappingExcelVO> datas = VopCategoryMappingConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "京东分类映射.xls", "数据", VopCategoryMappingExcelVO.class, datas);
    }


    /**
     * 获得京东分类映射导入模板
     * @param response
     * @throws IOException
     */
    @GetMapping("/get-import-template")
    @PreAuthorize("@ss.hasPermission('mall:vop-category-mapping:import')")
    @Operation(summary = "获得京东分类映射导入模板")
    public void importTemplate(HttpServletResponse response) throws IOException {
        // 手动创建导出 demo
        List<VopCategoryMappingExcelVO> list = Arrays.asList(
                VopCategoryMappingExcelVO.builder()
                        .lastCategoryId(6120706L)
                        .lastCategoryName("示波器")
                        .fullCategoryId("6120000-6120700-6120706")
                        .fullCategoryName("科研仪器-电子与通信技术类仪器-示波器")
                        .vopLastCategoryId(33406L)
                        .vopLastCategoryName("示波器")
                        .vopFullCategoryId("9855-33381-33406")
                        .vopFullCategoryName("家装建材-仪器仪表-示波器")
                        .build()
        );
        // 输出
        ExcelUtils.write(response, "供应商商品sku导入.xls", "Sheet1", VopCategoryMappingExcelVO.class, list);
    }

    @PostMapping("/import")
    @Operation(summary = "导入京东分类映射 Excel")
    @PreAuthorize("@ss.hasPermission('mall:vop-category-mapping:import')")
    @AsyncFrontTask(taskType = AsyncTaskTypeEnum.PRODUCT_VOP_CATEGORY_MAPPING_IMPORT, returnRunningTaskId = true)
    public CommonResult<String> importExcel(@RequestParam("file") MultipartFile file) throws Exception {
        String taskId = AsyncFrontTaskContext.getTaskId();
        List<VopCategoryMappingExcelVO> list = ExcelUtils.read(file, VopCategoryMappingExcelVO.class);
        vopCategoryMappingService.importExcel(list);
        return success(taskId);
    }

}
