package cn.iocoder.yudao.module.mall.trade.service.aftersale;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
import cn.iocoder.yudao.framework.common.util.object.ObjectUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.sku.ProductSkuDO;
import cn.iocoder.yudao.module.mall.product.service.sku.ProductSkuService;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.aftersale.TradeAfterSaleDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderItemDO;
import cn.iocoder.yudao.module.mall.trade.dal.mysql.order.TradeOrderItemMapper;
import cn.iocoder.yudao.module.mall.trade.dal.mysql.order.TradeOrderMapper;
import cn.iocoder.yudao.module.mall.trade.enums.aftersale.TradeAfterSaleStatusEnum;
import cn.iocoder.yudao.module.mall.trade.enums.aftersale.TradeAfterSaleTypeEnum;
import cn.iocoder.yudao.module.mall.trade.enums.aftersale.TradeAfterSaleWayEnum;
import cn.iocoder.yudao.module.mall.trade.enums.order.TradeOrderItemAfterSaleStatusEnum;
import cn.iocoder.yudao.module.mall.trade.enums.order.TradeOrderStatusEnum;
import cn.iocoder.yudao.module.mall.trade.service.order.TradeOrderService;
import cn.iocoder.yudao.module.mall.trade.service.settle.OrderSettleService;
import cn.iocoder.yudao.module.mall.vop.service.messagehandle.messagevo.AfsWareVO;
import cn.iocoder.yudao.module.mall.vop.service.messagehandle.messagevo.OrderAfterSellComponentVO;
import cn.iocoder.yudao.module.mall.vop.service.messagehandle.messagevo.OrderApplyRefundCompleteVO;
import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertMap;

@Service
@Validated
@Slf4j
public class VopAfterSalesBridgeServiceImpl implements VopAfterSalesBridgeService {

    @Resource
    private TradeOrderMapper tradeOrderMapper;
    @Resource
    private TradeOrderService tradeOrderService;
    @Resource
    private ProductSkuService productSkuService;
    @Resource
    private TradeAfterSaleService tradeAfterSaleService;
    @Resource
    private TradeOrderItemMapper orderItemMapper;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private OrderSettleService orderSettleService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean afterSaleComponentOrder4Vop(OrderAfterSellComponentVO afterSellComponentVO) {
        if(!afterSellComponentVO.getOrderId().equals(afterSellComponentVO.getOriginalOrderId())) {
            log.info("售后换新单，原单号：{}，换新单号：{}", afterSellComponentVO.getOriginalOrderId(), afterSellComponentVO.getOrderId());
        }

        if(!afterSellComponentVO.getRefund()){
            log.info("订单{}服务单{}消息，步骤{}，不是退款消息，无需处理", afterSellComponentVO.getOriginalOrderId(),
                    afterSellComponentVO.getAfsServiceId(), afterSellComponentVO.getStepType());
            return true;
        }

        if(afterSellComponentVO.getRealRefundAmount().compareTo(BigDecimal.ZERO) == 0){
            log.info("订单{}服务单{}消息，步骤{}，退款金额为0，无需处理", afterSellComponentVO.getOriginalOrderId(),
                    afterSellComponentVO.getAfsServiceId(), afterSellComponentVO.getStepType());
            return true;
        }

        String key = String.format("lock:afterSaleComponentOrder4Vop:%d", afterSellComponentVO.getOriginalOrderId());
        RLock lock = redissonClient.getLock(key);
        boolean lockAcquired = false;
        log.info("afterSaleComponentOrder4Vop:获取锁，{}", afterSellComponentVO.getOriginalOrderId());

        try {
            try{
                lockAcquired = lock.tryLock(60, 180, TimeUnit.SECONDS);
                if(!lockAcquired) {
                    log.error("afterSaleComponentOrder4Vop:获取锁失败");
                    return false;
                }
            }
            catch (Exception ex){
                log.error("afterSaleComponentOrder4Vop:获取锁失败", ex);
                return false;
            }

            // 如果售后单已经存在，则不重复处理
            List<TradeAfterSaleDO> tradeAfterSaleDOS = tradeAfterSaleService.list(new LambdaQueryWrapperX<TradeAfterSaleDO>()
                    .eq(TradeAfterSaleDO::getServiceNo, String.valueOf(afterSellComponentVO.getAfsServiceId())));
            if(tradeAfterSaleDOS != null && !tradeAfterSaleDOS.isEmpty()){
                log.info("订单{}的售后单{}已处理，不需要进行重复处理", afterSellComponentVO.getOriginalOrderId(), (afterSellComponentVO.getAfsServiceId()));
                return true;
            }

            log.info("VOP售后组件节点消息处理：jd OrderId:{}", afterSellComponentVO.getOriginalOrderId());
            TradeOrderDO tradeOrderDO = tradeOrderMapper.selectOne(TradeOrderDO::getThirdOrderId, afterSellComponentVO.getOriginalOrderId().toString());
            if(tradeOrderDO == null) {
                log.error("VOP售后jd OrderId:{}找不到对应订单", afterSellComponentVO.getOriginalOrderId());
                return false;
            }

            // 订单状态、订单item状态、订单退款金额
            // 如果订单状态为取消状态，则不需要进行重复处理
            if(ObjectUtils.equalsAny(tradeOrderDO.getStatus(), TradeOrderStatusEnum.CANCELED.getStatus())) {
                log.info("订单{}已取消，不需要进行重复处理", afterSellComponentVO.getOriginalOrderId());
                return true;
            }

            List<TradeOrderItemDO> itemList = tradeOrderService.getOrderItemListByOrderId(tradeOrderDO.getId());
            if(CollUtil.isEmpty(itemList)) {
                log.error("订单{}查询不正确，查询不到order item", tradeOrderDO.getId());
                return false;
            }

            // 清理发票信息
            orderSettleService.clearInvoice(Arrays.asList(tradeOrderDO.getId()));

            // 退款完成逻辑处理
            return handleAfterSaleComplete(afterSellComponentVO, tradeOrderDO, itemList);
        }
        finally {
            // 确保无论事务提交还是回滚都释放锁
            if (lockAcquired) {
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCompletion(int status) {
                        try {
                            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                                log.info("afterSaleComponentOrder4Vop:释放锁，{}", afterSellComponentVO.getOriginalOrderId());
                                lock.unlock();
                            }
                        } catch (Exception e) {
                            log.error("afterSaleComponentOrder4Vop释放锁异常:{}", afterSellComponentVO.getOriginalOrderId(), e);
                        }
                    }
                });
            }
        }
    }

    /**
     * 售后完成场景的逻辑处理：
     * 1，订单明细的售后状态更新，内部会自动处理订单的售后状态
     * 2，创建售后单；
     * 3，外部系统下游逻辑处理，如：业财合同的售后单处理；
     * @param afterSellComponentVO
     * @param orderDO
     * @param itemList
     */
    private Boolean handleAfterSaleComplete(OrderAfterSellComponentVO afterSellComponentVO, TradeOrderDO orderDO, List<TradeOrderItemDO> itemList) {
        // 退货的京东商品和数量，过滤掉赠品，有可能出现赠品也需要售后，也需要退款，同一个sku既是赠品，又不是赠品的，直接把赠品过滤掉，保留不是赠品的
        List<AfsWareVO> notAnnexAfsWareVOS = afterSellComponentVO.getWares().stream().filter(afsWareVO -> !afsWareVO.getAnnex()).collect(Collectors.toList());
        List<String> notAnnexJdSkuIds = notAnnexAfsWareVOS.stream().map(AfsWareVO::getWareId).map(Object::toString).collect(Collectors.toList());
        List<ProductSkuDO> skuDOS = productSkuService.getSkuByIdOrInnerIds(notAnnexJdSkuIds);
        // 售后商品是否已入池，如果未入池（比如赠品），则从orderItem中获取信息，并构造productSkuDO对象
        for (AfsWareVO notAnnexAfsWareVO : notAnnexAfsWareVOS) {
            boolean notFound = true;
            for (ProductSkuDO skuDO : skuDOS) {
                if(skuDO.getSkuInnerId().equals(notAnnexAfsWareVO.getWareId().toString())){
                    notFound = false;
                    break;
                }
            }

            if(notFound){
                log.info("订单{}售后商品{}未入池，从orderItem中获取信息", orderDO.getNo(), notAnnexAfsWareVO.getWareId());
                // 未入池商品，从orderItem中获取信息
                for (TradeOrderItemDO tradeOrderItemDO : itemList) {
                    if(notAnnexAfsWareVO.getWareId().toString().equals(tradeOrderItemDO.getSkuInnerId())){
                        ProductSkuDO productSkuDO = new ProductSkuDO()
                                .setId(tradeOrderItemDO.getSkuId())
                                .setSkuInnerId(tradeOrderItemDO.getSkuInnerId())
                                .setSkuName(tradeOrderItemDO.getSkuName())
                                .setSpuId(tradeOrderItemDO.getSkuId())
                                .setSpuName(tradeOrderItemDO.getSkuName())
                                .setPicUrl(tradeOrderItemDO.getPicUrl())
                                .setSupplierId(tradeOrderItemDO.getSupplierId())
                                .setSupplierName(orderDO.getSupplierName())
                                .setSupplierType(orderDO.getSupplierType());
                        skuDOS.add(productSkuDO);
                        break;
                    }
                }
            }
        }
        Assert.equals(notAnnexAfsWareVOS.size(), skuDOS.size(), "订单{}售后商品数量不一致", orderDO.getNo());

        Map<Long, AfsWareVO> notAnnexAfsWareMap = new HashMap<>();
        for (AfsWareVO notAnnexAfsWareVO : notAnnexAfsWareVOS) {
            for (ProductSkuDO skuDO : skuDOS) {
                if(skuDO.getSkuInnerId().equals(String.valueOf(notAnnexAfsWareVO.getWareId()))){
                    notAnnexAfsWareMap.put(skuDO.getId(), notAnnexAfsWareVO);
                    break;
                }
            }
        }
        Map<Long, ProductSkuDO> productMap = convertMap(skuDOS, ProductSkuDO::getId);
        Map<Long, TradeOrderItemDO> tradeOrderItemDOMap = convertMap(itemList, TradeOrderItemDO::getSkuId);

        String afterSaleMemo = afterSellComponentVO.getQuestionDesc();
        if(StringUtils.isNotBlank(afterSaleMemo) && afterSaleMemo.length() > 300) {
            afterSaleMemo = afterSaleMemo.substring(0, 300);
        }

        // 根据京东售后单号处理退款金额，可能存在多批，比如一个订单，两个商品，其中一个商品分多次退货
        List<TradeAfterSaleDO> afterSaleDOList = new ArrayList<>();
        // 更新订单item状态为已退款
        for (Long skuId : notAnnexAfsWareMap.keySet()) {
            AfsWareVO afsWareVO = notAnnexAfsWareMap.get(skuId);
            ProductSkuDO skuDO = productMap.get(skuId);
            TradeOrderItemDO orderItem = tradeOrderItemDOMap.get(skuId);
            Assert.notNull(orderItem, "订单{} item不存在，对应sku为：{}，innerId：{}", orderDO.getNo(), skuDO.getId(), skuDO.getSkuInnerId());

            orderItemMapper.update(null, new LambdaUpdateWrapper<TradeOrderItemDO>()
                    .eq(TradeOrderItemDO::getId, orderItem.getId())
                    .set(TradeOrderItemDO::getAfterSaleStatus, TradeOrderItemAfterSaleStatusEnum.APPLY.getStatus()));
            // 创建内部售后单
            afterSaleDOList.add(createLocalAfterSale(orderDO, skuDO, orderItem, afterSellComponentVO.getRealRefundAmount(),
                    afsWareVO.getWareNum(), afterSaleMemo, String.valueOf(afterSellComponentVO.getAfsServiceId())));
        }

        if(CollUtil.isNotEmpty(afterSaleDOList)) {
            tradeAfterSaleService.saveBatch(afterSaleDOList);

            // 业财售后处理
            afterSaleDOList.forEach(afterSaleDO -> {
                log.info("开始处理订单{}的售后单：{}", afterSaleDO.getOrderId(), afterSaleDO.getNo());
                tradeAfterSaleService.completeAfterSale4JD(afterSaleDO);
            });
        }

        return true;
    }

    private TradeAfterSaleDO createLocalAfterSale(TradeOrderDO tradeOrderDO,
                                      ProductSkuDO skuDO, TradeOrderItemDO orderItemDO, BigDecimal refundPrice, Integer count, String afterSaleMemo, String serviceNo) {
        // 根据退款商品数量计算退款金额，可能存在不准的情况
        TradeAfterSaleDO tradeAfterSaleDO = new TradeAfterSaleDO()
                .setNo(IdUtil.getSnowflakeNextIdStr())
                .setOrderNo(tradeOrderDO.getNo())
                .setOrderId(tradeOrderDO.getId())
                .setUserName(tradeOrderDO.getUserName())
                .setUserId(tradeOrderDO.getUserId())
                .setApplyDescription(afterSaleMemo)
                // 与商家协商一致退款
                .setApplyReason("100")
                .setStatus(TradeAfterSaleStatusEnum.WAIT_REFUND.getStatus())
                .setOrderItemId(orderItemDO.getId())
                .setSkuId(orderItemDO.getSkuId())
                .setSpuId(skuDO.getSpuId())
                .setSpuName(skuDO.getSpuName())
                .setPayOrderId(tradeOrderDO.getPayOrderId())
                .setSupplierId(skuDO.getSupplierId())
                .setSupplierName(skuDO.getSupplierName())
                .setSupplierType(skuDO.getSupplierType())
                .setPicUrl(skuDO.getPicUrl())
                .setRefundPrice(refundPrice)
                .setCount(count)
                .setType(TradeOrderStatusEnum.isCompleted(tradeOrderDO.getStatus()) ? TradeAfterSaleTypeEnum.AFTER_SALE.getType() : TradeAfterSaleTypeEnum.IN_SALE.getType())
                .setRefundTime(LocalDateTime.now())
                .setServiceNo(serviceNo);
        // 处理支付信息
        if(tradeOrderDO.getPayed() != null && tradeOrderDO.getPayed()) {
            Long payOrderId = tradeOrderDO.getPayOrderId();
            if(tradeOrderDO.getPayOrderId() == null && tradeOrderDO.getParentOrderId() != null) {
                // 向取找父订单的支付单ID
                TradeOrderDO parentOrder = tradeOrderService.getOrder(tradeOrderDO.getUserId(), tradeOrderDO.getParentOrderId());
                payOrderId = parentOrder.getPayOrderId();
            }
            tradeAfterSaleDO.setPayOrderId(payOrderId);
        }

        // 售后方式
        if(count == 0) {
            tradeAfterSaleDO.setWay(TradeAfterSaleWayEnum.REFUND.getWay());
        } else {
            tradeAfterSaleDO.setWay(TradeAfterSaleWayEnum.RETURN_AND_REFUND.getWay());
        }

        return tradeAfterSaleDO;
    }

    @Override
    public boolean handleApplyRefundComplete(OrderApplyRefundCompleteVO applyRefundCompleteVO){
        TradeOrderDO tradeOrderDO = tradeOrderMapper.selectOne(TradeOrderDO::getThirdOrderId, applyRefundCompleteVO.getOriginalOrderId());
        if(tradeOrderDO == null) {
            log.error("VOP退款完成订单OrderId:{}找不到", applyRefundCompleteVO.getOriginalOrderId());
            return false;
        }

        BigDecimal refundAmount = tradeOrderDO.getRefundPrice();
        if (tradeOrderDO.getRefundPrice() == null) {
            refundAmount = BigDecimal.ZERO;
        }

        if(applyRefundCompleteVO.getRefundAmount().compareTo(refundAmount) != 0){
            log.error("VOP完成消息，订单{}退款金额不一致，京东退款金额：{}， 我们退款金额：{}", tradeOrderDO.getNo(), applyRefundCompleteVO.getRefundAmount(), refundAmount);
            return false;
        }
        return true;
    }
}
