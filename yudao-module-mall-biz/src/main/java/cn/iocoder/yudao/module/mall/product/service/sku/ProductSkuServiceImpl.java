package cn.iocoder.yudao.module.mall.product.service.sku;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.servlet.ServletUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.redis.util.RedisUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.annotation.ProductSkuCheck;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.supplier.SupplierDO;
import cn.iocoder.yudao.module.mall.basis.service.basisconfig.BasisConfigService;
import cn.iocoder.yudao.module.mall.basis.service.productrule.ProductRuleService;
import cn.iocoder.yudao.module.mall.basis.service.supplier.SupplierService;
import cn.iocoder.yudao.module.mall.enums.basis.SupplierStatusEnum;
import cn.iocoder.yudao.module.mall.enums.basis.SupplierTypeEnum;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.product.AppOpenSkuUpdatePriceReqVO;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.product.AppOpenSkuUpdateStatusReqVO;
import cn.iocoder.yudao.module.mall.mq.producer.product.ProductSkuProducer;
import cn.iocoder.yudao.module.mall.product.api.sku.dto.ProductSkuUpdateStockReqDTO;
import cn.iocoder.yudao.module.mall.product.controller.admin.productstatistics.vo.SkuCountCategorySummaryRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.productstatistics.vo.SkuSummaryRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.productstatistics.vo.SupplierSkuSummaryRespVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.sku.vo.*;
import cn.iocoder.yudao.module.mall.product.controller.admin.skustock.vo.ProductSkuStockCreateReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.spec.vo.ProductSpecValueReqVO;
import cn.iocoder.yudao.module.mall.product.controller.admin.spec.vo.ProductSpecValueRespVO;
import cn.iocoder.yudao.module.mall.product.controller.app.sku.vo.*;
import cn.iocoder.yudao.module.mall.product.controller.app.spec.vo.AppProductSpecValueVO;
import cn.iocoder.yudao.module.mall.product.controller.app.spu.vo.ProductSpuBaseRespOpenVO;
import cn.iocoder.yudao.module.mall.product.controller.app.spu.vo.ProductSpuOpenRespVO;
import cn.iocoder.yudao.module.mall.product.controller.app.spu.vo.ProductSpuOpenVO;
import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.vo.VopGoodsDetailResp;
import cn.iocoder.yudao.module.mall.product.controller.app.vopgoods.vo.VopSkuGoodsPageItem;
import cn.iocoder.yudao.module.mall.product.convert.es.ProductSkuESConvert;
import cn.iocoder.yudao.module.mall.product.convert.sku.ProductSkuConvert;
import cn.iocoder.yudao.module.mall.product.convert.skustock.ProductSkuStockConvert;
import cn.iocoder.yudao.module.mall.product.convert.spec.ProductSpecConvert;
import cn.iocoder.yudao.module.mall.product.convert.spu.ProductSpuConvert;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ConfigProductCategorySpecDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.category.ProductCategoryDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.sku.ProductSkuDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skusearch.ProductSkuES;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.skustock.ProductSkuStockDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.spec.ProductSkuSpecDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.spec.ProductSpuSpecDO;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.spu.ProductSpuDO;
import cn.iocoder.yudao.module.mall.product.dal.mysql.sku.ProductSkuMapper;
import cn.iocoder.yudao.module.mall.product.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.mall.product.enums.es.SortTypeEnum;
import cn.iocoder.yudao.module.mall.product.enums.spu.ProductSkuSeoStatusEnum;
import cn.iocoder.yudao.module.mall.product.enums.spu.ProductSkuShowStatusEnum;
import cn.iocoder.yudao.module.mall.product.enums.spu.ProductSpuSpecTypeEnum;
import cn.iocoder.yudao.module.mall.product.enums.spu.ProductSpuStatusEnum;
import cn.iocoder.yudao.module.mall.product.service.brand.ProductBrandService;
import cn.iocoder.yudao.module.mall.product.service.category.ConfigProductCategorySpecService;
import cn.iocoder.yudao.module.mall.product.service.category.ProductCategoryService;
import cn.iocoder.yudao.module.mall.product.service.skusearch.ProductSkuElasticsearchService;
import cn.iocoder.yudao.module.mall.product.service.skustock.ProductSkuStockService;
import cn.iocoder.yudao.module.mall.product.service.spec.ProductSkuSpecService;
import cn.iocoder.yudao.module.mall.product.service.spec.ProductSpuSpecService;
import cn.iocoder.yudao.module.mall.product.service.spu.ProductSpuService;
import cn.iocoder.yudao.module.mall.product.service.tag.ProductTagSkuService;
import cn.iocoder.yudao.module.mall.product.service.vopgoods.VopGoodsBridgeService;
import cn.iocoder.yudao.module.mall.trade.controller.app.order.vo.TradeOrderItemBaseReqVO;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncFrontTaskContext;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncFrontTaskUtils;
import cn.iocoder.yudao.module.mall.util.fronttask.AsyncTaskTypeEnum;
import cn.iocoder.yudao.module.mall.util.fronttask.vo.AsyncImportRespExcelVO;
import cn.iocoder.yudao.module.mall.util.fronttask.vo.AsyncImportTask;
import cn.iocoder.yudao.module.mall.vop.apiserver.VopGoodsService;
import cn.iocoder.yudao.module.mall.vop.dal.dataobject.VopConfigDO;
import cn.iocoder.yudao.module.mall.vop.dal.dataobject.VopSkuCategoryDO;
import cn.iocoder.yudao.module.mall.vop.req.VopGoodsBrotherSkuReq;
import cn.iocoder.yudao.module.mall.vop.req.VopGoodsGetSkuDetailReq;
import cn.iocoder.yudao.module.mall.vop.service.VopConfigService;
import cn.iocoder.yudao.module.mall.vop.service.VopSkuCategoryService;
import com.alibaba.ttl.TtlRunnable;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSellPrice.GetSellPriceGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSimilarSkuList.GetSimilarSkuGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSkuDetailInfo.CategoryAttributeGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSkuDetailInfo.GetSkuPoolInfoGoodsResp;
import com.jd.open.api.sdk.domain.vopsp.SkuInfoGoodsProvider.response.getSkuDetailInfo.OpenRpcResult;
import com.jd.open.api.sdk.response.vopsp.VopGoodsGetSimilarSkuListResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.StopWatch;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.*;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;
import static cn.iocoder.yudao.module.mall.basis.dal.redis.RedisKeyConstants.MALL_PRODUCT_BROTHER_SKUS;
import static cn.iocoder.yudao.module.mall.basis.dal.redis.RedisKeyConstants.MALL_PRODUCT_DETAIL;
import static cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants.*;
import static cn.iocoder.yudao.module.mall.product.enums.ErrorCodeConstants.*;

/**
 * 商品 SKU Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ProductSkuServiceImpl extends ServiceImpl<ProductSkuMapper, ProductSkuDO> implements ProductSkuService {

    @Resource
    private ProductSkuMapper productSkuMapper;
    @Resource
    @Lazy
    private ProductSpuService productSpuService;
    @Resource
    private ConfigProductCategorySpecService configCategorySpecService;
    @Resource
    @Lazy
    private ProductCategoryService categoryService;
    @Resource
    private ProductSpuSpecService productSpuSpecService;
    @Resource
    private ProductSkuSpecService productSkuSpecService;
    @Resource
    private ProductSkuStockService productSkuStockService;
    @Resource
    @Lazy
    private SupplierService supplierService;
    @Resource
    private VopGoodsService vopGoodsService;
    @Resource
    @Lazy
    private VopGoodsBridgeService vopGoodsBridgeService;
    @Resource
    @Lazy
    private ProductSkuElasticsearchService productSkuElasticsearchService;
    @Resource
    private ProductBrandService brandService;
    @Resource
    private ProductSkuProducer productSkuProducer;
    @Resource
    private BasisConfigService basisConfigService;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private AsyncFrontTaskUtils asyncFrontTaskUtils;
    @Resource
    private ProductTagSkuService productTagSkuService;
    @Resource
    private ProductRuleService productRuleService;
    @Resource
    private ProductCategoryService productCategoryService;
    @Resource
    private VopSkuCategoryService vopSkuCategoryService;
    @Resource
    private VopConfigService vopConfigService;

    @Value("${jd.image-path:https://img13.360buyimg.com/n12/}")
    private String jdImagePath;

    public static final String  DEFAULT_AREA_IDS = "17,1381,50718,53772";

    private void syncProductIndex(Long... skuId) {
        syncProductIndex(false, skuId);
    }

    private void syncProductIndex(boolean afterTrx, Long... skuId) {
        try {
            if(skuId.length > 0) {
                if(!afterTrx) {
                    productSkuProducer.sendProductSkuIndexSync(Arrays.asList(skuId));
                } else {
                    TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                        @Override
                        public void afterCommit() {
                            try {
                                productSkuProducer.sendProductSkuIndexSync(Arrays.asList(skuId));
                            } catch (Exception e) {
                                log.error("商品SKU生成索引异常:{}", skuId, e);
                            }
                        }
                    });
                }
            }
        } catch (Exception e) {
            log.error("商品SKU生成索引异常:{}", skuId, e);
        }
    }

    private void syncProductIndex(List<Long> skuIds) {
        syncProductIndex(skuIds, false);
    }

    private void syncProductIndex(List<Long> skuIds, boolean afterTrx) {
        try {
            if(CollUtil.isNotEmpty(skuIds)) {
                if(!afterTrx) {
                    productSkuProducer.sendProductSkuIndexSync(skuIds);
                } else {
                    TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                        @Override
                        public void afterCommit() {
                            try {
                                productSkuProducer.sendProductSkuIndexSync(skuIds);
                            } catch (Exception e) {
                                log.error("商品SKU生成索引异常:{}", skuIds, e);
                            }
                        }
                    });
                }
            }
        } catch (Exception e) {
            log.error("商品SKU生成索引异常:{}", skuIds, e);
        }
    }

    @Override
    public void deleteSku(Long id) {
        // 校验存在
        ProductSkuDO skuDO = validateSkuExists(id);
        // 删除
        productSkuMapper.deleteById(id);
        syncProductIndex(id);
        // 删除缓存
        deleteSkuCache(skuDO.getSupplierId(), skuDO.getId());
    }

    private ProductSkuDO validateSkuExists(Long id) {
        ProductSkuDO skuDO = productSkuMapper.selectById(id);
        if (skuDO == null) {
            throw exception(SKU_NOT_EXISTS);
        }

        return skuDO;
    }

    @Override
    public ProductSkuDO getSku(Long id) {
        return productSkuMapper.selectById(id);
    }

    @Override
    public ProductSkuDO getSku(Long id, Long supplierId) {
        return productSkuMapper.selectOne(ProductSkuDO::getId, id, ProductSkuDO::getSupplierId, supplierId);
    }

    @Override
    public ProductSkuRespVO getSkuDetail(Long id) {
        ProductSkuDO skuDO = productSkuMapper.selectById(id);

        List<ProductSkuSpecDO> skuSpecValueList = productSkuSpecService.getProductSkuSpecBySkuId(id);
        ProductSkuStockDO skuStock = productSkuStockService.getSkuStock(id);
        ProductSkuRespVO skuRespVO = ProductSkuConvert.INSTANCE.convert03(skuDO, skuStock);

        List<ProductSpecValueRespVO> specValueVOList = ProductSpecConvert.INSTANCE.convertVOList05(skuSpecValueList);
        skuRespVO.setSpecValueList(specValueVOList);

        return skuRespVO;
    }

    @Override
    public List<ProductSkuRespVO> getSkuDetailListBySpu(Long spuId) {
        List<ProductSkuDO> skuDOList = productSkuMapper.selectListBySpuId(spuId);
        List<ProductSkuRespVO> skuRespVOList = new ArrayList<>();
        skuDOList.forEach(sku -> {
            List<ProductSkuSpecDO> skuSpecValueList = productSkuSpecService.getProductSkuSpecBySkuId(sku.getId());
            ProductSkuStockDO skuStock = productSkuStockService.getSkuStock(sku.getId());
            ProductSkuRespVO skuRespVO = ProductSkuConvert.INSTANCE.convert03(sku, skuStock);

            List<ProductSpecValueRespVO> specValueVOList = ProductSpecConvert.INSTANCE.convertVOList05(skuSpecValueList);
            skuRespVO.setSpecValueList(specValueVOList);
            skuRespVOList.add(skuRespVO);
        });

        return skuRespVOList;
    }

    @Override
    public ProductSkuDO getSimpleSkuById(Long skuId) {
        return productSkuMapper.selectOne(Wrappers.lambdaQuery(ProductSkuDO.class)
                .select(ProductSkuDO::getId, ProductSkuDO::getSpuId, ProductSkuDO::getSkuInnerId, ProductSkuDO::getSupplierId, ProductSkuDO::getSupplierType, ProductSkuDO::getSpuName, ProductSkuDO::getStatus, ProductSkuDO::getPlatformStatus)
                .eq(ProductSkuDO::getId, skuId));
    }

    @Override
    public ProductSkuDO getSimpleSkuByInnerIdAndSupplierId(String innerId, Long supplierId) {
        return productSkuMapper.selectOne(Wrappers.lambdaQuery(ProductSkuDO.class)
                .select(ProductSkuDO::getId, ProductSkuDO::getSpuId, ProductSkuDO::getSkuInnerId, ProductSkuDO::getSpuName, ProductSkuDO::getStatus, ProductSkuDO::getPlatformStatus)
                .eq(ProductSkuDO::getSkuInnerId, innerId)
                .eq(ProductSkuDO::getSupplierId, supplierId)
                .orderByAsc(ProductSkuDO::getCreateTime)
                .last(" limit 1 "));
    }

    @Override
    public List<ProductSkuDO> getSkuListByInnerIdAndSupplierId(List<String> innerIds, Long supplierId) {
        if(CollUtil.isEmpty(innerIds)) {
            return Lists.newArrayList();
        }
        return productSkuMapper.selectList(Wrappers.lambdaQuery(ProductSkuDO.class).in(ProductSkuDO::getSkuInnerId, innerIds).eq(ProductSkuDO::getSupplierId, supplierId));
    }

    @Override
    public List<ProductSkuDO> getSkuList() {
        return productSkuMapper.selectList();
    }

    @Override
    public List<ProductSkuDO> getSkuList(Collection<Long> ids) {
        if(CollectionUtils.isNotEmpty(ids)) {
            return productSkuMapper.selectBatchIds(ids);
        }
        return null;
    }

    @Override
    public List<String> getInnerIdsBySupplierId(Long supplierId) {
        LambdaQueryWrapper<ProductSkuDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(ProductSkuDO::getSkuInnerId);
        wrapper.eq(ProductSkuDO::getSupplierId, supplierId);
        return productSkuMapper.selectObjs(wrapper)
                .stream()
                .map(element -> (String) element)
                .collect(Collectors.toList());
    }

    @Override
    public void updatePrice(List<ProductSkuPriceReqVO> skus) {
        if (CollUtil.isNotEmpty(skus)) {
            ProductSkuDO skuDO_1 = productSkuMapper.selectById(skus.get(0).getId());

            for(ProductSkuPriceReqVO sku : skus){
                validProductRule(sku.getId().toString(), sku.getSalePrice());
            }

            ProductSpuDO productSpu = new ProductSpuDO();
            productSpu.setId(skuDO_1.getSpuId());
            BigDecimal spuPrice = getMinSalePrice2(skus);
            productSpu.setSalePrice(spuPrice);

            productSpuService.update(null, Wrappers.lambdaUpdate(ProductSpuDO.class).
                    eq(ProductSpuDO::getId, productSpu.getId()).set(ProductSpuDO::getSalePrice, productSpu.getSalePrice()));

            List<ProductSkuDO> skuDOs = skus.stream().map(sku -> {
                ProductSkuDO skuDO = new ProductSkuDO();
                skuDO.setId(sku.getId());
                skuDO.setSalePrice(sku.getSalePrice());
                return skuDO;
            }).collect(Collectors.toList());

            productSkuMapper.updateBatch(skuDOs, 20);

            syncProductIndex(skuDOs.stream().map(ProductSkuDO::getId).collect(Collectors.toList()));
        }
    }

    @Override
    public boolean saveOrUpdate(ProductSkuDO entity) {
        syncProductIndex(entity.getId());
        return super.saveOrUpdate(entity);
    }

    @Override
    public boolean updateById(ProductSkuDO entity) {
        syncProductIndex(entity.getId());
        // 删除缓存
        deleteSkuCache(entity.getSupplierId(), entity.getId());
        return super.updateById(entity);
    }

    @Override
    public void updateStock(List<ProductSkuStockReqVO> skus) {
        if (CollUtil.isNotEmpty(skus)) {
            Map<Long, Integer> ajustStockMap = skus.stream().collect(Collectors.toMap(ProductSkuStockReqVO::getId, ProductSkuStockReqVO::getAdjustStock));
            List<ProductSkuStockDO> stockDOs = new ArrayList<>();
            Map<Long,ProductSkuStockDO> stockMap = new HashMap<>();
            ajustStockMap.entrySet().forEach(entry -> {
                ProductSkuStockDO stockDO = productSkuStockService.getSkuStock(entry.getKey());
                Integer stock = (stockDO.getStock() + entry.getValue());
                stock = stock < 0 ? 0 : stock;
                if (stockDO != null) {
                    stockDO.setStock(stock);
                    stockDOs.add(stockDO);
                    stockMap.put(stockDO.getSkuId(), stockDO);
                } else {
                    ProductSkuDO skuDO = productSkuMapper.selectById(entry.getKey());
                    ProductSkuStockCreateReqVO stockVO = new ProductSkuStockCreateReqVO();

                    stockVO.setSupplierId(skuDO.getSupplierId())
                            .setWarnStock(0)
                            .setReserveStock(0)
                            .setStock(stock)
                            .setSkuId(skuDO.getId());

                    productSkuStockService.createSkuStock(stockVO);
                }
            });

            productSkuStockService.batchUpdateSkuStock(stockDOs);

            syncProductIndex(skus.stream().map(ProductSkuStockReqVO::getId).collect(Collectors.toList()));
        }
    }

    @Override
    public void updateSeoInfo(List<ProductSkuSeoUpdateReqVO> skus) {
        if (CollUtil.isNotEmpty(skus)) {
            List<ProductSkuDO> skuDOs = skus.stream().map(sku -> {
                ProductSkuDO skuDO = new ProductSkuDO();
                skuDO.setId(sku.getId());
                skuDO.setInitSalesCount(sku.getInitSalesCount());
                return skuDO;
            }).collect(Collectors.toList());

            productSkuMapper.updateBatch(skuDOs, 100);
            syncProductIndex(skuDOs.stream().map(ProductSkuDO::getId).collect(Collectors.toList()));
        }
    }

    @Override
    public void validateSkuList(List<ProductSkuCreateOrUpdateReqVO> skus, Integer specType) {
        // 非多规格，不需要校验
        if (ObjectUtil.notEqual(specType, ProductSpuSpecTypeEnum.Single.getType())) {
            return;
        }

        // 1、校验属性项存在
        Set<Long> specIds = skus.stream().filter(p -> p.getSpecValueList() != null)
                .flatMap(p -> p.getSpecValueList().stream()) // 遍历多个 规格值
                .map(ProductSpecValueReqVO::getSpecId) // 将每个 规格值 转换成对应的 specId，最后形成集合
                .filter(spId -> spId > 0)
                .collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(specIds)) {
            List<ConfigProductCategorySpecDO> specList = configCategorySpecService.getSpecList(specIds);
            if (specList.size() != specIds.size()) {
                throw exception(PROPERTY_NOT_EXISTS);
            }
        }

        // 2. 再校验，每个 Sku 的属性值的数量，是一致的。
        if(CollUtil.isNotEmpty(skus.get(0).getSpecValueList())) {
            int attrValueIdsSize = skus.get(0).getSpecValueList().size();
            for (int i = 1; i < skus.size(); i++) {
                if (attrValueIdsSize != skus.get(i).getSpecValueList().size()) {
                    throw exception(ErrorCodeConstants.SPU_ATTR_NUMBERS_MUST_BE_EQUALS);
                }
            }
        }

        // 3. 最后校验，每个 Sku 之间不是重复的
        Set<Set<String>> skuAttrValues = new HashSet<>(); // 每个元素，都是一个 Sku 的 attrValueId 集合。这样，通过最外层的 Set ，判断是否有重复的.
        for (ProductSkuCreateOrUpdateReqVO sku : skus) {
            if (!skuAttrValues.add(convertSet(sku.getSpecValueList(), ProductSpecValueReqVO::getSpecValue))) { // 添加失败，说明重复
                throw exception(ErrorCodeConstants.SPU_SKU_NOT_DUPLICATE);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createSkuList(ProductSpuDO spu, List<ProductSkuCreateOrUpdateReqVO> list) {
        List<Long> skuIdList = Lists.newArrayList();
        list.forEach(item -> {
            ProductSkuDO sku = ProductSkuConvert.INSTANCE.convert02(item, spu);
            productSkuMapper.insert(sku);
            skuIdList.add(sku.getId());
            //productOperateLogService.saveCreateOperateSkuLog(sku);
            // 处理规格
            productSkuSpecService.handleSkuSpec(item.getSpecValueList(), sku.getId());

            // 处理库存
            productSkuStockService.createOrUpdateStock(sku, item);
        });
        validateSkuLimit(spu.getSupplierId());

        syncProductIndex(skuIdList, true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSkuList(ProductSpuDO spu, List<ProductSkuCreateOrUpdateReqVO> list) {
        List<ProductSkuDO> productSkuDOS = ProductSkuConvert.INSTANCE.convert02List(list, spu);
        this.updateBatchById(productSkuDOS);

        List<Long> skuIdList = productSkuDOS.stream().map(ProductSkuDO::getId).collect(Collectors.toList());
        validateSkuLimit(spu.getSupplierId());
        Map<Long, ProductSkuDO> productSkuDOMap = convertMap(productSkuDOS, ProductSkuDO::getId);
        list.forEach(item -> {
            ProductSkuDO sku = productSkuDOMap.get(item.getId());
            // 处理规格
            productSkuSpecService.handleSkuSpec(item.getSpecValueList(), sku.getId());

            // 处理库存
            productSkuStockService.createOrUpdateStock(sku, item);

            // 删除缓存
            deleteSkuCache(sku.getSupplierId(), sku.getId());
        });

        syncProductIndex(skuIdList, true);
    }

    private void validateSkuLimit(Long supplierId) {
        SupplierDO supplier = supplierService.getSupplier(supplierId);
        List<ProductSkuStatusResult> productSkuStatusResults = getProductSkuStatusResult(supplier.getTenantId(), supplier.getId());
        int skuTotalCount = 0, onSaleCount = 0;
        if(CollectionUtils.isNotEmpty(productSkuStatusResults)) {
            skuTotalCount = productSkuStatusResults.stream().mapToInt(ProductSkuStatusResult::getCount).sum();
            onSaleCount = productSkuStatusResults.stream()
                    .filter(productSkuStatusResult->ProductSpuStatusEnum.ENABLE.getStatus().equals(productSkuStatusResult.getStatus()))
                    .mapToInt(ProductSkuStatusResult::getCount)
                    .sum();
        }
        if(!supplier.isJd() && supplier.getOnSaleSkuLimit() != null && onSaleCount > supplier.getOnSaleSkuLimit()) {
            throw exception(SUPPLIER_ON_SALE_SKU_MAX_COUNT,supplier.getOnSaleSkuLimit());
        }
        if(!supplier.isJd() && supplier.getSkuLimit() != null && skuTotalCount > supplier.getSkuLimit()) {
            throw exception(SUPPLIER_SKU_MAX_COUNT,supplier.getSkuLimit());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSkuStatusBySpu(Long spuId, Integer status) {
        ProductSkuDO entity = new ProductSkuDO();
        entity.setStatus(status);

        baseMapper.update(entity, Wrappers.lambdaUpdate(ProductSkuDO.class).eq(ProductSkuDO::getSpuId, spuId));
        List<ProductSkuDO> skuList = baseMapper.selectSkuIdListBySpuId(spuId);
        if(ProductSpuStatusEnum.ENABLE.getStatus().equals(status) && CollectionUtils.isNotEmpty(skuList)) {
            validateSkuLimit(skuList.get(0).getSupplierId());
        }

        syncProductIndex(skuList.stream().map(ProductSkuDO::getId).collect(Collectors.toList()), true);
        // 删除缓存
        deleteSkuCache(skuList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSkuStatus(Long skuId, Integer status) {
        ProductSkuDO skuDO = validateSkuExists(skuId);
        baseMapper.update(null, Wrappers.lambdaUpdate(ProductSkuDO.class).eq(ProductSkuDO::getId, skuId)
                .set(ProductSkuDO::getStatus, status));

        syncProductIndex(true, skuId);
        // 删除缓存
        deleteSkuCache(skuDO.getSupplierId(), skuDO.getId());
    }

    @Override
    public void updateSkuShowStatusBySpu(Long spuId, Integer showStatus) {
        ProductSkuDO entity = new ProductSkuDO();
        entity.setShowStatus(showStatus);
        baseMapper.update(entity, Wrappers.lambdaUpdate(ProductSkuDO.class).eq(ProductSkuDO::getSpuId, spuId));
        List<ProductSkuDO> skuList = baseMapper.selectSkuIdListBySpuId(spuId);
        syncProductIndex(skuList.stream().map(ProductSkuDO::getId).collect(Collectors.toList()));
        // 删除缓存
        deleteSkuCache(skuList);
    }

    @Override
    public void updateSkuPlatformStatus(Long skuId, Integer status) {
        ProductSkuDO skuDO = validateSkuExists(skuId);
        baseMapper.update(null, Wrappers.lambdaUpdate(ProductSkuDO.class).eq(ProductSkuDO::getId, skuId)
                .set(ProductSkuDO::getPlatformStatus, status));
        syncProductIndex(skuId);
        // 删除缓存
        deleteSkuCache(skuDO.getSupplierId(), skuDO.getId());
    }

    @Override
    public void updateSkuPlatformStatusBySpu(Long spuId, Integer status) {
        ProductSkuDO entity = new ProductSkuDO();
        entity.setPlatformStatus(status);
        baseMapper.update(entity, Wrappers.lambdaUpdate(ProductSkuDO.class).eq(ProductSkuDO::getSpuId, spuId));
        List<ProductSkuDO> skuList = baseMapper.selectSkuIdListBySpuId(spuId);
        syncProductIndex(skuList.stream().map(ProductSkuDO::getId).collect(Collectors.toList()));
        // 删除缓存
        deleteSkuCache(skuList);
    }

    @Override
    public void updateSkuShowStatus(Long skuId, Integer showStatus) {
        ProductSkuDO skuDO = validateSkuExists(skuId);
        baseMapper.update(null, Wrappers.lambdaUpdate(ProductSkuDO.class).eq(ProductSkuDO::getId, skuId)
                .set(ProductSkuDO::getShowStatus, showStatus));
        syncProductIndex(skuId);
        // 删除缓存
        deleteSkuCache(skuDO.getSupplierId(), skuDO.getId());
    }

    @Override
    public List<ProductSkuDO> getSkuListBySpuId(Long spuId) {
        return productSkuMapper.selectListBySpuId(spuId);
    }

    @Override
    public List<ProductSkuDO> getSkuListBySpuIdAndStatus(Long spuId, Integer status) {
        return productSkuMapper.selectListBySpuIdAndStatus(spuId, status);
    }

    @Override
    public List<ProductSkuDO> getSkuListBySpuId(List<Long> spuIds) {
        return productSkuMapper.selectListBySpuIds(spuIds);
    }

    @Override
    public void deleteSkuBySpuId(Long spuId) {
        List<ProductSkuDO> skuList = baseMapper.selectSkuIdListBySpuId(spuId);
        productSkuMapper.deleteBySpuId(spuId);
        syncProductIndex(skuList.stream().map(ProductSkuDO::getId).collect(Collectors.toList()));
    }

    @Override
    public void disableSkuBySpuId(Long spuId) {
        List<ProductSkuDO> skuList = baseMapper.selectSkuIdListBySpuId(spuId);
        List<ProductSkuDO> productSkuDOS = productSkuMapper.selectListBySpuId(spuId);
        productSkuDOS.forEach(productSkuDO -> {
            productSkuDO.setStatus(0);
        });

        if(CollUtil.isEmpty(productSkuDOS)) {
            log.info("商品SPU下面的SKU为空:{}", spuId);
            return;
        }
        productSkuMapper.updateBatch(productSkuDOS, 20);
        syncProductIndex(skuList.stream().map(ProductSkuDO::getId).collect(Collectors.toList()));
    }

    @Override
    public List<ProductSkuDO> getSkuListByAlarmStock() {
        return productSkuMapper.selectListByAlarmStock();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setProductSkuStatus(List<ProductSkuUpdateStatusVO> reqVOs, Long supplierId) {
        List<ProductSkuDO> productSkuDOS = reqVOs.stream().map(productSkuUpdateStatusVO ->
                        new ProductSkuDO()
                                .setId(productSkuUpdateStatusVO.getSkuId())
                                .setStatus(productSkuUpdateStatusVO.getStatus()))
                .collect(Collectors.toList());
        productSkuMapper.updateBatch(productSkuDOS, 1000);
        validateSkuLimit(supplierId);

        syncProductIndex(reqVOs.stream().map(ProductSkuUpdateStatusVO::getSkuId).collect(Collectors.toList()), true);
    }

    @Override
    public void setProductSkuStatus02(List<AppOpenSkuUpdateStatusReqVO> reqVOs, Long supplierId) {
        reqVOs.forEach(reqVO -> {
            productSkuMapper.update(null, Wrappers.lambdaUpdate(ProductSkuDO.class)
                    .eq(ProductSkuDO::getSupplierId, supplierId)
                    .in(ProductSkuDO::getId, reqVO.getSkuId())
                    .set(ProductSkuDO::getStatus, reqVO.getStatus()));
        });

        syncProductIndex(reqVOs.stream().map(AppOpenSkuUpdateStatusReqVO::getSkuId).collect(Collectors.toList()));
    }

    @Override
    public void updateProductSkuPrice(List<ProductSkuUpdatePriceVO> reqVOs, Long supplierId) {
        List<ProductSkuDO> productSkuDOS = reqVOs.stream().map(productSkuUpdateStatusVO ->
                        new ProductSkuDO()
                                .setId(productSkuUpdateStatusVO.getSkuId())
                                .setSalePrice(productSkuUpdateStatusVO.getSalePrice())
                                .setMarketPrice(productSkuUpdateStatusVO.getMarketPrice()))
                .collect(Collectors.toList());
        productSkuMapper.updateBatch(productSkuDOS, 1000);

        syncProductIndex(reqVOs.stream().map(ProductSkuUpdatePriceVO::getSkuId).collect(Collectors.toList()));
    }

    @Override
    public void updateProductSkuPrice02(List<AppOpenSkuUpdatePriceReqVO> reqVOs, Long supplierId) {
        reqVOs.forEach(reqVO -> {
            validProductRule(reqVO.getSkuId().toString(), reqVO.getSalePrice());
            productSkuMapper.update(null, Wrappers.lambdaUpdate(ProductSkuDO.class)
                    .eq(ProductSkuDO::getSupplierId, supplierId)
                    .in(ProductSkuDO::getId, reqVO.getSkuId())
                    .set(ProductSkuDO::getMarketPrice, reqVO.getMarketPrice())
                    .set(ProductSkuDO::getSalePrice, reqVO.getSalePrice()));
        });

        syncProductIndex(reqVOs.stream().map(AppOpenSkuUpdatePriceReqVO::getSkuId).collect(Collectors.toList()));
    }

    @Override
    public void updateStatusByInnerId(String innerId, Integer status, Long supplierId) {
        List<ProductSkuDO> skuList = productSkuMapper.selectList(Wrappers.lambdaQuery(ProductSkuDO.class).select(ProductSkuDO::getId)
                .eq(ProductSkuDO::getSkuInnerId, innerId)
                .eq(ProductSkuDO::getSupplierId, supplierId));
        productSkuMapper.update(null, Wrappers.<ProductSkuDO>lambdaUpdate()
                .eq(ProductSkuDO::getSkuInnerId, innerId)
                .eq(ProductSkuDO::getSupplierId, supplierId)
                .set(ProductSkuDO::getStatus, status));

        syncProductIndex(skuList.stream().map(ProductSkuDO::getId).collect(Collectors.toList()));
    }

    private void fillCategory(ProductSpuOpenVO reqVO, ProductSpuDO productSpuDO) {
        List<Long> categoryIds = new ArrayList<>();
        if (reqVO.getCategory1Id() != null) {
            categoryIds.add(reqVO.getCategory1Id());
        }
        if (reqVO.getCategory2Id() != null) {
            categoryIds.add(reqVO.getCategory2Id());
        }
        if (reqVO.getCategory3Id() != null) {
            categoryIds.add(reqVO.getCategory3Id());
        }
        // 不接收和平台分类不匹配的商品
        if(!categoryService.validateCategoryIds(categoryIds)) {
            exception(OPEN_APP_PRODUCT_CATEGORY_INVALID);
        }
        productSpuDO.setFullCategoryId(StringUtils.join(categoryIds, "-"));
        String fullNames = categoryService.getNamePath(categoryIds, "/");
        productSpuDO.setFullCategoryName(fullNames);

        String[] names = StringUtils.split(fullNames, "/");
        if(names != null) {
            if(names.length > 0) {
                productSpuDO.setCategory1Name(names[0]);
            }
            if(names.length > 1) {
                productSpuDO.setCategory2Name(names[1]);
            }
            if(names.length > 2) {
                productSpuDO.setCategory3Name(names[2]);
            }
        }
    }

    private BigDecimal getMinSalePrice2(List<ProductSkuPriceReqVO> skus) {
        if(CollUtil.isNotEmpty(skus)) {
            try {
                return skus.stream().map(ProductSkuPriceReqVO::getSalePrice).min(Comparator.comparing(x -> x)).orElse(BigDecimal.ZERO);
            } catch(Exception e) {}
        }
        return BigDecimal.ZERO;
    }

    private BigDecimal getMinSalePrice(List<AppProductSkuOpenVO> skus) {
        if(CollUtil.isNotEmpty(skus)) {
            try {
                return skus.stream().map(AppProductSkuOpenVO::getSalePrice).min(Comparator.comparing(x -> x)).orElse(BigDecimal.ZERO);
            } catch(Exception e) {}
        }
        return BigDecimal.ZERO;
    }

    private List<ProductSkuDO> getSimpleSkuBySupplierInnerId(Long supplierId, List<String> skuInnerIds) {
        return productSkuMapper.selectList(Wrappers.lambdaQuery(ProductSkuDO.class)
                .eq(ProductSkuDO::getSupplierId, supplierId)
                .in(ProductSkuDO::getSkuInnerId, skuInnerIds)
                .select(ProductSkuDO::getId, ProductSkuDO::getSupplierId, ProductSkuDO::getSkuInnerId));
    }

    private void cleanSpuId(List<ProductSpuDO> spuList) {
        spuList.forEach(spu -> spu.setId(null));
    }

    private void cleanSkuId(List<ProductSkuDO> skuList) {
        skuList.forEach(sku -> sku.setId(null));
    }

    private void handleSpuId(List<ProductSpuDO> spuList, List<ProductSpuDO> persistList) {
        if(CollUtil.isEmpty(persistList)) {
            return;
        }
        Map<String, Long> innerIdMap = convertMap(persistList, ProductSpuDO::getSpuInnerId, ProductSpuDO::getId);
        spuList.forEach(spu -> {
            if(innerIdMap.containsKey(spu.getSpuInnerId())) {
                spu.setId(innerIdMap.get(spu.getSpuInnerId()));
            }
        });
    }

    private void handleSkuId(List<ProductSkuDO> skuList, List<ProductSkuDO> persistList) {
        if(CollUtil.isEmpty(persistList)) {
            return;
        }
        Map<String, Long> innerIdMap = convertMap(persistList, ProductSkuDO::getSkuInnerId, ProductSkuDO::getId);
        skuList.forEach(sku -> {
            if(innerIdMap.containsKey(sku.getSkuInnerId())) {
                sku.setId(innerIdMap.get(sku.getSkuInnerId()));
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ProductSpuBaseRespOpenVO> addProductSku(List<ProductSpuOpenVO> reqVOs, Long supplierId) {
        List<String> spuInnerIds = reqVOs.stream().map(ProductSpuOpenVO::getSpuInnerId).collect(Collectors.toList());
        Set<String> spuInnerIdSet = new HashSet<>(spuInnerIds);
        Assert.equals(spuInnerIdSet.size(), spuInnerIds.size(), "spuInnerId重复：{}", StringUtils.join(spuInnerIds, ","));

        List<ProductSpuBaseRespOpenVO> respOpenVOS = new ArrayList<>();
        SupplierDO supplierDO = supplierService.getSupplier(supplierId);
        Map<String, List<AppProductSpecValueVO>> spuSpecValueMap = new HashMap<>();
        Map<String, List<AppProductSkuOpenVO>> skuMap = new HashMap<>();
        List<ProductSpuDO> productSpuDOS = new ArrayList<>();
        for (ProductSpuOpenVO reqVO : reqVOs) {
            List<String> skuInnerIds = reqVO.getSkus().stream().map(AppProductSkuOpenVO::getSkuInnerId).collect(Collectors.toList());
            Set<String> skuInnerIdSet = new HashSet<>(skuInnerIds);
            Assert.equals(skuInnerIds.size(), skuInnerIdSet.size(), "{}下的skuInnerId重复：{}", reqVO.getSpuInnerId(), StringUtils.join(skuInnerIds, ","));

            BigDecimal spuPrice = getMinSalePrice(reqVO.getSkus());
            ProductSpuDO productSpuDO = ProductSpuConvert.INSTANCE.convertDO(reqVO, supplierDO);
            productSpuDO.setSalePrice(spuPrice);
            if(productSpuDO.getStatus() == null) {
                productSpuDO.setStatus(ProductSpuStatusEnum.ENABLE.getStatus());
            }
            fillCategory(reqVO, productSpuDO);
            productSpuDO.setBrandId(brandService.createFromName(reqVO.getBrandName()));
            productSpuDOS.add(productSpuDO);
            List<AppProductSpecValueVO> spuSpecValueList = reqVO.getSpuSpecValueList();
            spuSpecValueMap.put(reqVO.getSpuInnerId(), spuSpecValueList);
            List<AppProductSkuOpenVO> skus = reqVO.getSkus();

            if(!skuMap.containsKey(reqVO.getSpuInnerId())) {
                skuMap.put(reqVO.getSpuInnerId(), skus);
            } else {
                skuMap.get(reqVO.getSpuInnerId()).addAll(skus);
            }

            for (ProductSpuOpenVO spu : reqVOs) {
                for (AppProductSkuOpenVO sku : spu.getSkus()) {
                    validProductRule(sku.getSkuInnerId(), productSpuDO.getFullCategoryId(), sku.getSalePrice(),
                            Arrays.asList(spu.getSpuName(), sku.getSkuName()));
                }
            }
        }

        Map<String, ProductSpuDO> productSpuInnerDOMap = convertMap(productSpuDOS, ProductSpuDO::getSpuInnerId);
        // 通过spuInnerId查询商品是否存在并设置ID
        List<ProductSpuDO> persistSpuList = productSpuService.getSimpleSpuBySupplierInnerId(supplierId, new ArrayList<>(productSpuInnerDOMap.keySet()));
        cleanSpuId(productSpuDOS);
        handleSpuId(productSpuDOS, persistSpuList);

        //保存spu信息
        productSpuService.saveOrUpdateBatchV2(productSpuDOS);
        Map<Long, ProductSpuDO> productSpuDOMap = convertMap(productSpuDOS, ProductSpuDO::getId);

        //保存sku信息
        List<ProductSkuDO> productSkuDOS = new ArrayList<>();
        Map<String, List<AppProductSpecValueVO>> skuSpecValueMap = new HashMap<>();
        for (Map.Entry<String, List<AppProductSkuOpenVO>> entry : skuMap.entrySet()) {
            List<AppProductSkuOpenVO> appProductSkuOpenVOS = skuMap.get(entry.getKey());
            Long spuId = productSpuInnerDOMap.get(entry.getKey()).getId();
            String spuName = productSpuInnerDOMap.get(entry.getKey()).getSpuName();
            if(CollectionUtils.isNotEmpty(appProductSkuOpenVOS)) {
                appProductSkuOpenVOS.forEach(skuOpenVO -> {
                    skuSpecValueMap.put(skuOpenVO.getSkuInnerId(), skuOpenVO.getSkuSpecValueList());
                    log.info("保存sku信息:{}", skuOpenVO.getSkuInnerId());
                });
            }

            productSkuDOS.addAll(ProductSkuConvert.INSTANCE.convertDOList(appProductSkuOpenVOS, supplierDO, spuId, spuName));
        }

        Map<String, ProductSkuDO> skuInnerIdMap = convertMap(productSkuDOS, ProductSkuDO::getSkuInnerId);
        // 通过spuInnerId查询商品是否存在并设置ID
        List<ProductSkuDO> persistSkuList = getSimpleSkuBySupplierInnerId(supplierId, new ArrayList<>(skuInnerIdMap.keySet()));
        cleanSkuId(productSkuDOS);
        handleSkuId(productSkuDOS, persistSkuList);

        saveOrUpdateBatch(productSkuDOS);
        validateSkuLimit(supplierId);

        Map<Long, ProductSkuDO> skuIdMap = convertMap(productSkuDOS, ProductSkuDO::getId);
        //保存商品库存
        List<AppProductSkuOpenVO> appProductSkuOpenVOS = skuMap.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());
        for (AppProductSkuOpenVO appProductSkuOpenVO : appProductSkuOpenVOS) {
            appProductSkuOpenVO.setSupplierId(supplierId);
            ProductSkuDO productSkuDO = appProductSkuOpenVO.getSkuId() != null ? skuIdMap.get(appProductSkuOpenVO.getSkuId()) : skuInnerIdMap.get(appProductSkuOpenVO.getSkuInnerId());
            if(productSkuDO != null && appProductSkuOpenVO.getSkuId() == null) {
                appProductSkuOpenVO.setSkuId(productSkuDO.getId());
            }
        }
        List<ProductSkuStockDO> productSkuStockDOS = ProductSkuStockConvert.INSTANCE.convertList01(appProductSkuOpenVOS);
        productSkuStockService.saveBatchProductSkuStocks(productSkuStockDOS);

        // 保存spu规格信息
        for (Map.Entry<String, List<AppProductSpecValueVO>> entry : spuSpecValueMap.entrySet()) {
            Long spuId = productSpuInnerDOMap.get(entry.getKey()).getId();
            productSpuSpecService.saveBatchBySpu(ProductSpecConvert.INSTANCE.convertDOList03(entry.getValue(), spuId), spuId);
        }

        //保存sku规格属性
        Map<String, ProductSkuDO> productSkuDOMap = convertMap(productSkuDOS, ProductSkuDO::getSkuInnerId);
        for (Map.Entry<String, List<AppProductSpecValueVO>> entry : skuSpecValueMap.entrySet()) {
            Long skuId = productSkuDOMap.get(entry.getKey()).getId();
            productSkuSpecService.saveBatchBySku(ProductSpecConvert.INSTANCE.convertDOList02(entry.getValue(), skuId), skuId);
        }

        Map<Long, List<ProductSkuDO>> productSKuMap = productSkuDOS.stream().collect(Collectors.groupingBy(ProductSkuDO::getSpuId));
        for (Map.Entry<Long, List<ProductSkuDO>> entry : productSKuMap.entrySet()) {
            Long spuId = entry.getKey();
            String spuInnerId = productSpuDOMap.get(spuId).getSpuInnerId();
            ProductSpuBaseRespOpenVO respOpenVO = new ProductSpuBaseRespOpenVO().setSpuId(spuId).setSpuInnerId(spuInnerId);
            List<ProductSpuBaseRespOpenVO.SkuRespVO> skuRespVOS = entry.getValue().stream().map(productSkuDO -> new ProductSpuBaseRespOpenVO.SkuRespVO()
                            .setSkuId(productSkuDO.getId())
                            .setSkuInnerId(productSkuDO.getSkuInnerId()))
                    .collect(Collectors.toList());
            respOpenVO.setSkus(skuRespVOS);
            respOpenVOS.add(respOpenVO);
        }

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                try {
                    syncProductIndex(productSkuDOS.stream().map(ProductSkuDO::getId).collect(Collectors.toList()));
                    deleteSkuCache(productSkuDOS);
                } catch (Exception e) {
                    log.error("商品SKU添加后逻辑处理异常:{}", e);
                }
            }
        });

        return respOpenVOS;
    }

    @Override
    public void validProductRule(String skuInnerId, String fullCategoryId, BigDecimal salePrice, List<String> keywords) {
        List<String> fullCategoryIdList = Arrays.asList(fullCategoryId.split("-"));
        Set<String> fullCategoryIdSet = new HashSet<>(fullCategoryIdList);
        if(fullCategoryIdList.size() == 0 || fullCategoryIdList.size() != fullCategoryIdSet.size()){
            throw exception(PRODUCT_RULE_NO_VALID_CATEGORY, skuInnerId, fullCategoryId);
        }

        if(!productRuleService.validProductCategory(fullCategoryId)){
            throw exception(PRODUCT_RULE_NO_VALID_CATEGORY, skuInnerId, fullCategoryId);
        }

        if(!productRuleService.validProductPrice(salePrice)){
            throw exception(PRODUCT_RULE_NO_VALID_PRICE, skuInnerId, salePrice.toPlainString());
        }

        for (String keyword : keywords) {
            if(!productRuleService.validProductKeywords(keyword)){
                throw exception(PRODUCT_RULE_NO_VALID_KEYWORDS, skuInnerId, keyword);
            }
        }
    }

    @Override
    public void validProductRule(String skuInnerId, String fullCategoryId) {
        List<String> fullCategoryIdList = Arrays.asList(fullCategoryId.split("-"));
        Set<String> fullCategoryIdSet = new HashSet<>(fullCategoryIdList);
        if(fullCategoryIdList.size() == 0 || fullCategoryIdList.size() != fullCategoryIdSet.size()){
            throw exception(PRODUCT_RULE_NO_VALID_CATEGORY, skuInnerId, fullCategoryId);
        }

        if(!productRuleService.validProductCategory(fullCategoryId)){
            throw exception(PRODUCT_RULE_NO_VALID_CATEGORY, skuInnerId, fullCategoryId);
        }
    }

    @Override
    public void validProductRule(String skuInnerId, BigDecimal salePrice) {
        if(!productRuleService.validProductPrice(salePrice)){
            throw exception(PRODUCT_RULE_NO_VALID_PRICE, skuInnerId, salePrice.toPlainString());
        }
    }

    @Override
    public void validProductRule(String skuInnerId, List<String> keywords) {
        for (String keyword : keywords) {
            if(!productRuleService.validProductKeywords(keyword)){
                throw exception(PRODUCT_RULE_NO_VALID_KEYWORDS, skuInnerId, keyword);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateProductSku(ProductSpuOpenVO reqVO, Long supplierId, Long tenantId) {
        SupplierDO supplierDO;
        if(tenantId != null){
            TenantContextHolder.setTenantId(tenantId);
            supplierDO = supplierService.getSupplierJD();
            supplierId = supplierDO.getId();
        }
        else {
            supplierDO = supplierService.getSupplier(supplierId);
        }

        ProductSpuDO productSpuDO = ProductSpuConvert.INSTANCE.convertDO(reqVO, supplierDO);
        fillCategory(reqVO, productSpuDO);
		productSpuDO.setBrandId(brandService.createFromName(reqVO.getBrandName()));
        BigDecimal spuPrice = getMinSalePrice(reqVO.getSkus());
        productSpuDO.setSalePrice(spuPrice);
        if(productSpuDO.getStatus() == null) {
            productSpuDO.setStatus(ProductSpuStatusEnum.ENABLE.getStatus());
        }
        //保存spu信息
        ProductSpuDO persistSpu = productSpuService.getSimpleSpuBySupplierInnerId(supplierId, productSpuDO.getSpuInnerId());
        if(persistSpu != null) {
            productSpuDO.setId(persistSpu.getId());
        }
        productSpuService.saveOrUpdate(productSpuDO);

        //保存sku信息
        List<ProductSkuDO> productSkuDOS = new ArrayList<>();
        Map<String, List<AppProductSpecValueVO>> innerSpecList = new HashMap<>();
        for (AppProductSkuOpenVO skuVO : reqVO.getSkus()) {
            ProductSkuDO skuDO = ProductSkuConvert.INSTANCE.convertDO2(skuVO, supplierDO, productSpuDO.getId(), productSpuDO.getSpuName());
            productSkuDOS.add(skuDO);

            innerSpecList.put(skuVO.getSkuInnerId(), skuVO.getSkuSpecValueList());
        }
        Map<String, ProductSkuDO> innerIdMap = convertMap(productSkuDOS, ProductSkuDO::getSkuInnerId);
        List<ProductSkuDO> persistSkuList = getSimpleSkuBySupplierInnerId(supplierId, new ArrayList<>(innerIdMap.keySet()));
        handleSkuId(productSkuDOS, persistSkuList);
        this.saveOrUpdateBatch(productSkuDOS);

        // 更新SPU规格类型
        long skuNums = selectCountBySpuId(productSpuDO.getId());
        productSpuDO.setSpecType(skuNums <= 1 ? ProductSpuSpecTypeEnum.Single.getType() : ProductSpuSpecTypeEnum.Multiple.getType());
        productSpuService.updateById(new ProductSpuDO().setId(productSpuDO.getId()).setSpecType(productSpuDO.getSpecType()));

        // 保存spu规格信息
        productSpuSpecService.saveBatchBySpu(ProductSpecConvert.INSTANCE.convertDOList03(reqVO.getSpuSpecValueList(), productSpuDO.getId()), productSpuDO.getId());

        for (String innerId : innerSpecList.keySet()) {
            // 保存sku规格信息
            Long skuId = innerIdMap.get(innerId).getId();
            productSkuSpecService.saveBatchBySku(ProductSpecConvert.INSTANCE.convertDOList02(innerSpecList.get(innerId), skuId), skuId);
        }

        syncProductIndex(productSkuDOS.stream().map(ProductSkuDO::getId).collect(Collectors.toList()), true);
    }

    private long selectCountBySpuId(Long spuId) {
        return this.baseMapper.selectCount(ProductSkuDO::getSpuId, spuId);
    }

    @Override
    public AppAppProductSkuOpenRespVO getProductSkuDetail(Long skuId, Long supplierId) {
        //1.先查sku信息
        ProductSkuDO sku = this.getSku(skuId);
        Assert.notNull(sku, "商品sku信息不存在");
        Assert.isTrue(sku.getSupplierId().equals(supplierId), "商品sku信息不存在");
        //2.查询sku关联的规格属性
        List<ProductSkuSpecDO> skuSpecDOS = productSkuSpecService.getProductSkuSpecBySkuId(skuId);
        //3.查询spu信息
        Long spuId = sku.getSpuId();
        ProductSpuDO spu = productSpuService.getSpu(spuId);
        Assert.notNull(spu, "商品spu信息不存在");
        //4.查询sku库存
        ProductSkuStockDO skuStockDO = productSkuStockService.getSkuStock(skuId);
        //5.查询spu关联的商品规格属性
        List<ProductSpuSpecDO> spuSpecDOS = productSpuSpecService.getSpuSpecListBySpuId(spuId);
        return assembleProductSpuOpenVO(spu, spuSpecDOS, sku, skuSpecDOS, skuStockDO);
    }

    @Override
    public Long convert2JdSkuId(Long skuId) {
        // 通过sku id先在本库查询，如果能查到，则返回京东skuId
        ProductSkuDO skuDO = getSku(skuId);
        if(skuDO != null) {
            return Long.valueOf(skuDO.getSkuInnerId());
        }

        return skuId;
    }

    @Override
    public Integer getUnionStatus(Long skuId, Integer thirdStatus){
        Integer status = thirdStatus;
        if(status == 1) {
            ProductSkuDO skuDO = getSku(skuId);
            if(skuDO != null && skuDO.getStatus() != 1){
                status = 0;
            }
        }
        return status;
    }

    @Override
    public String getSpuCategory(String skuInnerId, Long supplierId) {
        String category = productSkuMapper.selectSpuCategory(skuInnerId, supplierId);
        log.info("商品分类{}, {}, {}", skuInnerId, supplierId, category);
        if(StringUtils.isNotBlank(category)) {
            return StringUtils.replace(category,"-", ";");
        }
        return category;
    }

    @Override
    public void increaseSkuSaleCountAndAmount(List<TradeOrderItemBaseReqVO> items) {
        if (CollectionUtils.isNotEmpty(items)) {
            for (TradeOrderItemBaseReqVO item : items) {
                productSkuMapper.updateSalesCountAndAmount(item);
            }

        }
    }

    @Override
    public void decreaseSaleCountAndAmount(List<TradeOrderItemBaseReqVO> items) {
        if (CollectionUtils.isNotEmpty(items)) {
            for (TradeOrderItemBaseReqVO item : items) {
                item.setCount(-item.getCount());
                productSkuMapper.updateSalesCountAndAmount(item);
            }
        }
    }

    @Override
    public List<SkuCountCategorySummaryRespVO> getProductCountByCategory() {
        Long tenantId = TenantContextHolder.getTenantId();
        return productSkuMapper.getProductCountByCategory(tenantId);
    }

    @Override
    @ProductSkuCheck(skuId = "#skuDetailReq.skuId",async = true,checkSort = ProductSkuCheck.CheckSortType.AFTER)
    public AppSkuDetailInfo getSkuDetailInfo(VopGoodsGetSkuDetailReq skuDetailReq) {
        String areaIds = StringUtils.isNotBlank(skuDetailReq.getAreaIds()) ? skuDetailReq.getAreaIds() : DEFAULT_AREA_IDS;
        ProductSkuDO sku = this.getSku(skuDetailReq.getSkuId());
        Assert.notNull(sku, "商品sku信息不存在");
        SupplierDO supplier = supplierService.getSupplier(sku.getSupplierId());
        if (supplier != null && supplier.isJd()) {
            Long skuInnerId = Long.valueOf(sku.getSkuInnerId());

            skuDetailReq.setSkuId(skuInnerId);
            skuDetailReq.setAreaIds(areaIds);
            OpenRpcResult openRpcResult = vopGoodsService.getSkuDetailInfo(skuDetailReq).getOpenRpcResult();
            Assert.isTrue(openRpcResult.getSuccess(), String.format("商品sku：%d不存在", skuDetailReq.getSkuId()));
            GetSkuPoolInfoGoodsResp getSkuPoolInfoGoodsResp = openRpcResult.getResult();
            AppSkuDetailInfo appSkuDetailInfo = ProductSkuConvert.INSTANCE.convertSkuDetailResp(getSkuPoolInfoGoodsResp);
            appSkuDetailInfo.setSupplierId(supplier.getId());
            appSkuDetailInfo.setSupplierName(supplier.getName());
            appSkuDetailInfo.setLogoUrl(supplier.getLogoUrl());
            appSkuDetailInfo.setImagePath(jdImagePath + appSkuDetailInfo.getImagePath());
            if(appSkuDetailInfo.getLowestBuy() == null){
                appSkuDetailInfo.setLowestBuy(1);
            }

            //京东skuId转换为平台skuId
            appSkuDetailInfo.setSkuId(sku.getId());
            appSkuDetailInfo.setSkuInnerId(sku.getSkuInnerId());
            appSkuDetailInfo.setIsJD(true);
            String category = getSpuCategory(skuInnerId.toString(), supplier.getId());
            appSkuDetailInfo.setCategory(category);

            if(CollUtil.isNotEmpty(getSkuPoolInfoGoodsResp.getCategoryAttrList())){
                List<ParamAttributeRespVO> paramAttributeList = new ArrayList<>();
                for(CategoryAttributeGoodsResp categoryAttr : getSkuPoolInfoGoodsResp.getCategoryAttrList()) {
                    ParamAttributeRespVO paramAttributeRespVO = new ParamAttributeRespVO();
                    paramAttributeRespVO.setParamAttrName(categoryAttr.getCateAttrName());
                    paramAttributeRespVO.setParamAttrValList(categoryAttr.getCateAttrValList());
                    paramAttributeList.add(paramAttributeRespVO);
                }

                if(appSkuDetailInfo.getParamGroupAttrList() == null){
                    List<ParamGroupAttributeGoodsRespVO> paramGroupAttributeGoodsRespList = new ArrayList<>();
                    ParamGroupAttributeGoodsRespVO paramGroupAttributeGoodsRespVO = new ParamGroupAttributeGoodsRespVO();
                    paramGroupAttributeGoodsRespVO.setParamGroupName("主体");
                    paramGroupAttributeGoodsRespVO.setParamAttributeList(new ArrayList<>());
                    appSkuDetailInfo.setParamGroupAttrList(paramGroupAttributeGoodsRespList);
                }

                for (ParamGroupAttributeGoodsRespVO paramGroupAttributeGoodsRespVO : appSkuDetailInfo.getParamGroupAttrList()) {
                    paramGroupAttributeGoodsRespVO.getParamAttributeList().addAll(0, paramAttributeList);
                }
            }

            return appSkuDetailInfo;
        }

        Long spuId = sku.getSpuId();
        ProductSpuDO spu = productSpuService.getSpu(spuId);
        Assert.notNull(spu, "商品spu信息不存在");
        AppSkuDetailInfo appSkuDetailInfo = new AppSkuDetailInfo();
        appSkuDetailInfo.setIsJD(false);
        appSkuDetailInfo.setSupplierId(supplier.getId());
        appSkuDetailInfo.setSupplierName(supplier.getName());
        appSkuDetailInfo.setLogoUrl(supplier.getLogoUrl());
        appSkuDetailInfo.setImagePath(spu.getPicUrl());
        appSkuDetailInfo.setSkuState(sku.getStatus());
        appSkuDetailInfo.setSaleStatus(sku.getStatus());
        appSkuDetailInfo.setSliderPicUrls(spu.getSliderPicUrls());
        appSkuDetailInfo.setSalePrice(sku.getSalePrice());
        appSkuDetailInfo.setMarketPrice(sku.getMarketPrice());
        appSkuDetailInfo.setSkuId(sku.getId());
        appSkuDetailInfo.setSkuInnerId(sku.getSkuInnerId());
        appSkuDetailInfo.setBrandName(spu.getBrandName());
        appSkuDetailInfo.setCategory(StringUtils.replace(spu.getFullCategoryId(), "-", ";"));
        appSkuDetailInfo.setSkuName(sku.getSkuName());
        Integer lowestBuy = sku.getLowestBuy();
        if(lowestBuy == null){
            lowestBuy = 1;
        }
        appSkuDetailInfo.setLowestBuy(lowestBuy);
        if(ServletUtils.isMobileRequest()) {
            if(StringUtils.isNotBlank(spu.getDescriptionH5())) {
                appSkuDetailInfo.setIntroduceApp(spu.getDescriptionH5());
            } else {
                appSkuDetailInfo.setIntroduce(spu.getDescription());
            }
        } else {
            appSkuDetailInfo.setIntroducePc(spu.getDescription());
        }

        appSkuDetailInfo.setSpuName(spu.getSpuName());
        appSkuDetailInfo.setSpuId(spu.getId());
        appSkuDetailInfo.setSpecType(spu.getSpecType());
        appSkuDetailInfo.setLogisticsType(1);
        List<ProductSpuSpecDO> spuSpecDOS = productSpuSpecService.getSpuSpecListBySpuId(spuId);
        //设置商品规格
        if(CollectionUtils.isNotEmpty(spuSpecDOS)) {
            List<ParamAttributeRespVO>  paramGroupAttrList = spuSpecDOS.stream().map(spuSpecDO -> {
                String[] specValues = {spuSpecDO.getSpecValue()};
                List<String[]> paramAttrValList = new ArrayList<>();
                paramAttrValList.add(specValues);
                return new ParamAttributeRespVO()
                        .setParamAttrName(spuSpecDO.getSpecName())
                        .setParamAttrValList(paramAttrValList);
            }).collect(Collectors.toList());
            ParamGroupAttributeGoodsRespVO paramGroupAttributeGoodsRespVO = new ParamGroupAttributeGoodsRespVO();
            paramGroupAttributeGoodsRespVO.setParamGroupName("规格");
            paramGroupAttributeGoodsRespVO.setParamAttributeList(paramGroupAttrList);
            appSkuDetailInfo.setParamGroupAttrList(Arrays.asList(paramGroupAttributeGoodsRespVO));
        }

        return appSkuDetailInfo;
    }

    @Override
    @ProductSkuCheck(skuId = "#brotherSkuReq.skuId",async = true,checkSort = ProductSkuCheck.CheckSortType.AFTER)
    public List<BrotherSku> getBrotherSkus(VopGoodsBrotherSkuReq brotherSkuReq) {
        ProductSkuDO productSkuDO = getSimpleSkuById(brotherSkuReq.getSkuId());
        Assert.notNull(productSkuDO, String.format("商品sku：%d不存在", brotherSkuReq.getSkuId()));

        SupplierDO supplier = supplierService.getSupplier(productSkuDO.getSupplierId());
        if(supplier == null) {
            return new ArrayList<>();
        }
        if (productSkuDO.isJd()) {
            String skuInnerId = productSkuDO.getSkuInnerId();
            VopGoodsGetSimilarSkuListResponse response = vopGoodsService.getSimilarSkuList(Long.valueOf(productSkuDO.getSkuInnerId()));
            Assert.isTrue(response.getOpenRpcResult().getSuccess(),
                    String.format("查询相似商品失败：%d", brotherSkuReq.getSkuId()));
            List<GetSimilarSkuGoodsResp> similarSkuGoodsResps = response.getOpenRpcResult().getResult();
            if(CollUtil.isEmpty(similarSkuGoodsResps)) {
                return new ArrayList<>();
            }
            Set<Long> skuInnerIds = new HashSet<>();
            similarSkuGoodsResps.forEach(similarSkuGoodsResp -> {
                similarSkuGoodsResp.getSaleLabelMap().forEach((labelName, saleLabel) -> {
                    skuInnerIds.addAll(saleLabel.getSkuIdSet());
                });
            });
            Set<String> insertSkuInnerIds = skuInnerIds.stream()
                    .map(String::valueOf)
                    .collect(Collectors.toSet());
            Set<String> filterSkuInnerIds = insertSkuInnerIds;
            VopConfigDO vopConfig = vopConfigService.getVopConfigRequired();
            // 全量商品池校验
            if(vopConfig != null && vopConfig.getFullPoolSwitch() != null && vopConfig.getFullPoolSwitch()){
                List<VopSkuCategoryDO> vopSkuCategoryDOS = vopSkuCategoryService.list(
                        new LambdaQueryWrapperX<VopSkuCategoryDO>().in(VopSkuCategoryDO::getSkuInnerId, insertSkuInnerIds).select(VopSkuCategoryDO::getSkuInnerId)
                );
                if(CollUtil.isEmpty(vopSkuCategoryDOS)){
                    return new ArrayList<>();
                }
                filterSkuInnerIds = vopSkuCategoryDOS.stream()
                        .map(VopSkuCategoryDO::getSkuInnerId)
                        .collect(Collectors.toSet());
            }
            List<ProductSkuDO> skuDOS = productSkuMapper.selectList(new LambdaQueryWrapperX<ProductSkuDO>().in(ProductSkuDO::getSkuInnerId, filterSkuInnerIds).select(ProductSkuDO::getId, ProductSkuDO::getSkuInnerId));
            List<BrotherSku> brotherSkuList = new ArrayList<>();
            Map<String, BrotherSku> brotherSkuMap = new HashMap<>();
            for (GetSimilarSkuGoodsResp similarSkuGoodsResp : similarSkuGoodsResps) {
                similarSkuGoodsResp.getSaleLabelMap().forEach((labelName, saleLabel) -> {
                    Set<String> jdSkuIdSet = saleLabel.getSkuIdSet().stream()
                            .map(String::valueOf)
                            .collect(Collectors.toSet());
                    jdSkuIdSet.retainAll(skuDOS.stream().map(ProductSkuDO::getSkuInnerId).collect(Collectors.toSet()));
                    if (!jdSkuIdSet.isEmpty()) {
                        List<Long> skuIdList = skuDOS.stream().filter(item -> jdSkuIdSet.contains(item.getSkuInnerId())).map(ProductSkuDO::getId).collect(Collectors.toList());
                        if(brotherSkuMap.containsKey(similarSkuGoodsResp.getSaleName())) {
                            BrotherSku brotherSku = brotherSkuMap.get(similarSkuGoodsResp.getSaleName());
                            BrotherSkuSpecValue brotherSkuSpecValue = brotherSku.getSpecValues().stream().filter(item -> item.getSpecValue().equals(labelName)).findFirst().orElse(null);
                            if(brotherSkuSpecValue == null) {
                                brotherSkuSpecValue = new BrotherSkuSpecValue().setSpecValue(labelName)
                                        .setImagePath(saleLabel.getImagePath() != null ? jdImagePath + saleLabel.getImagePath() : null)
                                        .setSkuIdList(skuIdList);
                                brotherSkuSpecValue.setSelected(jdSkuIdSet.contains(skuInnerId));
                                brotherSku.getSpecValues().add(brotherSkuSpecValue);
                            } else {
                                brotherSkuSpecValue.getSkuIdList().addAll(skuIdList);
                            }
                        } else {
                            BrotherSku brotherSku = new BrotherSku();
                            brotherSku.setSpecName(similarSkuGoodsResp.getSaleName()).setSpecValues(new ArrayList<>());

                            BrotherSkuSpecValue brotherSkuSpecValue = new BrotherSkuSpecValue();
                            brotherSkuSpecValue.setSpecValue(labelName)
                                    .setSkuIdList(skuIdList)
                                    .setImagePath(saleLabel.getImagePath() != null ? jdImagePath + saleLabel.getImagePath() : null);
                            brotherSkuSpecValue.setSelected(jdSkuIdSet.contains(skuInnerId));

                            brotherSku.getSpecValues().add(brotherSkuSpecValue);
                            brotherSkuList.add(brotherSku);
                            brotherSkuMap.put(brotherSku.getSpecName(), brotherSku);
                        }
                    }
                });
            }
            return brotherSkuList;
        } else if(supplier.getMultipleSpec() != null && supplier.getMultipleSpec()) {
            List<ProductSkuDO> productSkuDOS = productSkuMapper.selectList(
                    new LambdaQueryWrapperX<ProductSkuDO>().eq(ProductSkuDO::getSpuId, productSkuDO.getSpuId()).select(ProductSkuDO::getId, ProductSkuDO::getPicUrl));
            List<Long> skuIds = productSkuDOS.stream().map(ProductSkuDO::getId).collect(Collectors.toList());
            List<ProductSkuSpecDO> productSkuSpecDOS = productSkuSpecService.getProductSkuSpecBySkuIds(skuIds);
            return ProductSpecConvert.INSTANCE.convertList02(brotherSkuReq.getSkuId(), productSkuDOS, productSkuSpecDOS);
        }

        return new ArrayList<>();
    }

    @Override
    public SkuStockInfo getSkuStockInfo(Long skuId) {
        ProductSkuDO skuDO = getSku(skuId);
        Assert.notNull(skuDO, String.format("商品sku：%d不存在", skuId));
        SkuStockInfo skuStockInfo = new SkuStockInfo();
        if (skuDO.isJd()) {
            String skuInnerId = skuDO.getSkuInnerId();
            VopGoodsDetailResp vopGoodsDetailResp = vopGoodsBridgeService.getSkuStockPriceAndImages(Long.valueOf(skuInnerId));
            if(CollUtil.isNotEmpty(vopGoodsDetailResp.getSkuImageList())) {
                List<String> sliderPicUrls = vopGoodsDetailResp.getSkuImageList()
                        .stream()
                        .map(skuImageItemGoodsResp -> jdImagePath + skuImageItemGoodsResp.getShortPath())
                        .collect(Collectors.toList());
                skuStockInfo.setSliderPicUrls(sliderPicUrls);
            }
            //商品状态
            int jdStatus = 0;
            if (skuId != null && CollUtil.isNotEmpty(vopGoodsDetailResp.getSkuStateList())) {
                jdStatus = vopGoodsDetailResp.getSkuStateList().get(0).getSkuState();
            }
            skuStockInfo.setSkuState(getUnionStatus(skuId, jdStatus));
            //设置售卖状态
            skuStockInfo.setSaleStatus(jdStatus);
            //商品价格
            if (CollUtil.isNotEmpty(vopGoodsDetailResp.getPriceGoodsList())) {
                GetSellPriceGoodsResp getSellPriceGoodsResp = vopGoodsDetailResp.getPriceGoodsList().get(0);
                BigDecimal jdPrice = getSellPriceGoodsResp.getJdPrice();
                BigDecimal salePrice = getSellPriceGoodsResp.getSalePrice();
                BigDecimal marketPrice = getSellPriceGoodsResp.getMarketPrice();
                skuStockInfo.setSalePrice(salePrice);
                skuStockInfo.setMarketPrice(NumberUtil.max(marketPrice, jdPrice != null ? jdPrice : marketPrice));
                if(getSellPriceGoodsResp.getHasPromotion() != null
                        && getSellPriceGoodsResp.getHasPromotion()
                        && getSellPriceGoodsResp.getFixedPricePromotion() != null){
                    skuStockInfo.setHasPromotion(getSellPriceGoodsResp.getHasPromotion());
                    skuStockInfo.setLimitedNum(getSellPriceGoodsResp.getFixedPricePromotion().getLimitedNum());
                    skuStockInfo.setRemainNum(getSellPriceGoodsResp.getFixedPricePromotion().getRemainNum());
                }
                else {
                    skuStockInfo.setHasPromotion(false);
                }
            }
            // 售后提醒
            if(CollUtil.isNotEmpty(vopGoodsDetailResp.getSkuSaleList())) {
                skuStockInfo.setReturnRuleStr(vopGoodsDetailResp.getSkuSaleList().get(0).getReturnRuleStr());
            }
        } else {
            ProductSpuDO spuDO = productSpuService.getSimpleSpu(skuDO.getSpuId());
            skuStockInfo.setSkuState(skuDO.getStatus());
            skuStockInfo.setSalePrice(skuDO.getSalePrice());
            skuStockInfo.setMarketPrice(skuDO.getMarketPrice());
            skuStockInfo.setSliderPicUrls(spuDO.getSliderPicUrls());
            skuStockInfo.setSaleStatus(skuDO.getStatus());
            skuStockInfo.setHasPromotion(false);
        }

        return skuStockInfo;
    }

    @Override
    public List<SkuGoodsPageItem> getSkuSuggestList(VopGoodsGetSkuDetailReq skuDetailReq) {
        String areaIds = StringUtils.isNotBlank(skuDetailReq.getAreaIds()) ? skuDetailReq.getAreaIds() : DEFAULT_AREA_IDS;
        ProductSkuDO sku = this.getSku(skuDetailReq.getSkuId());
        Assert.notNull(sku, "商品sku信息不存在");
        ProductSpuDO spu = productSpuService.getSimpleSpu(sku.getSpuId());
        Assert.notNull(spu, "商品spu信息不存在");

        if(spu != null) {
            GoodsSearchReq goodsSearchReq =new GoodsSearchReq().setSortType(SortTypeEnum.SALE_DESC.getCode()).setAreaIds(areaIds);
            if(spu.getCategory3Id() != null) {
                goodsSearchReq.setCategoryId3(Long.valueOf(spu.getCategory3Id()));
            } else if(spu.getCategory2Id() != null) {
                goodsSearchReq.setCategoryId2(Long.valueOf(spu.getCategory2Id()));
            } else if(spu.getCategory1Id() != null) {
                goodsSearchReq.setCategoryId1(Long.valueOf(spu.getCategory1Id()));
            }
            GoodsSearchPageResultResp goodsSearchPageResultResp = productSkuElasticsearchService.goodsSearchPageList(goodsSearchReq);
            List<SkuGoodsPageItem> skuGoodsPageItems = goodsSearchPageResultResp.getPageResult().getList();
            return skuGoodsPageItems;
        }

        return null;
    }

    @Override
    public List<AppProductSkuDetailRespVO> getProductSkuDetailList(List<Long> skuIds) {
        List<ProductSkuDO> productSkuDOS = getSkuList(skuIds);
        if(CollectionUtils.isEmpty(productSkuDOS)) {
            return null;
        }
        List<ProductSkuSpecDO> skuSpecDOS = productSkuSpecService.getProductSkuSpecBySkuIds(skuIds);
        Map<Long, List<ProductSkuSpecDO>> skuSpecMap = convertMultiMap(skuSpecDOS, ProductSkuSpecDO::getSkuId);

        List<Long> spuIds = productSkuDOS.stream().map(ProductSkuDO::getSpuId).distinct().collect(Collectors.toList());
        List<ProductSpuDO> productSpuDOS = productSpuService.getSpuList(spuIds, false);
        Map<Long, ProductSpuDO> productSpuDOMap = convertMap(productSpuDOS, ProductSpuDO::getId);

        List<ProductSpuSpecDO> productSpuSpecDOS = productSpuSpecService.getSpuSpecListBySpuIds(spuIds);
        Map<Long, List<ProductSpuSpecDO>> productSpuSpecMap = convertMultiMap(productSpuSpecDOS, ProductSpuSpecDO::getSpuId);
        return productSkuDOS.stream()
                .map(productSkuDO -> ProductSkuConvert.INSTANCE.convertSkuDetailResp(productSkuDO, productSpuDOMap.get(productSkuDO.getSpuId()), productSpuSpecMap.get(productSkuDO.getSpuId()), skuSpecMap.get(productSkuDO.getId())))
                .collect(Collectors.toList());
    }

    @Override
    public List<AppProductSkuDetailRespVO> getProductSkuDetailListNoSort(List<Long> skuIds) {
        List<AppProductSkuDetailRespVO> list = getProductSkuDetailList(skuIds);
        if(CollectionUtils.isNotEmpty(list)) {
            list.sort(Comparator.comparingInt(productSkuDetailRespVO -> skuIds.indexOf(productSkuDetailRespVO.getSkuId())));
        }

        return list;
    }

    @Override
    public void syncAllProductSkuIndex2ES() {
        productSkuElasticsearchService.deleteAllOfTenant(TenantContextHolder.getTenantId());
        doSyncProductSkuIndex2ES(null);
    }

    @Override
    public void syncProductSkuIndex2ESRecently(int days) {
        Date updateTime = DateUtils.addMinutes(new Date(), days * -1);
        doSyncProductSkuIndex2ES(updateTime);
    }

    private void doSyncProductSkuIndex2ES(Date updateTime) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        int pageNo = 1, pageSize = 500;
        AppProductSkuPageReqVO pageReqVO = assembleProductSkuPageReqVO(pageNo, pageSize, updateTime);
        PageResult<ProductSkuDO> pageResult = this.baseMapper.selectSkuIdPage(pageReqVO);
        int saveSize = 0, deleteSize = 0;
        List<SupplierDO> supplierDOS = supplierService.getAllSupplierList();
        Map<Long, SupplierDO> supplierMap = convertMap(supplierDOS, SupplierDO::getId);
        while (CollUtil.isNotEmpty(pageResult.getList())) {
            List<ProductSkuDO> productSkuDOS = pageResult.getList();
            List<Long> skuIds = productSkuDOS.stream().map(ProductSkuDO::getId).collect(Collectors.toList());
            List<AppProductSkuDetailRespVO> productSkuDetailList = getProductSkuDetailList(skuIds);
            if(CollUtil.isNotEmpty(productSkuDetailList)) {
                // 根据SKU状态，供应商状态，以及分类状态进行过滤
                List<AppProductSkuDetailRespVO> enableList = filterIndexInNeed(productSkuDetailList, supplierMap);
                List<Long> enableSkuIds = enableList.stream().map(AppProductSkuDetailRespVO::getSkuId).collect(Collectors.toList());
                skuIds.removeAll(enableSkuIds);

                // 生成索引
                if(CollUtil.isNotEmpty(enableSkuIds)) {
                    saveProductIndex2ES(enableList, supplierMap);
                    saveSize += enableSkuIds.size();
                }

                // 删除索引
                if(CollUtil.isNotEmpty(skuIds)) {
                    deleteProductIndexInES(skuIds);
                    deleteSize += skuIds.size();
                }
            }

            pageReqVO.setPageNo(++pageNo);
            pageResult = this.baseMapper.selectSkuIdPage(pageReqVO);
        }
        //找出逻辑删除的SKU并删除索引，减少资源浪费
        pageNo = 1;
        pageReqVO.setPageNo(pageNo);
        pageReqVO.setPageSize(1000);
        PageResult<ProductSkuDO> deletePageResult = this.baseMapper.selectDeleteSkuIdPage(pageReqVO);
        while (CollUtil.isNotEmpty(deletePageResult.getList())) {
            List<ProductSkuDO> productSkuDOS = deletePageResult.getList();
            List<Long> skuIds = productSkuDOS.stream().map(ProductSkuDO::getId).collect(Collectors.toList());
            deleteProductIndexInES(skuIds);
            deleteSize += skuIds.size();

            pageReqVO.setPageNo(++pageNo);
            deletePageResult = this.baseMapper.selectDeleteSkuIdPage(pageReqVO);
        }
        stopWatch.stop();
        log.info("syncProductSkuIndex2ESByDate save {}, delete: {}, totalTime: {}", saveSize, deleteSize, stopWatch.getTotalTimeMillis());
    }

    @Override
    public void syncProductSkuIndex2ES(List<Long> skuIds) {
        if(CollUtil.isEmpty(skuIds)) {
            return;
        }
        List<AppProductSkuDetailRespVO> productSkuDetailList = getProductSkuDetailList(skuIds);
        int enableSize = 0, disableSize = 0;
        if(CollUtil.isNotEmpty(productSkuDetailList)) {
            List<Long> supplierIds = productSkuDetailList.stream().map(AppProductSkuDetailRespVO::getSupplierId).collect(Collectors.toList());
            List<SupplierDO> supplierDOS = supplierService.getSupplierList(supplierIds);
            Map<Long, SupplierDO> supplierMap = convertMap(supplierDOS, SupplierDO::getId);

            // 根据SKU状态，供应商状态，以及分类状态进行过滤
            List<AppProductSkuDetailRespVO> enableList = filterIndexInNeed(productSkuDetailList, supplierMap);
            productSkuDetailList.removeAll(enableList);
            // 生成索引
            if(CollUtil.isNotEmpty(enableList)) {
                enableSize = enableList.size();
                saveProductIndex2ES(enableList, supplierMap);
            }
            // 删除索引
            if(CollUtil.isNotEmpty(productSkuDetailList)) {
                List<Long> disableSkuIds = productSkuDetailList.stream().map(AppProductSkuDetailRespVO::getSkuId).collect(Collectors.toList());
                disableSize = disableSkuIds.size();
                deleteProductIndexInES(disableSkuIds);
            }
        } else {
            // 不存在的sku直接进行删除操作
            disableSize = skuIds.size();
            deleteProductIndexInES(skuIds);
        }

        log.info("syncProductSkuIndex2ESById save {}, delete: {}, skuIds：{}", enableSize, disableSize, skuIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
    }

    private void saveProductIndex2ES(List<AppProductSkuDetailRespVO> skuDetailList, Map<Long, SupplierDO> supplierMap) {
        try {
            if(CollectionUtils.isEmpty(skuDetailList)) {
                return;
            }
            List<Long> skuIds = skuDetailList.stream().map(AppProductSkuDetailRespVO::getSkuId).collect(Collectors.toList());

            Map<Long, List<Long>> skuTagIdMap = productTagSkuService.getTagIdListMap(skuIds);
            List<ProductSkuES> productSkuES = ProductSkuESConvert.INSTANCE.convertList02(skuDetailList, supplierMap, skuTagIdMap);
            log.info("save product to es size: {}",productSkuES.size());
            productSkuElasticsearchService.saveAll(productSkuES);
        } catch (Exception e) {
            log.error("ES save error", e);
        }

    }

    private void deleteProductIndexInES(List<Long> skuIds) {
        // 执行删除处理
        try {
            productSkuElasticsearchService.deleteAll(skuIds);
        } catch(Exception e) {
            log.error("ES delete error: {}", skuIds, e);
        }
    }

    private List<AppProductSkuDetailRespVO> filterIndexInNeed(List<AppProductSkuDetailRespVO> skuDetailList, Map<Long, SupplierDO> supplierMap) {
        List<AppProductSkuDetailRespVO> enableList = Collections.synchronizedList(new ArrayList<>());
        skuDetailList.forEach(sku -> {
            if(!ProductSpuStatusEnum.ENABLE.getStatus().equals(sku.getStatus())) {
                log.info("商品SKU无须生成索引:{},商品已下架", sku.getSkuId());
                return;
            }
            if(!ProductSkuShowStatusEnum.SHOW.getStatus().equals(sku.getShowStatus())) {
                log.info("商品SKU无须生成索引:{},商品没有列出", sku.getSkuId());
                return;
            }
            if(!ProductSpuStatusEnum.ENABLE.getStatus().equals(sku.getPlatformStatus())) {
                log.info("商品SKU无须生成索引:{},商品平台状态为下架", sku.getSkuId());
                return;
            }
            if(!supplierMap.containsKey(sku.getSupplierId())) {
                log.info("商品SKU无须生成索引:{},供应商ID不存在: {}", sku.getSkuId(), sku.getSupplierId());
                return;
            }
            if(!SupplierStatusEnum.ENABLE.getStatus().equals(supplierMap.get(sku.getSupplierId()).getStatus())) {
                log.info("商品SKU无须生成索引:{},供应商未启用:{}", sku.getSkuId(), sku.getSupplierId());
                return;
            }

            String fullCategoryIds = sku.getFullCategoryId();
            if(StringUtils.isNotBlank(fullCategoryIds)) {
                String[] ids = StringUtils.split(fullCategoryIds, "-");
                Set<Long> cateIds = Arrays.stream(ids).mapToLong(Long::parseLong).boxed().collect(Collectors.toSet());

                boolean allCateEnable = productCategoryService.isAllEnable(cateIds);
                if(!allCateEnable) {
                    log.info("商品SKU无须生成索引:{},商品分类存在未启用", sku.getSkuId(), cateIds);
                    return;
                }
            }
            enableList.add(sku);
        });

        return enableList;
    }

    @Override
    public ProductSkuDO getSkuByIdOrInnerId(String skuId) {
        return this.baseMapper.getSkuByIdOrInnerId(skuId);
    }

    @Override
    public List<ProductSkuDO> getSkuByIdOrInnerIds(List<String> skuIds) {
        return this.baseMapper.getSkuByIdOrInnerIds(skuIds);
    }

    @Override
    public List<Long> getSkuIdList(ProductSkuPageReqVO reqVO) {
        return this.baseMapper.selectSkuId(reqVO);
    }

    @Override
    public List<SkuPriceCompareRespVO> skuPriceCompare(List<Long> skuIds) {
        Assert.isTrue(skuIds.size() >= 2, "商品比价至少选择2件商品");
        Assert.isTrue(skuIds.size() <= 5, "商品比价最多选择5件商品");
        List<SkuPriceCompareRespVO> skuPriceCompareRespVOS = new ArrayList<>();
        for (Long skuId : skuIds) {
            ProductSkuDO productSkuDO = this.getSku(skuId);
            Assert.notNull(productSkuDO, String.format("商品不存在, skuId:%d", skuId));
            ProductSpuDO productSpuDO = productSpuService.getSimpleSpu(productSkuDO.getSpuId());
            List<ProductSpuSpecDO> productSpuSpecDOS = productSpuSpecService.getSpuSpecListBySpuId(productSkuDO.getSpuId());
            List<AppProductSpecValueVO> specValueVOList = ProductSpecConvert.INSTANCE.convertVOList03(productSpuSpecDOS);
            skuPriceCompareRespVOS.add(new SkuPriceCompareRespVO().setSkuId(productSkuDO.getId())
                    .setSkuInnerId(productSkuDO.getSkuInnerId())
                    .setSkuName(productSkuDO.getSkuName())
                    .setSupplierId(productSkuDO.getSupplierId())
                    .setSupplierName(productSkuDO.getSupplierName())
                    .setPicUrl(productSkuDO.getPicUrl())
                    .setMarketPrice(productSkuDO.getMarketPrice())
                    .setSalePrice(productSkuDO.getSalePrice())
                    .setBrandId(productSpuDO.getBrandId())
                    .setBrandName(productSpuDO.getBrandName())
                    .setUnit(productSpuDO.getUnit())
                    .setStatus(productSkuDO.getStatus())
                    .setSpecValueList(specValueVOList));
        }

        return skuPriceCompareRespVOS;
    }

    @Override
    public List<ProductSkuStatusResult> getProductSkuStatusResult(Long tenantId, Long supplierId) {
        return this.baseMapper.getProductSkuStatusResult(tenantId, supplierId);
    }

    @Override
    public SkuSummaryRespVO getSkuTotal() {
        Long enableTotal = productSkuMapper.selectCount(
                new LambdaQueryWrapperX<ProductSkuDO>().eq(ProductSkuDO::getStatus, ProductSpuStatusEnum.ENABLE.getStatus()));
        Long disableTotal = productSkuMapper.selectCount(
                new LambdaQueryWrapperX<ProductSkuDO>().eq(ProductSkuDO::getStatus, ProductSpuStatusEnum.DISABLE.getStatus()));
        Long categoryCount = categoryService.getCategoryCount();
        return new SkuSummaryRespVO()
                .setListingTotal(enableTotal)
                .setDelistingTotal(disableTotal)
                .setTotal(enableTotal + disableTotal)
                .setCategoryCount(categoryCount);
    }

    @Override
    public List<SupplierSkuSummaryRespVO> getSupplierSkuTotal() {
        List<SupplierSkuSummaryRespVO> supplierSkuSummaryRespVOS = new ArrayList<>();
        List<SupplierDO> supplierDOS = supplierService.getAllEnabledSupplierList();
        for (SupplierDO supplierDO : supplierDOS) {
            boolean warn = false;
            Long enableTotal = productSkuMapper.selectCount(
                    new LambdaQueryWrapperX<ProductSkuDO>().eq(ProductSkuDO::getStatus, ProductSpuStatusEnum.ENABLE.getStatus())
                            .eq(ProductSkuDO::getSupplierId, supplierDO.getId()));
            Long disableTotal = productSkuMapper.selectCount(
                        new LambdaQueryWrapperX<ProductSkuDO>()
                        .eq(ProductSkuDO::getStatus, ProductSpuStatusEnum.DISABLE.getStatus()).eq(ProductSkuDO::getSupplierId, supplierDO.getId()));
            SkuSummaryRespVO skuSummaryRespVO = new SkuSummaryRespVO()
                    .setListingTotal(enableTotal)
                    .setDelistingTotal(disableTotal)
                    .setTotal(enableTotal + disableTotal);

            // 只有接入型供应商才会告警
            if(Objects.equals(supplierDO.getType(), SupplierTypeEnum.INTEGRATED.getType()) && !Objects.equals(supplierDO.getType(), SupplierTypeEnum.JD.getType())){
                Integer skuLimit = Optional.ofNullable(supplierDO.getSkuLimit()).orElse(1000).intValue();
                warn = (enableTotal + disableTotal >= skuLimit * 0.9);
            }

            supplierSkuSummaryRespVOS.add(new SupplierSkuSummaryRespVO()
                    .setSupplierId(supplierDO.getId())
                    .setSupplierName(supplierDO.getName())
                    .setSkuSummaryRespVO(skuSummaryRespVO)
                    .setWarn(warn));
        }

        return supplierSkuSummaryRespVOS;
    }

    private AppProductSkuPageReqVO assembleProductSkuPageReqVO(Integer pageNo, Integer pageSize, Date updateTime) {
        AppProductSkuPageReqVO pageReqVO = new AppProductSkuPageReqVO();
        pageReqVO.setPageNo(pageNo);
        pageReqVO.setPageSize(pageSize);
        pageReqVO.setUpdateTime(updateTime);
        return pageReqVO;
    }


    private AppAppProductSkuOpenRespVO assembleProductSpuOpenVO(ProductSpuDO spu, List<ProductSpuSpecDO> spuSpecDOS, ProductSkuDO sku, List<ProductSkuSpecDO> skuSpecDOS, ProductSkuStockDO skuStockDO) {
        ProductSpuOpenRespVO productSpuOpenRespVO = ProductSpuConvert.INSTANCE.convertVO(spu);
        List<AppProductSpecValueVO> spuSpecValueList = ProductSpecConvert.INSTANCE.convertVOList03(spuSpecDOS);
        productSpuOpenRespVO.setSpuSpecValueList(spuSpecValueList);
        AppAppProductSkuOpenRespVO appProductSkuOpenRespVO = ProductSkuConvert.INSTANCE.convertVO(sku);
        List<AppProductSpecValueVO> skuSpecValueList = ProductSpecConvert.INSTANCE.convertVOList04(skuSpecDOS);
        appProductSkuOpenRespVO.setSkuSpecValueList(skuSpecValueList);
        appProductSkuOpenRespVO.setSpu(productSpuOpenRespVO);
        if(skuStockDO != null) {
            appProductSkuOpenRespVO.setStockCount(skuStockDO.getStock());
        }
        return appProductSkuOpenRespVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSkuStock(ProductSkuUpdateStockReqDTO updateStockReqDTO) {
        // 更新 SKU 库存
        List<Long> skuIds = new ArrayList<>();
        updateStockReqDTO.getItems().forEach(item -> {
            if (item.getIncrCount() > 0) {
                productSkuMapper.updateStockIncr(item.getId(), item.getIncrCount());
            } else if (item.getIncrCount() < 0) {
                int updateStockIncr = productSkuMapper.updateStockDecr(item.getId(), item.getIncrCount());
                if (updateStockIncr == 0) {
                    throw exception(SKU_STOCK_NOT_ENOUGH);
                }
            }
            skuIds.add(item.getId());
        });

        // 更新 SPU 库存
        List<ProductSkuDO> skus = productSkuMapper.selectBatchIds(
                convertSet(updateStockReqDTO.getItems(), ProductSkuUpdateStockReqDTO.Item::getId));
        Map<Long, Integer> spuStockIncrCounts = ProductSkuConvert.INSTANCE.convertSpuStockMap(
                updateStockReqDTO.getItems(), skus);
        productSpuService.updateSpuStock(spuStockIncrCounts);

        syncProductIndex(skuIds);
    }

    @Override
    public void updateOffJdSku(String skuInnerId, Long supplierId) {
        ProductSkuDO skuDO = productSkuMapper.selectOne(Wrappers.lambdaQuery(ProductSkuDO.class)
                .select(ProductSkuDO::getId)
                .eq(ProductSkuDO::getSkuInnerId, skuInnerId)
                .eq(ProductSkuDO::getSupplierId, supplierId)
                .orderByAsc(ProductSkuDO::getCreateTime)
                .last(" limit 1 "));

        if(skuDO != null) {
            skuDO.setStatus(ProductSpuStatusEnum.DISABLE.getStatus());
            productSkuMapper.updateById(skuDO);
            syncProductIndex(Arrays.asList(skuDO.getId()));
        }
    }

    @Override
    public PageResult<ProductSkuPageRespVO> selectPage2(ProductSkuPageReqVO reqVO) {
        // 分页查询
        Page<ProductSkuPageRespVO> pageInfo = new Page(reqVO.getPageNo(), reqVO.getPageSize());
        productSkuMapper.selectPage2(pageInfo, reqVO);

        PageResult<ProductSkuPageRespVO> pageResult = new PageResult((int)pageInfo.getCurrent(), (int)pageInfo.getSize(), (int)pageInfo.getPages(), pageInfo.getRecords(), pageInfo.getTotal());
        return pageResult;
    }

    @Override
    public ProductSkuDetailRespVO selectSkuDetail2(ProductSkuReqVO reqVO) {
        return productSkuMapper.selectSkuDetail2(reqVO);
    }

    @Override
    public void importSeoList(List<ProductSeoSkuImportExcelVO> list) {
        asyncFrontTaskUtils.asyncTask(SecurityFrameworkUtils.getLoginUserId(), AsyncTaskTypeEnum.PRODUCT_SKU_SEO_IMPORT, TtlRunnable.get(() -> {
            doImportSeoList(list);
        }));
    }

    public void doImportSeoList(List<ProductSeoSkuImportExcelVO> list) {
        String taskId = AsyncFrontTaskContext.getTaskId();
        AsyncImportTask importTask = asyncFrontTaskUtils.getAsyncImportTask(taskId);

        if(CollUtil.isEmpty(list)) {
            asyncFrontTaskUtils.taskDone(importTask);
            return;
        }

        try {
            importTask.setTotal(list.size());
            List<AsyncImportRespExcelVO> importRespList = new ArrayList<>();

            AtomicInteger counter = new AtomicInteger(1);
            asyncFrontTaskUtils.updateTask(importTask);

            List<ProductSkuDO> updates = new ArrayList<>();
            List<Long> skuIds = new ArrayList<>();
            list.stream().forEach(skuInfo -> {
                counter.incrementAndGet();
                if(counter.get() % 10 == 0) {
                    asyncFrontTaskUtils.updateTask(importTask);
                }

                String skuId = skuInfo.getSkuId() != null ? skuInfo.getSkuId().toString() : skuInfo.getSkuInnerId();
                try {
                    // 字段校验
                    validateProductSeoSku(skuInfo);
                } catch (Exception ex) {
                    importRespList.add(importTask.plusFailed(skuId, ex.getMessage()));
                    return;
                }

                ProductSkuDO persistDO = productSkuMapper.selectOne(Wrappers.lambdaQuery(ProductSkuDO.class).select(ProductSkuDO::getId)
                        .eq(ProductSkuDO::getSupplierId, skuInfo.getSupplierId())
                        .eq(skuInfo.getSkuId() != null, ProductSkuDO::getId, skuInfo.getSkuId())
                        .eq(StringUtils.isNotBlank(skuInfo.getSkuInnerId()), ProductSkuDO::getSkuInnerId, skuInfo.getSkuInnerId()).last(" limit 1 "));
                if(persistDO != null) {
                    ProductSkuDO updateDO = new ProductSkuDO();
                    updateDO.setId(persistDO.getId());
                    if(skuInfo.getInitSalesCount() != null) {
                        updateDO.setInitSalesCount(skuInfo.getInitSalesCount());
                    }
                    updateDO.setSeoStatus(ProductSkuSeoStatusEnum.OPEN.getStatus());

                    updates.add(updateDO);
                    skuIds.add(persistDO.getId());

                    importRespList.add(importTask.plusUpdated(skuId));
                } else {
                    importRespList.add(importTask.plusFailed(skuId, "不存在"));
                }
            });
            log.info("运营商品导入SKU数量：{}", updates.size());
            if(CollUtil.isNotEmpty(updates)) {
                productSkuMapper.updateBatch(updates, 1000);
                asyncFrontTaskUtils.updateTask(importTask);
                try {
                    syncProductIndex(skuIds);
                } catch(Exception e) {
                    log.error("运营商品导入完成状态更新异常:", e);
                }
            }
            asyncFrontTaskUtils.importDone(taskId, importRespList);
        } catch (Exception e) {
            log.error("商品SKU导入异常", e);
            asyncFrontTaskUtils.importFail();
        }
    }

    private void validateProductSeoSku(ProductSeoSkuImportExcelVO bean) {
        if(bean.getSupplierId() == null) {
            throw ServiceExceptionUtil.exception(SKU_SEO_IMPORT_SUPPLIER_EMPTY);
        }
        if(bean.getSkuId() == null && StringUtils.isBlank(bean.getSkuInnerId())) {
            throw ServiceExceptionUtil.exception(SKU_SEO_IMPORT_SKU_EMPTY);
        }
        if(bean.getInitSalesCount() == null) {
//            throw ServiceExceptionUtil.exception(SKU_SEO_IMPORT_INNIT_SALES_COUNT_EMPTY);
        }
    }

    @Override
    public void importTagList(List<ProductSkuTagImportExcelVO> list) {
        asyncFrontTaskUtils.asyncTask(SecurityFrameworkUtils.getLoginUserId(), AsyncTaskTypeEnum.PRODUCT_SKU_TAG_IMPORT, TtlRunnable.get(() -> {
            doImportTagList(list);
        }));
    }

    public void doImportTagList(List<ProductSkuTagImportExcelVO> list) {
        String taskId = AsyncFrontTaskContext.getTaskId();
        AsyncImportTask importTask = asyncFrontTaskUtils.getAsyncImportTask(taskId);

        if(CollUtil.isEmpty(list)) {
            asyncFrontTaskUtils.taskDone(importTask);
            return;
        }

        try {
            importTask.setTotal(list.size());
            List<AsyncImportRespExcelVO> importRespList = new ArrayList<>();

            AtomicInteger counter = new AtomicInteger(1);
            asyncFrontTaskUtils.updateTask(importTask);

            List<ProductSkuDO> updates = new ArrayList<>();
            Map<Long, List<Long>> skuTagMap = new HashMap<>();
            list.forEach(skuInfo -> {
                counter.incrementAndGet();
                if(counter.get() % 10 == 0) {
                    asyncFrontTaskUtils.updateTask(importTask);
                }

                String skuId = skuInfo.getSkuId() != null ? skuInfo.getSkuId().toString() : skuInfo.getSkuInnerId();
                try {
                    // 字段校验
                    validateProductSkuTag(skuInfo);
                } catch (Exception ex) {
                    importRespList.add(importTask.plusFailed(skuId, ex.getMessage()));
                    return;
                }

                ProductSkuDO persistDO = productSkuMapper.selectOne(Wrappers.lambdaQuery(ProductSkuDO.class).select(ProductSkuDO::getId)
                        .eq(ProductSkuDO::getSupplierId, skuInfo.getSupplierId())
                        .eq(skuInfo.getSkuId() != null, ProductSkuDO::getId, skuInfo.getSkuId())
                        .eq(StringUtils.isNotBlank(skuInfo.getSkuInnerId()), ProductSkuDO::getSkuInnerId, skuInfo.getSkuInnerId()).last(" limit 1 "));
                if(persistDO != null) {
                    ProductSkuDO updateDO = new ProductSkuDO()
                            .setId(persistDO.getId())
                            .setSeoStatus(ProductSkuSeoStatusEnum.OPEN.getStatus());
                    if(skuInfo.getInitSalesCount() != null) {
                        updateDO.setInitSalesCount(skuInfo.getInitSalesCount());
                    }
                    updates.add(updateDO);
                    skuTagMap.put(persistDO.getId(), skuInfo.getTagIdList());

                    importRespList.add(importTask.plusUpdated(skuId));
                } else {
                    importRespList.add(importTask.plusFailed(skuId, "不存在"));
                }
            });
            if(CollUtil.isNotEmpty(updates)) {
                productSkuMapper.updateBatch(updates, 1000);
                productTagSkuService.saveTagSkuBatch(skuTagMap);
            }

            asyncFrontTaskUtils.updateTask(importTask);
            asyncFrontTaskUtils.importDone(taskId, importRespList);
        } catch (Exception e) {
            log.error("商品标签导入异常", e);
            asyncFrontTaskUtils.importFail();
        }
    }

    private void validateProductSkuTag(ProductSkuTagImportExcelVO bean) {
        if(bean.getSupplierId() == null) {
            throw ServiceExceptionUtil.exception(SKU_SEO_IMPORT_SUPPLIER_EMPTY);
        }
        if(bean.getSkuId() == null && StringUtils.isBlank(bean.getSkuInnerId())) {
            throw ServiceExceptionUtil.exception(SKU_SEO_IMPORT_SKU_EMPTY);
        }
        if(StringUtils.isBlank(bean.getTagIds())) {
            throw ServiceExceptionUtil.exception(SKU_TAG_IMPORT_TAG_EMPTY);
        }
        if(bean.getInitSalesCount() < 0) {
            throw ServiceExceptionUtil.exception(SKU_SEO_IMPORT_INNIT_SALES_INVALID);
        }
    }

    @Override
    public void resetSkuSeoStatus(List<Long> skuIdList) {
        if(CollUtil.isEmpty(skuIdList)) {
            return;
        }
        productSkuMapper.update(null, Wrappers.lambdaUpdate(ProductSkuDO.class)
                .in(ProductSkuDO::getId, skuIdList)
                .set(ProductSkuDO::getSeoStatus, ProductSkuSeoStatusEnum.CLOSE.getStatus())
                .set(ProductSkuDO::getInitSalesCount, 0));

        productTagSkuService.deleteTagSkuBySkuV2(skuIdList);

        syncProductIndex(skuIdList);
    }

    @Override
    public void addSkuSeo(List<Long> skuIdList) {
        if(CollUtil.isEmpty(skuIdList)) {
            return;
        }
        productSkuMapper.update(null, Wrappers.lambdaUpdate(ProductSkuDO.class)
                .in(ProductSkuDO::getId, skuIdList)
                .set(ProductSkuDO::getSeoStatus, ProductSkuSeoStatusEnum.OPEN.getStatus()));
    }

    @Override
    public void setSalePriceIfNotLoggedIn(SkuStockInfo skuStockInfo) {
        if (getLoginUser() == null && Optional.ofNullable(basisConfigService.getBasisConfig())
                .orElseThrow(() -> exception(BASIS_CONFIG_NOT_EXISTS)).showPriceOnLogin()) {
            skuStockInfo.setSalePrice(BigDecimal.valueOf(-1)).setMarketPrice(BigDecimal.valueOf(-1));
        }
    }

    @Override
    public void setSalePriceIfNotLoggedIn(AppSkuDetailInfo appSkuDetailInfo) {
        if (getLoginUser() == null && Optional.ofNullable(basisConfigService.getBasisConfig())
                .orElseThrow(() -> exception(BASIS_CONFIG_NOT_EXISTS)).showPriceOnLogin()) {
            appSkuDetailInfo.setSalePrice(BigDecimal.valueOf(-1)).setMarketPrice(BigDecimal.valueOf(-1));
        }
    }

    @Override
    public void setSalePriceIfNotLoggedIn(List<?> items) {
        if (getLoginUser() == null && Optional.ofNullable(basisConfigService.getBasisConfig())
                .orElseThrow(() -> exception(BASIS_CONFIG_NOT_EXISTS)).showPriceOnLogin()) {
            for (Object item : Optional.ofNullable(items).orElse(Collections.emptyList())) {
                if (item instanceof VopSkuGoodsPageItem) {
                    ((VopSkuGoodsPageItem) item).setSalePrice(BigDecimal.valueOf(-1))
                            .setMarketPrice(BigDecimal.valueOf(-1));
                } else if (item instanceof SkuGoodsPageItem) {
                    ((SkuGoodsPageItem) item).setSalePrice(BigDecimal.valueOf(-1))
                            .setMarketPrice(BigDecimal.valueOf(-1));
                }
            }
        }
    }

    @Override
    public void setSalePriceIfNotLoggedIn(PageResult<?> pageResult) {
        if (getLoginUser() == null && Optional.ofNullable(basisConfigService.getBasisConfig())
                .orElseThrow(() -> exception(BASIS_CONFIG_NOT_EXISTS)).showPriceOnLogin()) {
            for (Object item : Optional.ofNullable(pageResult.getList()).orElse(Collections.emptyList())) {
                if (item instanceof VopSkuGoodsPageItem) {
                    ((VopSkuGoodsPageItem) item).setSalePrice(BigDecimal.valueOf(-1))
                            .setMarketPrice(BigDecimal.valueOf(-1));
                } else if (item instanceof SkuGoodsPageItem) {
                    ((SkuGoodsPageItem) item).setSalePrice(BigDecimal.valueOf(-1))
                            .setMarketPrice(BigDecimal.valueOf(-1));
                }
            }
        }
    }

    @Override
    public void updateSkuIndexBySupplierStatus(SupplierDO supplier) {
        if(supplier.isEnabled()) {
            ProductSkuPageReqVO reqVO = new ProductSkuPageReqVO();
            reqVO.setSupplierId(supplier.getId());
            List<Long> skuIds = getSkuIdList(reqVO);
            if(CollUtil.isEmpty(skuIds)) {
                return;
            }
            syncProductIndex(skuIds);
        } else {
            productSkuElasticsearchService.deleteBySupplierId(supplier.getId());
        }
    }

    @Override
    public void updateSkuIndexByCategoryStatus(ProductCategoryDO categoryDO) {
        ProductSkuPageReqVO reqVO = new ProductSkuPageReqVO();
        if(ObjectUtil.equal(categoryDO.getCategoryLevel(), 0)) {
            reqVO.setCategory1Id(categoryDO.getCategoryId());
        } else if(ObjectUtil.equal(categoryDO.getCategoryLevel(), 1)) {
            reqVO.setCategory2Id(categoryDO.getCategoryId());
        } else if(ObjectUtil.equal(categoryDO.getCategoryLevel(), 2)) {
            reqVO.setCategory3Id(categoryDO.getCategoryId());
        } else {
            return;
        }
        List<Long> skuIds = getSkuIdList(reqVO);
        if(CollUtil.isEmpty(skuIds)) {
            return;
        }
        if(categoryDO.isEnabled()) {
            syncProductIndex(skuIds);
        } else {
            productSkuElasticsearchService.deleteAll(skuIds);
        }
    }

    @Override
    public String exportSku(ProductSkuExportReqVO exportReqVO) {
        String taskId = AsyncFrontTaskContext.getTaskId();
        asyncFrontTaskUtils.asyncTask(SecurityFrameworkUtils.getLoginUserId(), AsyncTaskTypeEnum.PRODUCT_SKU_EXPORT, TtlRunnable.get(() -> {
            try {
                asyncFrontTaskUtils.updateExportProgress(30, null);
                List<ProductSkuExportRespVO> exportList = productSkuMapper.selectExportSku(exportReqVO);
                asyncFrontTaskUtils.updateExportProgress(50, null);
                String fileUrl = asyncFrontTaskUtils.exportDone(taskId, exportList);
                asyncFrontTaskUtils.updateExportProgress(100, fileUrl);
            } catch (Exception e) {
                asyncFrontTaskUtils.exportFail();
                log.error("async-export fail", e);
            }
        }));
        return taskId;
    }

    @Override
    public String exportSeoSku(ProductSkuExportReqVO exportReqVO) {
        String taskId = AsyncFrontTaskContext.getTaskId();
        asyncFrontTaskUtils.asyncTask(SecurityFrameworkUtils.getLoginUserId(), AsyncTaskTypeEnum.PRODUCT_SEO_SKU_EXPORT, TtlRunnable.get(() -> {
            try {
                asyncFrontTaskUtils.updateExportProgress(30, null);
                List<ProductSeoSkuExportRespVO> exportList = productSkuMapper.selectExportSeoSku(exportReqVO);
                asyncFrontTaskUtils.updateExportProgress(50, null);
                productTagSkuService.fillTag2(exportList);
                asyncFrontTaskUtils.updateExportProgress(80, null);
                String fileUrl = asyncFrontTaskUtils.exportDone(taskId, exportList);
                asyncFrontTaskUtils.updateExportProgress(100, fileUrl);
            } catch (Exception e) {
                asyncFrontTaskUtils.exportFail();
                log.error("async-export fail", e);
            }
        }));
        return taskId;
    }

    @Override
    public List<ProductSkuIndexStatusRespVO> batchQuerySkuIndexStatus(List<Long> skuIds) {
        if (CollUtil.isEmpty(skuIds)) {
            return new ArrayList<>();
        }

        // 获取SKU详情信息
        List<AppProductSkuDetailRespVO> productSkuDetailList = getProductSkuDetailList(skuIds);
        if (CollUtil.isEmpty(productSkuDetailList)) {
            // 如果没有找到任何SKU，返回不存在的状态
            return skuIds.stream().map(skuId -> {
                ProductSkuIndexStatusRespVO statusVO = new ProductSkuIndexStatusRespVO();
                statusVO.setSkuId(skuId);
                statusVO.setNeedIndex(false);
                statusVO.setReason("SKU不存在");
                return statusVO;
            }).collect(Collectors.toList());
        }

        // 获取供应商信息
        List<Long> supplierIds = productSkuDetailList.stream()
                .map(AppProductSkuDetailRespVO::getSupplierId)
                .distinct()
                .collect(Collectors.toList());
        List<SupplierDO> supplierDOS = supplierService.getSupplierList(supplierIds);
        Map<Long, SupplierDO> supplierMap = convertMap(supplierDOS, SupplierDO::getId);

        // 构建结果列表
        List<ProductSkuIndexStatusRespVO> resultList = new ArrayList<>();
        Map<Long, AppProductSkuDetailRespVO> skuDetailMap = convertMap(productSkuDetailList, AppProductSkuDetailRespVO::getSkuId);

        for (Long skuId : skuIds) {
            ProductSkuIndexStatusRespVO statusVO = new ProductSkuIndexStatusRespVO();
            statusVO.setSkuId(skuId);

            AppProductSkuDetailRespVO skuDetail = skuDetailMap.get(skuId);
            if (skuDetail == null) {
                // SKU不存在
                statusVO.setNeedIndex(false);
                statusVO.setReason("SKU不存在");
                resultList.add(statusVO);
                continue;
            }

            // 填充基本信息
            fillSkuBasicInfo(statusVO, skuDetail, supplierMap);

            // 检查是否需要索引并获取原因
            String reason = checkIndexNeedWithReason(skuDetail, supplierMap);
            if (reason == null) {
                statusVO.setNeedIndex(true);
            } else {
                statusVO.setNeedIndex(false);
                statusVO.setReason(reason);
            }

            resultList.add(statusVO);
        }

        return resultList;
    }

    @Override
    public List<String> existJdSkuIds(List<String> jdSkuIds) {
        List<ProductSkuDO> productSkuDOS = list(new LambdaQueryWrapperX<ProductSkuDO>().in(ProductSkuDO::getSkuInnerId, jdSkuIds).select(ProductSkuDO::getSkuInnerId));
        return productSkuDOS.stream().map(ProductSkuDO::getSkuInnerId).collect(Collectors.toList());
    }

    /**
     * 填充SKU基本信息
     */
    private void fillSkuBasicInfo(ProductSkuIndexStatusRespVO statusVO, AppProductSkuDetailRespVO skuDetail, Map<Long, SupplierDO> supplierMap) {
        statusVO.setSkuInnerId(skuDetail.getSkuInnerId());
        statusVO.setSkuName(skuDetail.getSkuName());
        statusVO.setStatus(skuDetail.getStatus());
        statusVO.setShowStatus(skuDetail.getShowStatus());
        statusVO.setPlatformStatus(skuDetail.getPlatformStatus());
        statusVO.setSupplierId(skuDetail.getSupplierId());
        statusVO.setSupplierName(skuDetail.getSupplierName());
        statusVO.setFullCategoryId(skuDetail.getFullCategoryId());
        statusVO.setFullCategoryName(skuDetail.getFullCategoryName());

        // 设置供应商状态
        SupplierDO supplier = supplierMap.get(skuDetail.getSupplierId());
        if (supplier != null) {
            statusVO.setSupplierStatus(supplier.getStatus());
        }
    }

    /**
     * 检查SKU是否需要索引，返回不需要索引的原因，如果返回null表示需要索引
     * 参考filterIndexInNeed方法的逻辑
     */
    private String checkIndexNeedWithReason(AppProductSkuDetailRespVO sku, Map<Long, SupplierDO> supplierMap) {
        // 检查商品状态
        if (!ProductSpuStatusEnum.ENABLE.getStatus().equals(sku.getStatus())) {
            return "商品已下架";
        }

        // 检查商品显示状态
        if (!ProductSkuShowStatusEnum.SHOW.getStatus().equals(sku.getShowStatus())) {
            return "商品没有列出";
        }

        // 检查商品平台状态
        if (!ProductSpuStatusEnum.ENABLE.getStatus().equals(sku.getPlatformStatus())) {
            return "商品平台状态为下架";
        }

        // 检查供应商是否存在
        if (!supplierMap.containsKey(sku.getSupplierId())) {
            return "供应商ID不存在: " + sku.getSupplierId();
        }

        // 检查供应商状态
        if (!SupplierStatusEnum.ENABLE.getStatus().equals(supplierMap.get(sku.getSupplierId()).getStatus())) {
            return "供应商未启用: " + sku.getSupplierId();
        }

        // 检查分类状态
        String fullCategoryIds = sku.getFullCategoryId();
        if (StringUtils.isNotBlank(fullCategoryIds)) {
            String[] ids = StringUtils.split(fullCategoryIds, "-");
            Set<Long> cateIds = Arrays.stream(ids).mapToLong(Long::parseLong).boxed().collect(Collectors.toSet());

            boolean allCateEnable = productCategoryService.isAllEnable(cateIds);
            if (!allCateEnable) {
                return "商品分类存在未启用: " + cateIds;
            }
        }

        // 所有检查都通过，需要索引
        return null;
    }

    private void deleteSkuCache(List<ProductSkuDO> skuList) {
        if(CollUtil.isEmpty(skuList)) {
            return;
        }
        skuList.forEach(sku -> {
            deleteSkuCache(sku.getSupplierId(), sku.getId());
        });
    }

    private void deleteSkuCache(Long supplierId, Long skuId) {
        String key1 = String.format("%s:%d:%s", MALL_PRODUCT_DETAIL, TenantContextHolder.getTenantId(), VopGoodsGetSkuDetailReq.generateCacheKey2(skuId, "1"));
        String key2 = String.format("%s:%d:%s", MALL_PRODUCT_DETAIL, TenantContextHolder.getTenantId(), VopGoodsGetSkuDetailReq.generateCacheKey2(skuId, "2"));

        String key3 = String.format("%s:%d:%s", MALL_PRODUCT_BROTHER_SKUS, TenantContextHolder.getTenantId(), VopGoodsBrotherSkuReq.generateCacheKey2(skuId));
        redisUtils.deleteKeys(key1, key2, key3);
    }
}
