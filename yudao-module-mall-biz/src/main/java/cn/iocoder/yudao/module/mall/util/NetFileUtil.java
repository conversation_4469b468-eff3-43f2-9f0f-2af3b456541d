package cn.iocoder.yudao.module.mall.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/31 17:18
 */
@Slf4j
public class NetFileUtil {

    /**
     * 通过文件URL取消文件扩展名，无法提取时只返回默认扩展名
     * @param fileUrl
     * @param defaultExtension
     */
    public static String extractExtension(String fileUrl, String defaultExtension) {
        if (StrUtil.isBlank(fileUrl)) {
            return defaultExtension;
        }

        try {
            // 创建 URL 对象并获取路径部分
            URL url = new URL(fileUrl);
            String path = url.getPath();

            // 获取路径中的最后一部分（通常是文件名）
            String fileName = path.substring(path.lastIndexOf('/') + 1);

            // 查找文件名中最后一个点的位置
            int lastDotIndex = fileName.lastIndexOf('.');
            if (lastDotIndex != -1 && lastDotIndex < fileName.length() - 1) {
                // 提取扩展名
                return fileName.substring(lastDotIndex + 1);
            }
        } catch (Exception e) {
            log.info("从通过文件URL{}提取扩展名失败:", fileUrl, e);
        }

        return defaultExtension;
    }

    /**
     * 保存文件URL成本地文件
     * @param resUrl
     * @param path
     * @throws IOException
     */
    public static void saveFile(String resUrl, String path) throws IOException {
        URL url = new URL(resUrl);
        HttpURLConnection httpURLConnection = null;
        InputStream inputStream = null;
        try {
            httpURLConnection = (HttpURLConnection) url.openConnection();
            httpURLConnection.setDoInput(true);
            httpURLConnection.setRequestMethod("GET");
            httpURLConnection.connect();
            inputStream = httpURLConnection.getInputStream();
            FileUtil.writeFromStream(inputStream, path);
        } finally {

            if (inputStream != null) {
                inputStream.close();
            }

            if (httpURLConnection != null) {
                httpURLConnection.disconnect();
            }
        }
    }

    public static void main(String[] args) {
        // 测试用例
        String[] testUrls = {
                "https://example.com/files/document.pdf",
                "http://example.com/images/photo.jpg?token=abc123",
                "https://example.com/download/file.zip",
                "https://example.com/no-extension",
                "https://example.com/path/to/file.tar.gz"
        };

        for (String url : testUrls) {
            String extension = extractExtension(url, "jpg");
            System.out.println("URL: " + url);
            System.out.println("Extracted Extension: " + extension);
            System.out.println();
        }
    }

}
