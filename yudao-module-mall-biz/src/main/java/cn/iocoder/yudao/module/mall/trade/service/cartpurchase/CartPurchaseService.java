package cn.iocoder.yudao.module.mall.trade.service.cartpurchase;

import java.util.*;
import javax.validation.*;
import  cn.iocoder.yudao.module.mall.trade.controller.admin.cartpurchase.vo.*;
import  cn.iocoder.yudao.module.mall.trade.dal.dataobject.cartpurchase.CartPurchaseDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 采购订单购物车 Service 接口
 *
 * <AUTHOR>
 */
public interface CartPurchaseService {

    /**
     * 创建采购订单购物车
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCartPurchase(@Valid CartPurchaseCreateReqVO createReqVO);

    /**
     * 更新采购订单购物车
     *
     * @param updateReqVO 更新信息
     */
    void updateCartPurchase(@Valid CartPurchaseUpdateReqVO updateReqVO);

    /**
     * 删除采购订单购物车
     *
     * @param id 编号
     */
    void deleteCartPurchase(Long id);

    /**
     * 获得采购订单购物车
     *
     * @param id 编号
     * @return 采购订单购物车
     */
    CartPurchaseDO getCartPurchase(Long id);

    /**
     * 获得采购订单购物车列表
     *
     * @param ids 编号
     * @return 采购订单购物车列表
     */
    List<CartPurchaseDO> getCartPurchaseList(Collection<Long> ids);

    /**
     * 获得采购订单购物车分页
     *
     * @param pageReqVO 分页查询
     * @return 采购订单购物车分页
     */
    PageResult<CartPurchaseDO> getCartPurchasePage(CartPurchasePageReqVO pageReqVO);

    /**
     * 获得采购订单购物车列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 采购订单购物车列表
     */
    List<CartPurchaseDO> getCartPurchaseList(CartPurchaseExportReqVO exportReqVO);


    /**
     * 获得采购订单购物车
     *
     * @param userId 用户id
     * @return 采购订单购物车
     */
    List<CartPurchaseDO> getCartPurchaseByUserId(Long userId);

    /**
     * 根据员工id和订单id查询采购购物车
     * @param userId
     * @param orderIds
     * @return
     */
    List<CartPurchaseDO> selectListByUserIdAndOrderIds(Long userId, List<Long> orderIds);

    void updateByIds(List<Long> ids,CartPurchaseDO updateCartPurchases);

    /**
     * 删除采购购物车里的订单
     * @param userId
     * @param orderIds
     */
    void deleteCartOrders(Long userId, List<Long> orderIds);

    void saveBatch(List<CartPurchaseDO> cartPurchases);

}
