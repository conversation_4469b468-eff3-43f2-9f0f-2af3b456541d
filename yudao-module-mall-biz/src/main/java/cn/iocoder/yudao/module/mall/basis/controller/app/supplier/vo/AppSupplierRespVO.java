package cn.iocoder.yudao.module.mall.basis.controller.app.supplier.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 供应商
 */
@Data
public class AppSupplierRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "全称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String fullName;

    @Schema(description = "编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String code;

    @Schema(description = "简称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @Schema(description = "Logo地址")
    private String logoUrl;

    @Schema(description = "客服电话")
    private String servicePhone;

    @Schema(description = "状态：0-未配置，1-启用，2-停用", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer status;

    @Schema(description = "类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer type;

    @Schema(description = "包邮金额")
    private Integer freightThreshold;

    @Schema(description = "邮费")
    private Integer freight;

    @Schema(description = "起售金额")
    private Integer saleAmountMin;

    @Schema(description = "客服名称")
    private String serviceAgent;

    @Schema(description = "客服微信号")
    private String serviceWechatId;

    @Schema(description = "客服QQ号")
    private String serviceQqId;

}
