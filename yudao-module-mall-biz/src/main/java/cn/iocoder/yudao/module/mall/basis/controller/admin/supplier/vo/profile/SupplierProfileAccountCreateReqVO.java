package cn.iocoder.yudao.module.mall.basis.controller.admin.supplier.vo.profile;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 供应商收款账号创建 Request VO")
@Data
@ToString(callSuper = true)
public class SupplierProfileAccountCreateReqVO {

    private Long supplierId;

    @Schema(description = "单位名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "单位名称不能为空")
    private String orgName;

    @Schema(description = "所在省份", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "所在省份不能为空")
    private String province;

    @Schema(description = "所在城市", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "所在城市不能为空")
    private String city;

    @Schema(description = "开户行", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "开户行不能为空")
    private String bankName;

    @Schema(description = "开户行账号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "开户行账号不能为空")
    private String accountNum;

    @Schema(description = "开户行联行号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "开户行联行号不能为空")
    private String unifyBankNum;

}
