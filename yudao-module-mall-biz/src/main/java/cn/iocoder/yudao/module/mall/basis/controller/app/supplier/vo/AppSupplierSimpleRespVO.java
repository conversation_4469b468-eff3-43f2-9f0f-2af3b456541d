package cn.iocoder.yudao.module.mall.basis.controller.app.supplier.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 供应商
 */
@Data
public class AppSupplierSimpleRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "全称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String fullName;

    @Schema(description = "供应商类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer type;

    @Schema(description = "编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String code;

    @Schema(description = "简称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @Schema(description = "Logo地址")
    private String logoUrl;

    @Schema(description = "包邮金额")
    private Integer freightThreshold;

    @Schema(description = "邮费")
    private Integer freight;

    @Schema(description = "起售金额")
    private Integer saleAmountMin;

}
