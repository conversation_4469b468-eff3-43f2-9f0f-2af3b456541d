package cn.iocoder.yudao.module.mall.trade.dal.mysql.order;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.mall.trade.controller.app.teamorder.vo.AppTradeTeamOrderPageReqVO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeTeamOrderDO;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface TradeTeamOrderMapper extends BaseMapperX<TradeTeamOrderDO> {


    default TradeTeamOrderDO selectByUser(Long userId, Long id) {
        return selectOne(TradeTeamOrderDO::getUserId, userId, TradeTeamOrderDO::getId, id);
    }

    default TradeTeamOrderDO selectByTeamUser(Long teamUserId, Long id) {
        return selectOne(TradeTeamOrderDO::getTeamUserId, teamUserId, TradeTeamOrderDO::getId, id);
    }

    default TradeTeamOrderDO selectByUser(Long userId, String no) {
        return selectOne(TradeTeamOrderDO::getUserId, userId, TradeTeamOrderDO::getNo, no);
    }

    default TradeTeamOrderDO selectByUserOrTeamUser(Long userId, String no) {
        return selectOne(Wrappers.lambdaQuery(TradeTeamOrderDO.class)
                .eq(TradeTeamOrderDO::getNo, no)
                .and(wq -> wq
                        .eq(TradeTeamOrderDO::getUserId, userId)
                        .or()
                        .eq(TradeTeamOrderDO::getTeamUserId, userId)
                )
        );
    }

    default TradeTeamOrderDO selectByTeamUser(Long teamUserId, String no) {
        return selectOne(TradeTeamOrderDO::getTeamUserId, teamUserId, TradeTeamOrderDO::getNo, no);
    }

    default PageResult<TradeTeamOrderDO> selectPage(AppTradeTeamOrderPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TradeTeamOrderDO>()
                .eqIfPresent(TradeTeamOrderDO::getUserId, reqVO.getUserId())
                .eqIfPresent(TradeTeamOrderDO::getTeamUserId, reqVO.getTeamUserId())
                .eqIfPresent(TradeTeamOrderDO::getStatus, reqVO.getStatus())
                .eqIfPresent(TradeTeamOrderDO::getUserDeleted, reqVO.getUserDeleted())
                .like(StrUtil.isNotBlank(reqVO.getNo()), TradeTeamOrderDO::getNo, reqVO.getNo())
                .orderByDesc(TradeTeamOrderDO::getCreateTime));
    }

    Page<TradeTeamOrderDO> selectPage2(Page pageInfo, @Param("params") AppTradeTeamOrderPageReqVO reqVO);

    @MapKey("status")
    List<Map<String, Object>> selectStatsList(@Param("params") AppTradeTeamOrderPageReqVO reqVO);

}
