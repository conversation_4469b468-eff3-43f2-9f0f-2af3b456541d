package cn.iocoder.yudao.module.mall.basis.dal.mysql.stats;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.stats.StatsOrderCancelDO;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 订单取消统计 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface StatsOrderCancelMapper extends BaseMapperX<StatsOrderCancelDO> {

    default boolean exitsByOrderId(Long orderId) {
        return selectCount(StatsOrderCancelDO::getOrderId, orderId) > 0;
    }

    default StatsOrderCancelDO getByOrderId(Long orderId) {
        return selectOne(StatsOrderCancelDO::getOrderId, orderId);
    }

    default List<StatsOrderCancelDO> getByOrderId(List<Long> orderIds) {
        return selectList(Wrappers.lambdaQuery(StatsOrderCancelDO.class).in(StatsOrderCancelDO::getOrderId, orderIds));
    }

}
