package cn.iocoder.yudao.module.mall.basis.controller.admin.project.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 直采项目经费卡 Excel 导出 Request VO，参数和 ZcProjectPageReqVO 是一致的")
@Data
public class ZcProjectExportReqVO {

    @Schema(description = "项目名称")
    private String name;

    @Schema(description = "项目编号")
    private String no;

    @Schema(description = "项目类型编号")
    private String typeCode;

    @Schema(description = "项目类型名称")
    private String typeName;

    @Schema(description = "负责人工号")
    private String chargeNo;

    @Schema(description = "负责人姓名")
    private String chargeName;

    @Schema(description = "部门名称")
    private String departmentName;

    @Schema(description = "部门编号")
    private String departmentNo;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
