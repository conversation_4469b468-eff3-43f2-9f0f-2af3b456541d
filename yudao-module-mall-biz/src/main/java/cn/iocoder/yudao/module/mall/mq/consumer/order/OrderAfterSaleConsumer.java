package cn.iocoder.yudao.module.mall.mq.consumer.order;

import cn.iocoder.yudao.module.mall.basis.service.stats.StatsAfterSaleService;
import cn.iocoder.yudao.module.mall.mq.message.order.OrderAfterSaleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.function.Consumer;

/**
 * 针对 订单售后 的消费者
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class OrderAfterSaleConsumer implements Consumer<OrderAfterSaleMessage> {

    @Resource
    private StatsAfterSaleService statsAfterSaleService;

    @Override
    public void accept(OrderAfterSaleMessage message) {
        log.info("[accept][消息内容-订单售后处理:{}]", message);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            // 实时统计订单售后
            statsAfterSaleService.collectByData(message.getAfterSaleId());
        } catch(Exception e) {
            log.error("订单售后处理失败:", e);
        }
        stopWatch.stop();
        log.info("[accept][消息内容-订单售后处理完成，耗时：{}秒", stopWatch.getTotalTimeSeconds());
    }
}
