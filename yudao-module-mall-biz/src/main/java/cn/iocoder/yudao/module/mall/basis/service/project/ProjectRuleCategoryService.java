package cn.iocoder.yudao.module.mall.basis.service.project;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.basis.controller.admin.project.vo.ProjectRuleCategoryCreateReqVO;
import cn.iocoder.yudao.module.mall.basis.controller.admin.project.vo.ProjectRuleCategoryPageReqVO;
import cn.iocoder.yudao.module.mall.basis.controller.admin.project.vo.ProjectRuleCategoryStatusUpdateReqVO;
import cn.iocoder.yudao.module.mall.basis.controller.admin.project.vo.ProjectRuleCategoryUpdateReqVO;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.project.ProjectRuleCategoryDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 规则关联分类 Service 接口
 *
 * <AUTHOR>
 */
public interface ProjectRuleCategoryService {

    /**
     * 创建规则关联分类
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProjectRuleCategory(@Valid ProjectRuleCategoryCreateReqVO createReqVO);

    /**
     * 批量创建规则关联分类
     *
     * @param createReqVOs
     * @return
     */
    void createProjectRuleCategoryBatch(@Valid List<ProjectRuleCategoryCreateReqVO> createReqVOs);

    /**
     * 更新规则关联分类
     *
     * @param updateReqVO 更新信息
     */
    void updateProjectRuleCategory(@Valid ProjectRuleCategoryUpdateReqVO updateReqVO);

    /**
     * 更新规则关联分类状态
     *
     * @param updateReqVO 更新信息
     */
    void updateProjectRuleCategoryStatus(@Valid ProjectRuleCategoryStatusUpdateReqVO updateReqVO);

    /**
     * 删除规则关联分类
     *
     * @param id 编号
     */
    void deleteProjectRuleCategory(Long id);

    /**
     * 批量删除规则关联分类
     *
     * @param ids 编号
     */
    void deleteProjectRuleCategoryBatch(List<Long> ids);

    /**
     * 删除规则下所有商品分类
     * @param ruleId
     */
    void deleteProjectRuleCategoryByRule(Long ruleId);

    /**
     * 获得规则关联分类
     *
     * @param id 编号
     * @return 规则关联分类
     */
    ProjectRuleCategoryDO getProjectRuleCategory(Long id);

    /**
     * 获得规则关联分类列表
     *
     * @param ids 编号
     * @return 规则关联分类列表
     */
    List<ProjectRuleCategoryDO> getProjectRuleCategoryList(Collection<Long> ids);

    /**
     * 获得规则关联分类列表
     *
     * @param ruleId 规则编号
     * @return 规则关联分类列表
     */
    List<ProjectRuleCategoryDO> getProjectRuleCategoryListByRule(Long ruleId);

    List<ProjectRuleCategoryDO> getProjectRuleCategoryListByRule(Long ruleId, List<Long> categoryIds);

    /**
     * 获得规则关联分类分页
     *
     * @param pageReqVO 分页查询
     * @return 规则关联分类分页
     */
    PageResult<ProjectRuleCategoryDO> getProjectRuleCategoryPage(ProjectRuleCategoryPageReqVO pageReqVO);

}
