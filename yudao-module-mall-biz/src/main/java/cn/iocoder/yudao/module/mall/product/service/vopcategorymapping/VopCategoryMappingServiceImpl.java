package cn.iocoder.yudao.module.mall.product.service.vopcategorymapping;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import cn.iocoder.yudao.module.mall.product.controller.admin.vopcategorymapping.vo.*;
import cn.iocoder.yudao.module.mall.product.dal.dataobject.vopcategorymapping.VopCategoryMappingDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import cn.iocoder.yudao.module.mall.product.convert.vopcategorymapping.VopCategoryMappingConvert;
import cn.iocoder.yudao.module.mall.product.dal.mysql.vopcategorymapping.VopCategoryMappingMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.mall.product.enums.ErrorCodeConstants.VOP_CATEGORY_MAPPING_NOT_EXISTS;

/**
 * 京东分类映射 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class VopCategoryMappingServiceImpl extends ServiceImpl<VopCategoryMappingMapper, VopCategoryMappingDO> implements VopCategoryMappingService {

    @Resource
    private VopCategoryMappingMapper vopCategoryMappingMapper;

    @Override
    public Long createVopCategoryMapping(VopCategoryMappingCreateReqVO createReqVO) {
        // 插入
        VopCategoryMappingDO vopCategoryMapping = VopCategoryMappingConvert.INSTANCE.convert(createReqVO);
        vopCategoryMappingMapper.insert(vopCategoryMapping);
        // 返回
        return vopCategoryMapping.getId();
    }

    @Override
    public void updateVopCategoryMapping(VopCategoryMappingUpdateReqVO updateReqVO) {
        // 校验存在
        validateVopCategoryMappingExists(updateReqVO.getId());
        // 更新
        VopCategoryMappingDO updateObj = VopCategoryMappingConvert.INSTANCE.convert(updateReqVO);
        vopCategoryMappingMapper.updateById(updateObj);
    }

    @Override
    public void deleteVopCategoryMapping(Long id) {
        // 校验存在
        validateVopCategoryMappingExists(id);
        // 删除
        vopCategoryMappingMapper.deleteById(id);
    }

    private void validateVopCategoryMappingExists(Long id) {
        if (vopCategoryMappingMapper.selectById(id) == null) {
            throw exception(VOP_CATEGORY_MAPPING_NOT_EXISTS);
        }
    }

    @Override
    public VopCategoryMappingDO getVopCategoryMapping(Long id) {
        return vopCategoryMappingMapper.selectById(id);
    }

    @Override
    public List<VopCategoryMappingDO> getVopCategoryMappingList(Collection<Long> ids) {
        return vopCategoryMappingMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<VopCategoryMappingDO> getVopCategoryMappingPage(VopCategoryMappingPageReqVO pageReqVO) {
        return vopCategoryMappingMapper.selectPage(pageReqVO);
    }

    @Override
    public List<VopCategoryMappingDO> getVopCategoryMappingList(VopCategoryMappingExportReqVO exportReqVO) {
        return vopCategoryMappingMapper.selectList(exportReqVO);
    }

}
