package cn.iocoder.yudao.module.mall.trade.dal.dataobject.order;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.mall.external.ycrh.enums.YcrhFileTypeEnum;
import cn.iocoder.yudao.module.mall.trade.enums.order.InvoiceStatusEnum;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/27 11:16
 */
@TableName("trade_order_invoice")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TradeOrderInvoiceDO extends BaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;
    private Long orderId;
    private Long supplierId;
    private String invoiceDate;
    private String markId;
    private Integer invoiceOrg;

    private BigDecimal totalBatchInvoiceAmount;
    private BigDecimal invoicePrice;

    private String billToContact;
    private String title;
    private Integer currentBatch;
    private String enterpriseTaxpayer;

    private Integer invoiceType;
    private Integer bizInvoiceContent;
    private String supplierOrder;
    private Integer totalBatch;

    private String settlementId;
    private Integer invoiceNum;

    /**
     * 开票返回结果, 主要使用invoiceId，为发票号码；
     * invoiceCode一般用不到
     */
    private String invoiceId;
    private String fileUrl;
    private String invoiceCode;

    /**
     * 发票状态： 0 未开票 1 已申请开票 2 开票完成 3 开票失败
     * 枚举 {@link cn.iocoder.yudao.module.mall.trade.enums.order.InvoiceStatusEnum}
     */
    private Integer invoiceStatus;

    /**
     * 凭证文件名称
     */
    private String voucherName;
    /**
     * 凭证上传状态
     */
    private Boolean voucherUploadStatus;
    /**
     * 发票是否验真
     */
    private Boolean invoiceCheckStatus;
    /**
     * 发票验真次数
     */
    private Integer invoiceCheckCount;
    /**
     * 不能开票的原因
     */
    private String failReason;

    /**
     * 是否开票完成
     * @return
     */
    @JsonIgnore
    public boolean isInvoiceSuccess() {
        return ObjectUtil.equal(InvoiceStatusEnum.APPLY_INVOICE_SUCCESS.getStatus(), invoiceStatus);
    }

    /**
     * 是否上传到业财
     * @return
     */
    @JsonIgnore
    public boolean isUploadYcrhSuccess() {
        return StrUtil.isNotBlank(voucherName);
    }

}
