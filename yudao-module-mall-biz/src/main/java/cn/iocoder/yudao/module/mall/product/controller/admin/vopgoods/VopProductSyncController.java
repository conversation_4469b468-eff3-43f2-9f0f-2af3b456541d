package cn.iocoder.yudao.module.mall.product.controller.admin.vopgoods;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.mall.product.controller.admin.vopgoods.vo.VopProductSyncProgressVO;
import cn.iocoder.yudao.module.mall.product.service.vopgoods.VopGoodsBridgeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * VOP商品同步控制器
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - VOP商品同步")
@RestController
@RequestMapping("/product/vopSync")
@Slf4j
public class VopProductSyncController {

    @Resource
    private VopGoodsBridgeService vopGoodsBridgeService;

    @PostMapping("/start")
    @Operation(summary = "开始VOP商品同步")
    @PreAuthorize("@ss.hasPermission('vop:sync')")
    public CommonResult<Boolean> startSync(
            @Parameter(description = "分类ID，为空则同步所有分类") @RequestParam(required = false) Long categoryId,
            @Parameter(description = "最大页数") @RequestParam(required = false) Long maxPageSize) {

        try {
            // 异步执行同步任务
            new Thread(() -> {
                try {
                    vopGoodsBridgeService.syncVopProductByCategory(categoryId, maxPageSize);
                } catch (Exception e) {
                    log.error("VOP商品同步异常", e);
                }
            }).start();

            return success(true);
        } catch (Exception e) {
            log.error("启动VOP商品同步失败", e);
            return success(false);
        }
    }

    @GetMapping("/progress")
    @Operation(summary = "查询VOP商品同步进度")
    @PreAuthorize("@ss.hasPermission('vop:sync')")
    public CommonResult<VopProductSyncProgressVO> getProgress(
            @Parameter(description = "分类ID") @RequestParam(required = false) Long categoryId,
            @Parameter(description = "最大页数") @RequestParam(required = false) Long maxPageSize) {

        try {
            VopProductSyncProgressVO progress = vopGoodsBridgeService.getSyncProgress(categoryId, maxPageSize);
            return success(progress);
        } catch (Exception e) {
            log.error("查询VOP商品同步进度失败", e);
            return CommonResult.error(0,"查询进度失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/progress")
    @Operation(summary = "清除VOP商品同步进度")
    @PreAuthorize("@ss.hasPermission('vop:sync')")
    public CommonResult<Boolean> clearProgress(
            @Parameter(description = "分类ID") @RequestParam(required = false) Long categoryId,
            @Parameter(description = "最大页数") @RequestParam(required = false) Long maxPageSize) {

        try {
            vopGoodsBridgeService.clearSyncProgress(categoryId, maxPageSize);
            return success(true);
        } catch (Exception e) {
            log.error("清除VOP商品同步进度失败", e);
            return CommonResult.error(0,"清除进度失败: " + e.getMessage());
        }
    }
}
