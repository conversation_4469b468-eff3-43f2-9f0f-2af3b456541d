package cn.iocoder.yudao.module.mall.product.controller.admin.vopgoods;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.product.controller.admin.vopgoods.vo.VopProductSyncProgressVO;
import cn.iocoder.yudao.module.mall.product.service.vopgoods.VopGoodsBridgeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * VOP商品同步控制器
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - VOP商品同步")
@RestController
@RequestMapping("/product/vopSync")
@Slf4j
public class VopProductSyncController {

    @Resource
    private VopGoodsBridgeService vopGoodsBridgeService;

    @Resource
    private RedissonClient redissonClient;

    @PostMapping("/start")
    @Operation(summary = "开始VOP商品同步")
    @PreAuthorize("@ss.hasPermission('vop:sync')")
    public CommonResult<String> startSync(
            @Parameter(description = "分类ID，为空则同步所有分类") @RequestParam(required = false) Long categoryId,
            @Parameter(description = "最大页数") @RequestParam(required = false) Long maxPageSize) {

        Long tenantId = TenantContextHolder.getTenantId();
        String lockKey = generateSyncLockKey(tenantId, categoryId, maxPageSize);
        RLock lock = redissonClient.getLock(lockKey);

        try {
            // 尝试获取锁，如果获取不到说明已有任务在运行
            if (!lock.tryLock(1, TimeUnit.SECONDS)) {
                log.info("VOP商品同步任务已在运行中，tenantId={}, categoryId={}, maxPageSize={}",
                    tenantId, categoryId, maxPageSize);
                return success("同步任务已在运行中，请稍后再试");
            }

            // 检查是否有未完成的进度
            VopProductSyncProgressVO existingProgress = vopGoodsBridgeService.getSyncProgress(categoryId, maxPageSize);
            if (existingProgress != null && existingProgress.getCompleted() != null && !existingProgress.getCompleted()) {
                log.info("检测到未完成的同步任务，将继续执行，tenantId={}, categoryId={}, maxPageSize={}",
                    tenantId, categoryId, maxPageSize);
            }

            // 异步执行同步任务
            new Thread(() -> {
                try {
                    log.info("开始执行VOP商品同步任务，tenantId={}, categoryId={}, maxPageSize={}",
                        tenantId, categoryId, maxPageSize);
                    vopGoodsBridgeService.syncVopProductByCategory(categoryId, maxPageSize);
                    log.info("VOP商品同步任务执行完成，tenantId={}, categoryId={}, maxPageSize={}",
                        tenantId, categoryId, maxPageSize);
                } catch (Exception e) {
                    log.error("VOP商品同步异常，tenantId={}, categoryId={}, maxPageSize={}",
                        tenantId, categoryId, maxPageSize, e);
                } finally {
                    // 释放锁
                    if (lock.isHeldByCurrentThread()) {
                        lock.unlock();
                        log.info("释放VOP商品同步锁，tenantId={}, categoryId={}, maxPageSize={}",
                            tenantId, categoryId, maxPageSize);
                    }
                }
            }).start();

            return success("同步任务已启动");

        } catch (Exception e) {
            log.error("启动VOP商品同步失败，tenantId={}, categoryId={}, maxPageSize={}",
                tenantId, categoryId, maxPageSize, e);
            // 确保释放锁
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
            return CommonResult.error(0,"启动同步失败: " + e.getMessage());
        }
    }

    @GetMapping("/progress")
    @Operation(summary = "查询VOP商品同步进度")
    @PreAuthorize("@ss.hasPermission('vop:sync')")
    public CommonResult<VopProductSyncProgressVO> getProgress(
            @Parameter(description = "分类ID") @RequestParam(required = false) Long categoryId,
            @Parameter(description = "最大页数") @RequestParam(required = false) Long maxPageSize) {

        try {
            VopProductSyncProgressVO progress = vopGoodsBridgeService.getSyncProgress(categoryId, maxPageSize);
            return success(progress);
        } catch (Exception e) {
            log.error("查询VOP商品同步进度失败", e);
            return CommonResult.error(0,"查询进度失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/progress")
    @Operation(summary = "清除VOP商品同步进度")
    @PreAuthorize("@ss.hasPermission('vop:sync')")
    public CommonResult<Boolean> clearProgress(
            @Parameter(description = "分类ID") @RequestParam(required = false) Long categoryId,
            @Parameter(description = "最大页数") @RequestParam(required = false) Long maxPageSize) {

        try {
            vopGoodsBridgeService.clearSyncProgress(categoryId, maxPageSize);
            return success(true);
        } catch (Exception e) {
            log.error("清除VOP商品同步进度失败", e);
            return CommonResult.error(0,"清除进度失败: " + e.getMessage());
        }
    }

    /**
     * 生成同步任务锁Key
     * @param tenantId 租户ID
     * @param categoryId 分类ID
     * @param maxPageSize 最大页数
     * @return 锁Key
     */
    private String generateSyncLockKey(Long tenantId, Long categoryId, Long maxPageSize) {
        return String.format("vop_sync_lock:%d:%s:%s",
            tenantId,
            categoryId != null ? categoryId.toString() : "all",
            maxPageSize != null ? maxPageSize.toString() : "default");
    }
}
