package cn.iocoder.yudao.module.mall.trade.controller.admin.bill.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 管理后台 - 账单创建 Request VO
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 账单创建 Request VO")
@Data
@ToString(callSuper = true)
public class BillCreateReqVO {
    /**
     * 账单名称
     */
    @Schema(description = "账单名称")
    @NotBlank(message = "账单名称不能为空")
    private String billName;

    /**
     * 供应商ID
     */
    @Schema(description = "供应商ID")
    @NotNull(message = "供应商不能为空")
    private Long supplierId;

    /**
     * 结算方式
     */
    @Schema(description = "结算方式")
    @NotNull(message = "结算方式不能为空")
    private Integer settlementWay;
}
