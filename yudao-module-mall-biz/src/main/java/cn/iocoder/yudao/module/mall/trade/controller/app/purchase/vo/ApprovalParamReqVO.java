package cn.iocoder.yudao.module.mall.trade.controller.app.purchase.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/7/24
 */
@Data
public class ApprovalParamReqVO {

    /**
     * 项目的部门编号
     */
    @NotBlank(message = "部门编号不能为空")
    private String departmentNo;

    /**
     * 项目编号
     */
    @NotBlank(message = "项目编号不能为空")
    private String projectNo;

    /**
     * 经济分类
     */
    private String economyClass;

    /**
     * 订单总金额
     */
    private BigDecimal orderAmount;


}
