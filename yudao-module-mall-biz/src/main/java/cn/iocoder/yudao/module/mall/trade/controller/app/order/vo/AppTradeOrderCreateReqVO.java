package cn.iocoder.yudao.module.mall.trade.controller.app.order.vo;

import cn.iocoder.yudao.module.mall.basis.dal.dataobject.supplier.SupplierDO;
import cn.iocoder.yudao.module.mall.member.api.address.dto.AddressRespDTO;
import cn.iocoder.yudao.module.mall.trade.controller.app.purchase.vo.ProjectInfoDTO;
import cn.iocoder.yudao.module.mall.trade.enums.order.PaymentMethodEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 用户 App - 交易订单创建 Request VO
 */
@Schema(description = "用户 App - 交易订单创建 Request VO")
@Data
public class AppTradeOrderCreateReqVO {

    private Long userId;

    @Schema(description = "协同订单号", example = "1024")
    private String teamOrderNo;

    /**
     * 收件地址编号
     */
    @Schema(description = "收件地址编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "收件地址不能为空")
    private Long addressId;

    private AddressRespDTO addressDTO;

    /**
     * 优惠劵编号
     */
    @Schema(description = "优惠劵编号", example = "1024")
    private Long couponId;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "这个是我的订单哟")
    private String remark;

    /**
     * 支付方式
     * {@link PaymentMethodEnum}
     */
    @Schema(description = "支付方式", example = "1")
    private Integer paymentMethod;

    /**
     * 订单商品项列表
     */
    @Valid
    @Schema(description = "店铺商品信息")
    @NotEmpty(message = "商品信息不能为空")
    private List<ShopItem> shopItems;

    /**
     * 时间戳
     */
    @Schema(description = "时间戳")
    private Long timestamp;

    @Schema(description = "经济分类")
    private String economyClass;

    @Schema(description = "项目经费卡信息")
    private ProjectInfoDTO projectInfo;

    private Boolean needPurchase;

    private Boolean needYcrh;

    /**
     * 店铺商品信息
     */
    @Schema(description = "店铺商品信息")
    @Data
    public static class ShopItem {

        /**
         * 供应商id
         */
        @Schema(description = "供应商id")
        @NotNull(message = "供应商id不能为空")
        private Long supplierId;

        private String supplierName;
        private SupplierDO supplierDO;

        @Schema(description = "订单商品项列表")
        @NotEmpty(message = "商品项列表不能为空")
        private List<Item> items;

        /**
         * 运费
         */
        private BigDecimal deliveryPrice;
        /**
         * 总价
         */
        private BigDecimal totalPrice;
    }
    /**
     * 订单商品项
     */
    @Schema(description = "订单商品项")
    @Data
    public static class Item {

        /**
         * 商品 SKU 编号
         */
        @Schema(description = "商品 SKU 编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "111")
        @NotNull(message = "商品 SKU 编号不能为空")
        private Long skuId;

        private String skuName;
        private String picUrl;
        private String skuInnerId;
        private String categoryCode;
        private String fullCategoryCode;
        private String economyClass;
        private BigDecimal price;
        private BigDecimal skuTotalPrice;

        /**
         * 商品 SKU 购买数量
         */
        @Schema(description = "商品 SKU 购买数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
        @NotNull(message = "商品 SKU 购买数量不能为空")
        @Min(value = 1, message = "商品 SKU 购买数量必须大于 0")
        private Integer count;

    }

}
