package cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 交易统计明细的分页项 Response VO")
@Data
public class StatsOrderItemRespVO {

    /**
     * 订单状态
     */
    private Integer orderStatus;
    /**
     * 订单ID
     */
    private Long orderId;
    /**
     * 订单明细ID
     */
    private Long orderItemId;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 部门编码
     */
    private String deptCode;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 供应商ID
     */
    private Long supplierId;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 商品ID
     */
    private Long skuId;
    /**
     * 三方商品ID
     */
    private String thirdSkuId;
    /**
     * SKU名称
     */
    private String skuName;
    /**
     * 商品分类ID
     */
    private Long categoryId;
    /**
     * 商品分类名称
     */
    private String categoryName;
    /**
     * 一级商品分类ID
     */
    private Long categoryId1;
    /**
     * 二级商品分类ID
     */
    private Long categoryId2;
    /**
     * 三级商品分类ID
     */
    private Long categoryId3;
    /**
     * 价格
     */
    private BigDecimal price;
    /**
     * 购买数量
     */
    private Integer quantity;
    /**
     * 总金额
     */
    private BigDecimal totalPrice;
    /**
     * 下单时间
     */
    private LocalDateTime submitTime;
    /**
     * 统计日期
     */
    private LocalDateTime statsDate;
    /**
     * 运营标签
     */
    private List<String> skuTags;


}
