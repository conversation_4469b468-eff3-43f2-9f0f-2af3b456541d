package cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 交易统计明细的分页项 Response VO")
@Data
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class StatsSaleDateRespVO {

    /**
     * 订单数量
     */
    private Long orderCount;
    /**
     * 总金额
     */
    private BigDecimal totalPrice;
    /**
     * 取消订单总金额
     */
    private BigDecimal cancelTotalPrice;
    /**
     * 收入总金额
     */
    private BigDecimal netTotalPrice;
    /**
     * 售后总金额
     */
    private BigDecimal afterSaleTotalPrice;
    /**
     * 售后总订单数
     */
    private Long afterSaleTotalCount;
    /**
     * 统计日期
     */
    private LocalDateTime statsDate;
    /**
     * 年月
     */
    private String yearMonth;


}
