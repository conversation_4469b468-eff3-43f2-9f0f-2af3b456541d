package cn.iocoder.yudao.module.mall.trade.service.order.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.member.dal.dataobject.address.AddressDO;
import cn.iocoder.yudao.module.mall.member.service.address.AddressService;
import cn.iocoder.yudao.module.mall.trade.controller.app.cart.vo.CheckOrderReqVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.teamorder.vo.AppTradeTeamOrderPageReqVO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeTeamOrderDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeTeamOrderItemDO;
import cn.iocoder.yudao.module.mall.trade.dal.mysql.order.TradeOrderMapper;
import cn.iocoder.yudao.module.mall.trade.dal.mysql.order.TradeTeamOrderMapper;
import cn.iocoder.yudao.module.mall.trade.enums.order.TradeTeamOrderStatusEnum;
import cn.iocoder.yudao.module.mall.trade.service.order.TradeTeamOrderItemService;
import cn.iocoder.yudao.module.mall.trade.service.order.TradeTeamOrderService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.module.mall.trade.enums.ErrorCodeConstants.*;

/**
 * 协同订单 Service 接口
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Service
@Slf4j
public class TradeTeamOrderServiceImpl extends ServiceImpl<TradeTeamOrderMapper, TradeTeamOrderDO> implements TradeTeamOrderService {

    @Resource
    private TradeTeamOrderItemService teamOrderItemService;
    @Resource
    private TradeOrderMapper tradeOrderMapper;
    @Resource
    private AddressService addressService;

    @Override
    public TradeTeamOrderDO getByUser(Long userId, Long id) {
        TradeTeamOrderDO orderDO = baseMapper.selectByUser(userId, id);
        if(orderDO == null) {
            throw ServiceExceptionUtil.exception(TEAM_ORDER_NOT_FOUND);
        }
        return orderDO;
    }

    @Override
    public TradeTeamOrderDO getByTeamUser(Long teamUserId, Long id) {
        TradeTeamOrderDO orderDO =  baseMapper.selectByTeamUser(teamUserId, id);
        if(orderDO == null) {
            throw ServiceExceptionUtil.exception(TEAM_ORDER_NOT_FOUND);
        }
        return orderDO;
    }

    @Override
    public TradeTeamOrderDO getByUser(Long userId, String no) {
        TradeTeamOrderDO orderDO =  baseMapper.selectByUser(userId, no);
        if(orderDO == null) {
            throw ServiceExceptionUtil.exception(TEAM_ORDER_NOT_FOUND);
        }
        return orderDO;
    }

    @Override
    public TradeTeamOrderDO getByUserOrTeamUser(Long userId, String no) {
        TradeTeamOrderDO orderDO =  baseMapper.selectByUserOrTeamUser(userId, no);
        if(orderDO == null) {
            throw ServiceExceptionUtil.exception(TEAM_ORDER_NOT_FOUND);
        }
        return orderDO;
    }

    @Override
    public TradeTeamOrderDO getByTeamUser(Long teamUserId, String no) {
        TradeTeamOrderDO orderDO =  baseMapper.selectByTeamUser(teamUserId, no);
        if(orderDO == null) {
            throw ServiceExceptionUtil.exception(TEAM_ORDER_NOT_FOUND);
        }
        return orderDO;
    }

    @Override
    public AddressDO handleAddress(Long userId, String no) {
        TradeTeamOrderDO teamOrderDO = getByTeamUser(userId, no);
        if(ObjectUtil.notEqual(teamOrderDO.getStatus(), TradeTeamOrderStatusEnum.CONFIRMED.getStatus())) {
            return null;
        }

        AddressDO addressDO = new AddressDO();
        addressDO.setUserId(userId);
        addressDO.setName(teamOrderDO.getReceiverName());
        addressDO.setMobile(teamOrderDO.getReceiverMobile());
        addressDO.setProvinceId(teamOrderDO.getReceiverProvince());
        addressDO.setProvinceName(teamOrderDO.getReceiverProvinceName());
        addressDO.setCityId(teamOrderDO.getReceiverCity());
        addressDO.setCityName(teamOrderDO.getReceiverCityName());
        addressDO.setCountyId(teamOrderDO.getReceiverCounty());
        addressDO.setCountyName(teamOrderDO.getReceiverCountyName());
        addressDO.setTownId(teamOrderDO.getReceiverTown());
        addressDO.setTownName(teamOrderDO.getReceiverTownName());
        addressDO.setConsigneeAddress(teamOrderDO.getReceiverDetailAddress());

        return addressService.checkExists(addressDO);
    }

    @Override
    public void confirmTeamOrder(Long teamUserId, String no) {
        TradeTeamOrderDO teamOrderDO = getByTeamUser(teamUserId, no);
        if(ObjectUtil.notEqual(teamOrderDO.getStatus(), TradeTeamOrderStatusEnum.INIT.getStatus())) {
            throw ServiceExceptionUtil.exception(TEAM_ORDER_CONFIRM_FAIL);
        }

        teamOrderDO.setStatus(TradeTeamOrderStatusEnum.CONFIRMED.getStatus());
        teamOrderDO.setConfirmTime(LocalDateTime.now());
        baseMapper.updateById(new TradeTeamOrderDO().setId(teamOrderDO.getId())
                .setStatus(teamOrderDO.getStatus())
                .setConfirmTime(teamOrderDO.getConfirmTime()));
    }

    @Override
    public void rejectTeamOrder(Long teamUserId, String no) {
        TradeTeamOrderDO teamOrderDO = getByTeamUser(teamUserId, no);
        if(ObjectUtil.notEqual(teamOrderDO.getStatus(), TradeTeamOrderStatusEnum.INIT.getStatus())
            && ObjectUtil.notEqual(teamOrderDO.getStatus(), TradeTeamOrderStatusEnum.CONFIRMED.getStatus())) {
            throw ServiceExceptionUtil.exception(TEAM_ORDER_REJECT_FAIL);
        }

        teamOrderDO.setStatus(TradeTeamOrderStatusEnum.REJECT.getStatus());
        teamOrderDO.setRejectTime(LocalDateTime.now());
        baseMapper.updateById(new TradeTeamOrderDO().setId(teamOrderDO.getId())
                .setStatus(teamOrderDO.getStatus())
                .setRejectTime(teamOrderDO.getRejectTime()));
    }

    @Override
    public PageResult<TradeTeamOrderDO> getOrderPage(AppTradeTeamOrderPageReqVO reqVO) {
        Page<TradeOrderDO> pageInfo = new Page(reqVO.getPageNo(), reqVO.getPageSize());
        baseMapper.selectPage2(pageInfo, reqVO);

        PageResult<TradeTeamOrderDO> pageResult = new PageResult((int) pageInfo.getCurrent(), (int) pageInfo.getSize(), (int) pageInfo.getPages(), pageInfo.getRecords(), pageInfo.getTotal());
        return pageResult;
    }

    @Override
    public List<Map<String, Object>> getStatsList(AppTradeTeamOrderPageReqVO reqVO) {
        return baseMapper.selectStatsList(reqVO);
    }

    @Override
    public void deleteOrder(Long userId, String no) {
        TradeTeamOrderDO teamOrderDO = getByUser(userId, no);
        if(ObjectUtil.notEqual(teamOrderDO.getStatus(), TradeTeamOrderStatusEnum.CANCEL.getStatus())) {
            throw ServiceExceptionUtil.exception(TEAM_ORDER_DELETE_FAIL);
        }
        teamOrderDO.setUserDeleted(true);
        baseMapper.updateById(new TradeTeamOrderDO().setId(teamOrderDO.getId()).setUserDeleted(teamOrderDO.getUserDeleted()));
    }

    @Override
    public void cancelOrder(Long userId, String no) {
        TradeTeamOrderDO teamOrderDO = getByUser(userId, no);
        if(ObjectUtil.equal(teamOrderDO.getStatus(), TradeTeamOrderStatusEnum.CANCEL.getStatus())) {
            return;
        }
        teamOrderDO.setCancelTime(LocalDateTime.now());
        teamOrderDO.setStatus(TradeTeamOrderStatusEnum.CANCEL.getStatus());
        baseMapper.updateById(new TradeTeamOrderDO().setId(teamOrderDO.getId())
                .setStatus(teamOrderDO.getStatus())
                .setCancelTime(teamOrderDO.getCancelTime()));
    }

    @Override
    public List<CheckOrderReqVO.Item> buildCheckOrderItemList(Long teamUserId, String teamOrderNo) {
        List<CheckOrderReqVO.Item> checkOrderItemList = new ArrayList<>();
        TradeTeamOrderDO teamOrderDO = getByTeamUser(teamUserId, teamOrderNo);
        if(teamOrderDO == null) {
            return checkOrderItemList;
        }

        List<TradeTeamOrderItemDO> teamOrderItemList = teamOrderItemService.getItemListByTeamOrder(teamOrderDO.getId());
        if(CollUtil.isEmpty(teamOrderItemList)) {
            return checkOrderItemList;
        }

        return teamOrderItemList.stream().map(item -> {
            CheckOrderReqVO.Item checkItem = new CheckOrderReqVO.Item();
            checkItem.setSkuId(item.getSkuId());
            checkItem.setSupplierId(item.getSupplierId());
            checkItem.setCount(item.getCount());

            return checkItem;
        }).collect(Collectors.toList());
    }

    @Override
    public void updateOrderRelationAfterCreate(List<TradeOrderDO> newOrders, Long userId, String teamOrderNo) {
        if(StrUtil.isBlank(teamOrderNo)) {
            return;
        }
        if(CollUtil.isEmpty(newOrders)) {
            return;
        }
        TradeTeamOrderDO teamOrderDO = getByTeamUser(userId, teamOrderNo);
        Long parentOrderId = newOrders.get(0).getParentOrderId();
        List<Long> subOrderIds  = newOrders.stream().map(TradeOrderDO::getId).collect(Collectors.toList());
        if(parentOrderId != null) {
            subOrderIds.add(parentOrderId);
        }
        log.info("协同订单关系更新订单：{}", subOrderIds);
        tradeOrderMapper.update(null, Wrappers.lambdaUpdate(TradeOrderDO.class)
                .in(TradeOrderDO::getId, subOrderIds)
                .set(TradeOrderDO::getTeamOrderId, teamOrderDO.getId()));

        teamOrderDO.setCompleteTime(newOrders.get(0).getSubmitTime());
        baseMapper.updateById(new TradeTeamOrderDO().setId(teamOrderDO.getId())
                        .setStatus(TradeTeamOrderStatusEnum.COMPLETE.getStatus())
                .setCompleteTime(teamOrderDO.getCompleteTime()));
    }

}
