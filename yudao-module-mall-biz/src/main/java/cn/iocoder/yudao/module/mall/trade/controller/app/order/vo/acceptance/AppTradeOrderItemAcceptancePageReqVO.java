package cn.iocoder.yudao.module.mall.trade.controller.app.order.vo.acceptance;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 订单商品验收单分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppTradeOrderItemAcceptancePageReqVO extends PageParam {

    private Long userId;

    private Long orderId;

}
