package cn.iocoder.yudao.module.mall.trade.dal.mysql.message;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.mall.trade.controller.admin.message.vo.TradeMessagePageReqVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.message.vo.AppTradeMessageReqVO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.message.TradeMessageDO;
import cn.iocoder.yudao.module.mall.trade.enums.order.TradeMessageStatusEnum;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 订单消息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TradeMessageMapper extends BaseMapperX<TradeMessageDO> {

    /**
     * 分页查询
     * @param reqVO
     * @return
     */
    default PageResult<TradeMessageDO> selectPage(TradeMessagePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TradeMessageDO>()
                .eqIfPresent(TradeMessageDO::getId, reqVO.getId())
                .eqIfPresent(TradeMessageDO::getMessageType, reqVO.getMessageType())
                .eqIfPresent(TradeMessageDO::getMessageStatus, reqVO.getMessageStatus())
                .eqIfPresent(TradeMessageDO::getSupplierId, reqVO.getSupplierId())
                .betweenIfPresent(TradeMessageDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TradeMessageDO::getId));
    }

    /**
     * 获取未读消息列表
     * @param supplierId
     * @param reqVO
     * @return
     */
    default List<TradeMessageDO> getUnReadMessageList(Long supplierId, AppTradeMessageReqVO reqVO, Integer fetchSize) {
        return selectList(new LambdaQueryWrapperX<TradeMessageDO>()
                .eqIfPresent(TradeMessageDO::getMessageType, reqVO.getMessageType())
                .eqIfPresent(TradeMessageDO::getSupplierId, supplierId)
                .eq(TradeMessageDO::getMessageStatus, TradeMessageStatusEnum.UN_READ.getStatus())
                .betweenIfPresent(TradeMessageDO::getCreateTime, reqVO.getCreateTime())
                .in(CollUtil.isNotEmpty(reqVO.getMessageTypes()), TradeMessageDO::getMessageType, reqVO.getMessageTypes())
                .orderByAsc(TradeMessageDO::getId)
                .last("limit "+fetchSize));
    }


}
