package cn.iocoder.yudao.module.mall.basis.dal.mysql.supplier;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.mall.basis.controller.admin.supplier.vo.form.SupplierFormPageReqVO;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.supplier.SupplierFormDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 供应商入驻申请 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SupplierFormMapper extends BaseMapperX<SupplierFormDO> {

    default PageResult<SupplierFormDO> selectPage(SupplierFormPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SupplierFormDO>()
                .likeIfPresent(SupplierFormDO::getEnterpriseFullName, reqVO.getFullName())
                .likeIfPresent(SupplierFormDO::getContact, reqVO.getContact())
                .eqIfPresent(SupplierFormDO::getUserId, reqVO.getUserId())
                .betweenIfPresent(SupplierFormDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(SupplierFormDO::getApproveStatus, reqVO.getApproveStatus())
                .in(CollUtil.isNotEmpty(reqVO.getApproveStatusList()), SupplierFormDO::getApproveStatus, reqVO.getApproveStatusList())
                .orderBy(reqVO.getSortType() == null, false, SupplierFormDO::getId)
                .orderBy(ObjectUtil.equal(reqVO.getSortType(), 10), false, SupplierFormDO::getUpdateTime)
                .orderBy(ObjectUtil.equal(reqVO.getSortType(), 11), true, SupplierFormDO::getUpdateTime)
                .orderBy(ObjectUtil.equal(reqVO.getSortType(), 20), false, SupplierFormDO::getCreateTime)
                .orderBy(ObjectUtil.equal(reqVO.getSortType(), 21), true, SupplierFormDO::getCreateTime)
                .select(SupplierFormDO::getId,
                        SupplierFormDO::getUserId,
                        SupplierFormDO::getEnterpriseUnifiedId,
                        SupplierFormDO::getEnterpriseFullName,
                        SupplierFormDO::getEnterpriseShortName,
                        SupplierFormDO::getContact,
                        SupplierFormDO::getPhone,
                        SupplierFormDO::getRemark,
                        SupplierFormDO::getSubmitTime,
                        SupplierFormDO::getApproveBy,
                        SupplierFormDO::getApproveStatus,
                        SupplierFormDO::getApproveMemo,
                        SupplierFormDO::getApproveTime,
                        SupplierFormDO::getCreateTime,
                        SupplierFormDO::getUpdateTime));
    }

}
