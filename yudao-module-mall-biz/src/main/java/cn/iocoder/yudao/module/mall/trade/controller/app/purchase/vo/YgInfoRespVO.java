package cn.iocoder.yudao.module.mall.trade.controller.app.purchase.vo;

import lombok.Data;

/**
 * 员工信息查询结果DTO
 */
@Data
public class YgInfoRespVO {

    /**
     * 员工姓名
     */
    private String ygName;
    /**
     * 员工编号
     */
    private String ygNo;
    /**
     * 银行账号
     */
    private String bankAccount;
    /**
     * 部门编号
     */
    private String departmentNo;
    /**
     * 部门名称
     */
    private String departmentName;
    /**
     * 员工状态：1为可用，0：不可用
     */
    private String status;
    /**
     * 人员类型
     */
    private String ygType;
    /**
     * 公务卡号
     */
    private String cardNo;
    /**
     * 职务名称
     */
    private String jobTitle;
    /**
     * 联系电话
     */
    private String mobile;


}
