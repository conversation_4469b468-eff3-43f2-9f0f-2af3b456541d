package cn.iocoder.yudao.module.mall.trade.dal.mysql.orderstatistics;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.cart.TradeCartItemDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.orderstatistics.OrderStatisticsDO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.*;
import org.apache.ibatis.annotations.Param;

import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertMap;

/**
 * 订单统计 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface OrderStatisticsMapper extends BaseMapperX<OrderStatisticsDO> {

    default PageResult<OrderStatisticsDO> selectPage(OrderStatisticsPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<OrderStatisticsDO>()
                .eqIfPresent(OrderStatisticsDO::getSupplierId, reqVO.getSupplierId())
                .likeIfPresent(OrderStatisticsDO::getSupplierName, reqVO.getSupplierName())
                .eqIfPresent(OrderStatisticsDO::getType, reqVO.getType())
                .eqIfPresent(OrderStatisticsDO::getOrderNum, reqVO.getOrderNum())
                .eqIfPresent(OrderStatisticsDO::getOrderAmount, reqVO.getOrderAmount())
                .betweenIfPresent(OrderStatisticsDO::getStaticsTime, reqVO.getStaticsTime())
                .betweenIfPresent(OrderStatisticsDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(OrderStatisticsDO::getId));
    }

    default List<OrderStatisticsDO> selectList(OrderStatisticsExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<OrderStatisticsDO>()
                .eqIfPresent(OrderStatisticsDO::getSupplierId, reqVO.getSupplierId())
                .likeIfPresent(OrderStatisticsDO::getSupplierName, reqVO.getSupplierName())
                .eqIfPresent(OrderStatisticsDO::getType, reqVO.getType())
                .eqIfPresent(OrderStatisticsDO::getOrderNum, reqVO.getOrderNum())
                .eqIfPresent(OrderStatisticsDO::getOrderAmount, reqVO.getOrderAmount())
                .betweenIfPresent(OrderStatisticsDO::getStaticsTime, reqVO.getStaticsTime())
                .betweenIfPresent(OrderStatisticsDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(OrderStatisticsDO::getId));
    }

    default Map<String, OrderStatisticsDO> selectStaticsBySupplier(LocalDateTime date, Long supplierId) {
        String day = date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String month = date.format(DateTimeFormatter.ofPattern("yyyy-MM"));
        String year = date.format(DateTimeFormatter.ofPattern("yyyy"));
        List<OrderStatisticsDO> orderStatisticsDOS = selectList(new LambdaQueryWrapperX<OrderStatisticsDO>()
                .eq(OrderStatisticsDO::getSupplierId, supplierId)
                .in(OrderStatisticsDO::getStaticsTime, day, month, year));
        return convertMap(orderStatisticsDOS, OrderStatisticsDO::getStaticsTime);
    }

    List<OrderStatisticsVO> queryOrderStatistics(OrderStatisticsQueryVO queryVO);


    List<OrderStatisticsTotalResult> queryOrderStatisticsTotal(@Param("supplierId") Long supplierId);

    List<OrderStatisticsTotalResult> querySettleOrderStatisticsTotal(@Param("supplierId") Long supplierId);

    @TenantIgnore
    List<SupplierSellProportionRespVO> getSupplierSellProportion(@Param("tenantId") Long tenantId);

    @TenantIgnore
    List<SupplierSellCountSummaryRespVO> getSupplierSellProductCountSummary(@Param("tenantId") Long tenantId);

    @TenantIgnore
    List<ProductSellCountSummaryRespVO> getSellProductTotalSummary(@Param("tenantId") Long tenantId);

    @TenantIgnore
    List<ProductSellAmountSummaryRespVO> getSellProductAmountSummary(@Param("tenantId") Long tenantId);

    @TenantIgnore
    Long getSellCount(@Param("tenantId") Long tenantId);

    @TenantIgnore
    BigDecimal getSellAmount(@Param("tenantId") Long tenantId);

    @TenantIgnore
    BigDecimal getNotSettleAmount(@Param("tenantId") Long tenantId);

    @TenantIgnore
    List<OrderSummaryVO> getOrderSummary(@Param("tenantId") Long tenantId);

    @TenantIgnore
    OrderSummaryVO getAfterSaleSummary(@Param("tenantId") Long tenantId);

    @TenantIgnore
    List<OrderSummaryVO> getAuditSummary(@Param("tenantId") Long tenantId);

    @TenantIgnore
    List<OrderSummaryVO> getSettlementSummary(@Param("tenantId") Long tenantId);

    @TenantIgnore
    List<OrderDeptSummaryRespVO> getOrderSummaryByDept(@Param("tenantId") Long tenantId);

    @TenantIgnore
    List<OrderProjectSummaryRespVO> getOrderSummaryByProject(@Param("tenantId") Long tenantId);

    @TenantIgnore
    List<OrderSupplierSummaryRespVO> getOrderSummaryBySupplier(@Param("tenantId") Long tenantId);

    @TenantIgnore
    List<OrderAfterSaleSummaryRespVO> getAfterSaleOrderSummary(@Param("tenantId") Long tenantId);

    @TenantIgnore
    List<OrderSaleDaySummaryRespVO> dailySaleSummary(@Param("orderSaleSummaryReqVO") OrderSaleSummaryReqVO orderSaleSummaryReqVO, @Param("tenantId") Long tenantId);

    @TenantIgnore
    List<OrderSaleWeekSummaryRespVO> weeklySalesSummary(@Param("orderSaleSummaryReqVO") OrderSaleSummaryReqVO orderSaleSummaryReqVO, @Param("tenantId") Long tenantId);

    @TenantIgnore
    List<OrderSaleMonthSummaryRespVO> monthlySalesSummary(@Param("orderSaleMonthSummaryReqVO") OrderSaleMonthSummaryReqVO orderSaleMonthSummaryReqVO, @Param("tenantId") Long tenantId);

}
