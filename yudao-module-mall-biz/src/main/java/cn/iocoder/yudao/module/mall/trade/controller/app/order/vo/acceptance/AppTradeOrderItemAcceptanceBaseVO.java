package cn.iocoder.yudao.module.mall.trade.controller.app.order.vo.acceptance;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 订单商品验收单 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class AppTradeOrderItemAcceptanceBaseVO {

    @Schema(description = "订单ID")
    private Long orderId;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "订单明细ID")
    @NotNull(message = "订单明细ID不能为空")
    private Long orderItemId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户名")
    private String userName;

    @Schema(description = "部门ID")
    private Long deptId;

    @Schema(description = "申请人签名")
    private String applySign;

    @Schema(description = "证明材料")
    @NotEmpty(message = "证明材料不能为空")
    private String proofFile;

    @Schema(description = "实物照片，逗号分隔")
    private String otherFile;

    @Schema(description = "备注")
    private String memo;

}
