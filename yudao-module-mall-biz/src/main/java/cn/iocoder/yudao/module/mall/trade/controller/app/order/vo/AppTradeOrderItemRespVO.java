package cn.iocoder.yudao.module.mall.trade.controller.app.order.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * AppTradeOrderItemRespVO 类
 * 继承自 TradeOrderItemBaseReqVO，用于表示应用端的订单项响应对象。
 */
@Data
public class AppTradeOrderItemRespVO {

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单明细ID
     */
    private Long id;

    /**
     * 商品 SKU 编号
     */
    private Long skuId;

    /**
     * 商品 SKU 名称
     */
    private String skuName;

    /**
     * 商品图片
     */
    private String picUrl;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 商品 SKU 价格
     */
    private BigDecimal skuPrice;

    /**
     * 商品价格
     *
     */
    private BigDecimal skuTotalPrice;

    /**
     * 购买数量
     */
    private Integer count;

    /**
     * 固资建档状态：0-建档待处理, 1-建档已提交，2-无须建档 3-建档中 4-建档成功 5-建档失败
     * {@link cn.iocoder.yudao.module.mall.trade.enums.order.AssetStatusEnum}
     */
    private Integer assetStatus;

    /**
     * 是否为固定资产
     */
    private Boolean isAsset;

    /**
     * 验收状态
     * {@link cn.iocoder.yudao.module.mall.trade.enums.order.AcceptStatusEnum}
     */
    private Integer acceptStatus;

}