package cn.iocoder.yudao.module.mall.trade.controller.app.order.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/8/16
 */
@Data
public class ApprovalResultRespVO {


    /**
     * 审批级别
     */
    private String approvalLevel;

    /**
     * 审批时间格式"2022-06-23 15:06:45"
     */
    private String approvalTime;

    /**
     * 审批日期格式 20220623
     */
    private String approvalDate;

    /**
     * 审批状态 0 未审批 1审批通过 2审批驳回
     */
    private String auditStatus;

    /**
     * 审批意见
     */
    private String auditResult;

    /**
     * 是否签章 0 不签章 1 证书签章 2无证书签章
     */
    private String isSignature;

    /**
     * 签章图片	clob	签章图片（sfqz为0时该字段为空）	可空
     */
    private String signatureImage;

    /**
     * 审批人编号
     */
    private String approvalUserNo;

    /**
     * 审批角色名称
     */
    private String approvalRoleName;

    /**
     * 审批人名称
     */
    private String approvalUserName;

    /**
     * 是否签名1是  0否
     */
    private String isSign;

    /**
     * 签名图片	Clob	签名图片（base64格式图片）
     */
    private String signImage;

    /**
     * 是否财务审批
     */
    private String isFinancialAudit;
}
