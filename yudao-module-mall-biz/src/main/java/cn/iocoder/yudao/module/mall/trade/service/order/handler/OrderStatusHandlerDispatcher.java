package cn.iocoder.yudao.module.mall.trade.service.order.handler;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单状态处理调度处理器
 * <AUTHOR>
 */
@Component
@Slf4j
public class OrderStatusHandlerDispatcher  {

    @Resource
    private List<OrderStatusHandler> handlerList;

    public void handle(Integer status, Long orderId) {
        if(CollUtil.isEmpty(handlerList)) {
            return;
        }
        List<OrderStatusHandler> hitHandlerList = handlerList.stream().filter(item -> item.getTargetStatus().equals(status)).collect(Collectors.toList());
        if(CollUtil.isEmpty(hitHandlerList)) {
            log.info("订单状态{}处理器为空，忽略", status);
            return;
        }
        log.info("订单状态处理器处理开始，订单:{}，状态:{}", orderId, status);
        hitHandlerList.forEach(handler -> {
            handler.handle(orderId);
        });
    }


}
