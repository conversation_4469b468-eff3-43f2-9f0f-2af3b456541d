package cn.iocoder.yudao.module.mall.trade.dal.dataobject.order;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 协同订单项 DO
 *
 * <AUTHOR>
 */
@TableName(value = "trade_team_order_item", autoResultMap = true)
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class TradeTeamOrderItemDO extends BaseDO {

    /**
     * 编号，使用雪花ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 用户编号
     *
     * 关联 MemberUserDO 的 id 编号
     */
    private Long userId;

    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 订单ID
     *
     * 关联 {@link TradeTeamOrderDO#getId()}
     */
    private Long teamOrderId;

    /**
     * 商品 SKU 编号
     *
     * 关联 ProductSkuDO 的 id 编号
     */
    private Long skuId;
    /**
     * 商品分类ID
     */
    private Long categoryId;
    /**
     * 商品分类路径ID
     */
    private String fullCategoryId;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 供应商类型
     */
    private String supplierName;

    /**
     * 商品图片
     */
    private String picUrl;

    /**
     * 购买数量
     */
    private Integer count;

    /**
     * 商品价格
     *
     */
    private BigDecimal skuPrice;

    /**
     * 商品价格
     *
     */
    private BigDecimal skuTotalPrice;


}

