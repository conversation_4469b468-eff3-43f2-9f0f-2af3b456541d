package cn.iocoder.yudao.module.mall.basis.controller.app.supplier;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.idempotent.core.annotation.Idempotent;
import cn.iocoder.yudao.framework.ratelimiter.core.annotation.RateLimiter;
import cn.iocoder.yudao.framework.ratelimiter.core.keyresolver.impl.ClientIpRateLimiterKeyResolver;
import cn.iocoder.yudao.module.mall.basis.controller.admin.supplier.vo.SupplierPageReqVO;
import cn.iocoder.yudao.module.mall.basis.controller.app.supplier.vo.AppSupplierRegisterReqVO;
import cn.iocoder.yudao.module.mall.basis.controller.app.supplier.vo.AppSupplierRespVO;
import cn.iocoder.yudao.module.mall.basis.controller.app.supplier.vo.AppSupplierServiceRespVO;
import cn.iocoder.yudao.module.mall.basis.controller.app.supplier.vo.AppSupplierSimpleRespVO;
import cn.iocoder.yudao.module.mall.basis.convert.supplier.SupplierConvert;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.supplier.SupplierDO;
import cn.iocoder.yudao.module.mall.basis.service.supplier.SupplierFormService;
import cn.iocoder.yudao.module.mall.basis.service.supplier.SupplierService;
import cn.iocoder.yudao.module.mall.enums.basis.SupplierStatusEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 APP - 供应商配置")
@RestController
@RequestMapping("/mall/supplier")
@Validated
@Slf4j
public class AppSupplierController {

    @Resource
    private SupplierService supplierService;
    @Resource
    private SupplierFormService supplierFormService;

    @GetMapping("/get-detail")
    @Operation(summary = "获得供应商详情")
    public CommonResult<AppSupplierRespVO> getSupplierDetail(@RequestParam("id") Long id) {
        SupplierDO supplierDO = supplierService.getSupplier(id);
        return success(SupplierConvert.INSTANCE.convert02(supplierDO));
    }

    @GetMapping("/get-top-list")
    @Operation(summary = "获得前N个供应商列表")
    public CommonResult<List<AppSupplierSimpleRespVO>> getSupplierList() {
        return success(SupplierConvert.INSTANCE.convertList02(supplierService.getSupplierListByType(null)));
    }

    @GetMapping("/get-service-list")
    @Operation(summary = "分页查询供应商客服列表")
    public CommonResult<PageResult<AppSupplierServiceRespVO>> pageQuery4Service(@Valid PageParam reqVO) {
        SupplierPageReqVO pageReqVO = new SupplierPageReqVO();
        pageReqVO.setStatus(SupplierStatusEnum.ENABLE.getStatus());
        BeanUtil.copyProperties(reqVO, pageReqVO);
        PageResult<SupplierDO> pageResult = supplierService.getSupplierPage(pageReqVO);
        return success(SupplierConvert.INSTANCE.convertPage02(pageResult));
    }

    @PostMapping("/save-register")
    @Operation(summary = "供应商用户注册")
    @Idempotent(timeout = 30, message = "提交处理中，请勿重复提交")
    @RateLimiter(count = 10, timeUnit = TimeUnit.HOURS, keyResolver = ClientIpRateLimiterKeyResolver.class)
    public CommonResult<Boolean> saveSupplierRegister(@Valid @RequestBody AppSupplierRegisterReqVO reqVO) {
        return success(supplierFormService.saveSupplierRegister(reqVO));
    }

}
