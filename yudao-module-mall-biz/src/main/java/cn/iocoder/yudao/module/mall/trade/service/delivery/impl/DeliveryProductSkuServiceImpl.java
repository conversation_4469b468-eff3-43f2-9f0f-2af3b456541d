package cn.iocoder.yudao.module.mall.trade.service.delivery.impl;

import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.delivery.DeliveryProductSkuDO;
import cn.iocoder.yudao.module.mall.trade.dal.mysql.delivery.DeliveryProductSkuMapper;
import cn.iocoder.yudao.module.mall.trade.service.delivery.DeliveryProductSkuService;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.mall.trade.enums.ErrorCodeConstants.DELIVERY_PRODUCT_SKU_NOT_EXISTS;

/**
 * 物流订单商品sku关系 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DeliveryProductSkuServiceImpl extends ServiceImpl<DeliveryProductSkuMapper, DeliveryProductSkuDO> implements DeliveryProductSkuService {

    @Resource
    private DeliveryProductSkuMapper deliveryProductSkuMapper;

    @Override
    public void deleteDeliveryProductSku(Long id) {
        // 校验存在
        validateDeliveryProductSkuExists(id);
        // 删除
        deliveryProductSkuMapper.deleteById(id);
    }

    private void validateDeliveryProductSkuExists(Long id) {
        if (deliveryProductSkuMapper.selectById(id) == null) {
            throw exception(DELIVERY_PRODUCT_SKU_NOT_EXISTS);
        }
    }

    @Override
    public DeliveryProductSkuDO getDeliveryProductSku(Long id) {
        return deliveryProductSkuMapper.selectById(id);
    }

    @Override
    public List<DeliveryProductSkuDO> getListByDelivery(Long deliveryId) {
        return deliveryProductSkuMapper.selectList(Wrappers.lambdaQuery(DeliveryProductSkuDO.class)
                .eq(DeliveryProductSkuDO::getDeliveryId, deliveryId)
                .orderByAsc(DeliveryProductSkuDO::getId));
    }

    @Override
    public Boolean sync(List<DeliveryProductSkuDO> newItems, Wrapper<DeliveryProductSkuDO> queryWrapper) {
        List<Long> deleteIds = new ArrayList<>();
        List<DeliveryProductSkuDO> updates = new ArrayList<>();
        List<DeliveryProductSkuDO> inserts = new ArrayList<>();
        List<DeliveryProductSkuDO> oldItems = deliveryProductSkuMapper.selectList(queryWrapper);
        // 新的数据长度大于表中的长度，更新和插入操作
        if(newItems.size() > oldItems.size()){
            int i = 0;
            for (; i < oldItems.size(); i++) {
                newItems.get(i).setId(oldItems.get(i).getId());
                updates.add(newItems.get(i));
            }
            inserts.addAll(newItems.subList(i, newItems.size()));
        }
        else {
            // 新的数据长度小于表中的长度，删除和更新操作
            Integer sub = oldItems.size() - newItems.size();
            for (int i = 0; i < oldItems.size(); i++) {
                if(i <  sub){
                    deleteIds.add(oldItems.get(i).getId());
                }
                else {
                    newItems.get(i - sub).setId(oldItems.get(i).getId());
                    updates.add(newItems.get(i - sub));
                }
            }
        }

        if(updates.size() > 0){
            this.saveOrUpdateBatch(updates);
        }

        if(deleteIds.size() > 0){
            this.removeByIds(deleteIds);
        }

        if(inserts.size() > 0){
            this.saveOrUpdateBatch(inserts);
        }

        return true;
    }
}
