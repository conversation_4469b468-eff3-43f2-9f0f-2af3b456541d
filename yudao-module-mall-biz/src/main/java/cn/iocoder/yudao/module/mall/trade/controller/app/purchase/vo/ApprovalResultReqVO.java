package cn.iocoder.yudao.module.mall.trade.controller.app.purchase.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/8/2
 */
@Data
public class ApprovalResultReqVO {

    /**
     * 模块代码（由审批系统分配）
     */
    @JsonProperty("mkdm")
    private String moduleName;

    /**
     * 业务编号
     */
    @JsonProperty("ywbh")
    private String businessCode;

    /**
     * 业务流水号
     */
    @JsonProperty("ywlsh")
    private String businessNo;

    /**
     * 	请求类型	1获取业务数据，2回写审批状态，3通知单据被挂起（如果只想回写状态，收到1和3返回一个固定值即可）	是
     */
    private String method;

    /**
     * 审批结果 1通过 2驳回，0撤销
     */
    @JsonProperty("spjg")
    private String auditStatus;

    /**
     * 	签名
     */
    private String sign;
}
