package cn.iocoder.yudao.module.mall.trade.dal.mysql.delivery;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.delivery.DeliveryDO;
import jodd.util.StringUtil;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.mall.trade.controller.admin.delivery.vo.*;

/**
 * 物流信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DeliveryMapper extends BaseMapperX<DeliveryDO> {

    default PageResult<DeliveryDO> selectPage(DeliveryPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DeliveryDO>()
                .eqIfPresent(DeliveryDO::getIsCheck, reqVO.getIsCheck())
                .eq(reqVO.getFromNumber() != null && StringUtil.isNotBlank(reqVO.getFromNumber()), DeliveryDO::getFromNumber, reqVO.getFromNumber())
                .likeIfPresent(DeliveryDO::getFromName, reqVO.getFromName())
                .eq(reqVO.getToNumber() != null && StringUtil.isNotBlank(reqVO.getToNumber()), DeliveryDO::getToNumber, reqVO.getToNumber())
                .likeIfPresent(DeliveryDO::getToName, reqVO.getToName())
                .eq(reqVO.getCurNumber() != null && StringUtil.isNotBlank(reqVO.getCurNumber()), DeliveryDO::getCurNumber, reqVO.getCurNumber())
                .eq(reqVO.getCurName() != null && StringUtil.isNotBlank(reqVO.getCurName()), DeliveryDO::getCurName, reqVO.getCurName())
                .eqIfPresent(DeliveryDO::getSubscribe, reqVO.getSubscribe())
                .betweenIfPresent(DeliveryDO::getCreateTime, reqVO.getCreateTime())
                .eq(reqVO.getOrderNo() != null && StringUtil.isNotBlank(reqVO.getOrderNo()), DeliveryDO::getOrderNo, reqVO.getOrderNo())
                .eq(reqVO.getSource() != null && StringUtil.isNotBlank(reqVO.getSource()), DeliveryDO::getSource, reqVO.getSource())
                .eq(reqVO.getCom() != null && StringUtil.isNotBlank(reqVO.getCom()), DeliveryDO::getCom, reqVO.getCom())
                .eq(reqVO.getNum() != null && StringUtil.isNotBlank(reqVO.getNum()), DeliveryDO::getNum, reqVO.getNum())
                .eqIfPresent(DeliveryDO::getSupplierName, reqVO.getSupplierName())
                .eqIfPresent(DeliveryDO::getSupplierId, reqVO.getSupplierId())
                .eqIfPresent(DeliveryDO::getState, reqVO.getState())
                .likeIfPresent(DeliveryDO::getName, reqVO.getName())
                .orderByDesc(DeliveryDO::getId));
    }

    default List<DeliveryDO> selectList(DeliveryExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DeliveryDO>()
                .eqIfPresent(DeliveryDO::getIsCheck, reqVO.getIsCheck())
                .eqIfPresent(DeliveryDO::getFromNumber, reqVO.getFromNumber())
                .likeIfPresent(DeliveryDO::getFromName, reqVO.getFromName())
                .eqIfPresent(DeliveryDO::getToNumber, reqVO.getToNumber())
                .likeIfPresent(DeliveryDO::getToName, reqVO.getToName())
                .eqIfPresent(DeliveryDO::getCurNumber, reqVO.getCurNumber())
                .likeIfPresent(DeliveryDO::getCurName, reqVO.getCurName())
                .eqIfPresent(DeliveryDO::getSubscribe, reqVO.getSubscribe())
                .betweenIfPresent(DeliveryDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(DeliveryDO::getOrderNo, reqVO.getOrderNo())
                .eqIfPresent(DeliveryDO::getSupplierId, reqVO.getSupplierId())
                .eqIfPresent(DeliveryDO::getSupplierName, reqVO.getSupplierName())
                .eqIfPresent(DeliveryDO::getSource, reqVO.getSource())
                .eqIfPresent(DeliveryDO::getCom, reqVO.getCom())
                .eqIfPresent(DeliveryDO::getNum, reqVO.getNum())
                .eqIfPresent(DeliveryDO::getState, reqVO.getState())
                .likeIfPresent(DeliveryDO::getName, reqVO.getName())
                .orderByDesc(DeliveryDO::getId));
    }

}
