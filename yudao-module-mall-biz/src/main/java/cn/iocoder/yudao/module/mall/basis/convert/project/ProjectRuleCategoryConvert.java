package cn.iocoder.yudao.module.mall.basis.convert.project;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.basis.controller.admin.project.vo.ProjectRuleCategoryCreateReqVO;
import cn.iocoder.yudao.module.mall.basis.controller.admin.project.vo.ProjectRuleCategoryRespVO;
import cn.iocoder.yudao.module.mall.basis.controller.admin.project.vo.ProjectRuleCategoryUpdateReqVO;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.project.ProjectRuleCategoryDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 规则关联分类 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ProjectRuleCategoryConvert {

    ProjectRuleCategoryConvert INSTANCE = Mappers.getMapper(ProjectRuleCategoryConvert.class);

    ProjectRuleCategoryDO convert(ProjectRuleCategoryCreateReqVO bean);

    List<ProjectRuleCategoryDO> convert(List<ProjectRuleCategoryCreateReqVO> bean);

    ProjectRuleCategoryDO convert(ProjectRuleCategoryUpdateReqVO bean);

    ProjectRuleCategoryRespVO convert(ProjectRuleCategoryDO bean);

    List<ProjectRuleCategoryRespVO> convertList(List<ProjectRuleCategoryDO> list);

    PageResult<ProjectRuleCategoryRespVO> convertPage(PageResult<ProjectRuleCategoryDO> page);

}
