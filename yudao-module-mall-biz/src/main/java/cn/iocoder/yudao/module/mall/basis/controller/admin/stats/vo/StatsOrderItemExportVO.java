package cn.iocoder.yudao.module.mall.basis.controller.admin.stats.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 订单明细信息导出
 *
 * <AUTHOR>
 * @Date 2024/6/13
 */
@Schema(description = "管理后台 - 运营统计中订单明细导出excel VO")
@Data
public class StatsOrderItemExportVO {

    @Excel(name = "平台SKU", width = 20)
    private Long skuId;

    @Excel(name = "三方SKU", width = 20)
    private String thirdSkuId;

    @Excel(name = "商品名称", width = 45)
    private String skuName;

    @Excel(name = "一级分类", width = 20)
    private String categoryName1;

    @Excel(name = "二级分类", width = 20)
    private String categoryName2;

    @Excel(name = "三级分类", width = 20)
    private String categoryName3;

    @Excel(name = "运营标签", width = 15)
    private String tagNames;

    @Excel(name = "商品数量", width = 15)
    private Integer quantity;

    @Excel(name = "商品单价", width = 20)
    private BigDecimal price;

    @Excel(name = "商品金额", width = 20)
    private BigDecimal totalPrice;

    @Excel(name = "订单号", width = 24)
    private String orderNo;

    @Excel(name = "三方订单号",width = 24, replace = {"--_null"})
    private String thirdOrderId;

    @Excel(name = "运费金额", width = 15, replace = {"0_null"})
    private BigDecimal deliveryPrice;

    @Excel(name = "订单总金额", width = 15)
    private BigDecimal orderPrice;

    @Excel(name = "退款金额", width = 15, replace = {"0_null"})
    private BigDecimal refundPrice;

    @Excel(name = "供应商名称", width = 20)
    private String supplierName;

    @Excel(name = "订单状态", replace = {"未确认_1", "已确认_2", "已发货_3", "已送达_4", "已签收_5", "已完成_8", "已取消_9"}, width = 15)
    private Integer orderStatus;

    @Excel(name = "开票状态", replace = {"未开票_0", "已申请开票_1", "开票完成_2", "开票失败_3", "验真处理中_4", "验真完成_5", "验真失败_6"}, width = 15)
    private Integer invoiceStatus;

    @Excel(name = "下单时间", exportFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd HH:mm:ss", replace = {"--_null"}, width = 24)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime submitTime;

    @Excel(name = "完成时间", exportFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd HH:mm:ss", replace = {"--_null"}, width = 24)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime finishTime;

    @Excel(name = "下单人", width = 15)
    private String userName;

    @Excel(name = "下单人工号", width = 15)
    private String userNo;

    @Excel(name = "所在部门", width = 15)
    private String deptName;

    @Excel(name = "收货人",width = 15)
    private String receiverName;

    @Excel(name = "收货地址", width = 15)
    private String receiverAddress;

    @Excel(name = "下单终端", replace = {"PC端_1", "移动端H5_20"}, width = 15)
    private Integer platform;
    

}
