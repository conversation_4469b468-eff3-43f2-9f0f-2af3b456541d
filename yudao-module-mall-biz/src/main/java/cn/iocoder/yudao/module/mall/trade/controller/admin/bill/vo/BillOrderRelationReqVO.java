package cn.iocoder.yudao.module.mall.trade.controller.admin.bill.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 管理后台 - 账单创建 Request VO
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 账单创建 Request VO")
@Data
@ToString(callSuper = true)
public class BillOrderRelationReqVO {
    /**
     * 账单ID
     */
    @Schema(description = "账单ID")
    @NotNull(message = "账单ID不能为空")
    private Long billId;

    /**
     * 订单ID
     */
    @Schema(description = "订单编码")
    @NotEmpty(message = "订单编码不能为空")
    private List<String> orderNos;


}
