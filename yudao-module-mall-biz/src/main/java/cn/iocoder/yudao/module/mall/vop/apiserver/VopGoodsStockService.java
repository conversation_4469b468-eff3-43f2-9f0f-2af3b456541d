package cn.iocoder.yudao.module.mall.vop.apiserver;

import cn.iocoder.yudao.module.mall.vop.common.CommonRequestHandler;
import cn.iocoder.yudao.module.mall.vop.req.VopGoodsQueryAreaStockStatesReq;
import com.jd.open.api.sdk.domain.vopkc.SkuInfoGoodsProvider.request.getNewStockById.GetStockByIdGoodsReq;
import com.jd.open.api.sdk.request.vopkc.VopGoodsGetNewStockByIdRequest;
import com.jd.open.api.sdk.request.vopkc.VopGoodsQueryAreaStockStatesRequest;
import com.jd.open.api.sdk.response.vopkc.VopGoodsGetNewStockByIdResponse;
import com.jd.open.api.sdk.response.vopkc.VopGoodsQueryAreaStockStatesResponse;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description 商品库存API
 * <AUTHOR>
 * @Date 2023/6/12 9:07
 */

@Component
public class VopGoodsStockService {

    @Autowired
    CommonRequestHandler commonRequestHandler;

    /**
     * 查询某个商品的全地址库存
     *
     * @param vopGoodsQueryAreaStockStatesReq
     * @return
     */
    public VopGoodsQueryAreaStockStatesResponse queryAreaStock(VopGoodsQueryAreaStockStatesReq vopGoodsQueryAreaStockStatesReq) {
        VopGoodsQueryAreaStockStatesRequest request = new VopGoodsQueryAreaStockStatesRequest();
        BeanUtils.copyProperties(vopGoodsQueryAreaStockStatesReq, request);
        return (VopGoodsQueryAreaStockStatesResponse) commonRequestHandler.executeHandler(request);
    }

    /**
     * 根据京东四级地址查询库存状态（有货、无货、无货预定等），批量获取库存接口
     *
     * @param stockByIdGoodsReq
     * @return
     */
    public VopGoodsGetNewStockByIdResponse getNewStockById(GetStockByIdGoodsReq stockByIdGoodsReq) {
        VopGoodsGetNewStockByIdRequest request = new VopGoodsGetNewStockByIdRequest();
        request.setGetStockByIdGoodsReq(stockByIdGoodsReq);
        return (VopGoodsGetNewStockByIdResponse) commonRequestHandler.executeHandler(request);
    }

}
