package cn.iocoder.yudao.module.mall.trade.dal.mysql.purchase;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.mall.trade.controller.admin.purchase.vo.PurchasePageReqVO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.purchase.PurchaseDO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 采购 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PurchaseMapper extends BaseMapperX<PurchaseDO> {

    default PurchaseDO getByBpmNo(String bpmNo) {
        return selectOne(PurchaseDO::getBpmNo, bpmNo);
    }

    default PageResult<PurchaseDO> selectPage(PurchasePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PurchaseDO>()
                .eqIfPresent(PurchaseDO::getStatus, reqVO.getStatus())
                .eqIfPresent(PurchaseDO::getUserId, reqVO.getUserId())
                .eqIfPresent(PurchaseDO::getProjectNo, reqVO.getProjectNo())
                .likeIfPresent(PurchaseDO::getProjectName, reqVO.getProjectName())
                .eqIfPresent(PurchaseDO::getAuditStatus, reqVO.getAuditStatus())
                .betweenIfPresent(PurchaseDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(PurchaseDO::getId));
    }

    PurchaseDO selectById2(@Param("id") Long id, @Param("userId") Long userId);

    Page<PurchaseDO> selectPage2(Page pageInfo, @Param("params") PurchasePageReqVO reqVO);

}
