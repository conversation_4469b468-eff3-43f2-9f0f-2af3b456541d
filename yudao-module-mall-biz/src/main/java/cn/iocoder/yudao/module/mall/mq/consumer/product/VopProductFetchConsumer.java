package cn.iocoder.yudao.module.mall.mq.consumer.product;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.mq.message.product.VopProductFetchMessage;
import cn.iocoder.yudao.module.mall.product.service.vopgoods.VopGoodsBridgeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.function.Consumer;

/**
 * 针对 京东VOP商品拉取 的消费者
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class VopProductFetchConsumer implements Consumer<VopProductFetchMessage> {

    @Resource
    private VopGoodsBridgeService vopGoodsBridgeService;

    @Override
    public void accept(VopProductFetchMessage message) {
        log.info("[accept][消息内容-VOP商品拉取({})({}),租户:{}]", message.getSkuIds(), message.getPoolName(), TenantContextHolder.getTenantId());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            vopGoodsBridgeService.goodsUpdate(message.getPoolName(), message.getSkuIds().toArray(new Long[]{}));
        } catch(Exception e) {
            log.error("VOP商品拉取消息处理失败:", e);
        }
        stopWatch.stop();
        log.info("[accept][消息内容-VOP商品拉取处理完成，耗时：{}秒", stopWatch.getTotalTimeSeconds());
    }

}
