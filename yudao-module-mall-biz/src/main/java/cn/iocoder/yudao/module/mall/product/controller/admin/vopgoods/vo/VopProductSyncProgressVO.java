package cn.iocoder.yudao.module.mall.product.controller.admin.vopgoods.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * VOP商品同步进度信息
 *
 * <AUTHOR>
 */
@Data
public class VopProductSyncProgressVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 分类ID参数
     */
    private Long categoryId;

    /**
     * 最大页数参数
     */
    private Long maxPageSize;

    /**
     * 最小价格参数
     */
    private BigDecimal minPrice;

    /**
     * 最大价格参数
     */
    private BigDecimal maxPrice;

    /**
     * 当前处理的分类索引
     */
    private Integer currentCategoryIndex;

    /**
     * 当前处理的页码
     */
    private Integer currentPageIndex;

    /**
     * 总分类数量
     */
    private Integer totalCategoryCount;

    /**
     * 当前分类的总页数
     */
    private Integer currentCategoryPageCount;

    /**
     * 已同步的商品总数
     */
    private Long syncedProductCount;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;

    /**
     * 当前处理的分类映射ID
     */
    private Long currentVopCategoryMappingId;

    /**
     * 当前处理的分类全路径ID
     */
    private String currentVopFullCategoryId;

    /**
     * 是否完成
     */
    private Boolean completed;

    /**
     * 生成进度缓存Key
     */
    public static String generateProgressKey(Long tenantId, Long categoryId, Long maxPageSize) {
        return String.format("mall_vop_product_sync_progress:%d:%s:%s", 
            tenantId, 
            categoryId != null ? categoryId.toString() : "all",
            maxPageSize != null ? maxPageSize.toString() : "default");
    }

    /**
     * 检查参数是否匹配
     */
    public boolean isParametersMatch(Long categoryId, Long maxPageSize, BigDecimal minPrice, BigDecimal maxPrice) {
        return java.util.Objects.equals(this.categoryId, categoryId) &&
               java.util.Objects.equals(this.maxPageSize, maxPageSize) &&
               java.util.Objects.equals(this.minPrice, minPrice) &&
               java.util.Objects.equals(this.maxPrice, maxPrice);
    }

    /**
     * 更新进度
     */
    public void updateProgress(Integer categoryIndex, Integer pageIndex, Long syncCount) {
        this.currentCategoryIndex = categoryIndex;
        this.currentPageIndex = pageIndex;
        this.syncedProductCount = syncCount;
        this.lastUpdateTime = LocalDateTime.now();
    }

    /**
     * 标记完成
     */
    public void markCompleted() {
        this.completed = true;
        this.lastUpdateTime = LocalDateTime.now();
    }

    /**
     * 计算总体进度百分比
     */
    public int calculateProgressPercentage() {
        if (totalCategoryCount == null || totalCategoryCount == 0) {
            return 0;
        }
        
        if (completed != null && completed) {
            return 100;
        }
        
        if (currentCategoryIndex == null) {
            return 0;
        }
        
        // 计算已完成的分类进度
        double completedCategoriesProgress = (double) currentCategoryIndex / totalCategoryCount;
        
        // 计算当前分类的页面进度
        double currentCategoryProgress = 0;
        if (currentCategoryPageCount != null && currentCategoryPageCount > 0 && currentPageIndex != null) {
            currentCategoryProgress = (double) currentPageIndex / currentCategoryPageCount / totalCategoryCount;
        }
        
        return (int) Math.min(99, (completedCategoriesProgress + currentCategoryProgress) * 100);
    }
}
