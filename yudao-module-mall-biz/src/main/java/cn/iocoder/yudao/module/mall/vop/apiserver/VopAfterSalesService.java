package cn.iocoder.yudao.module.mall.vop.apiserver;

import cn.iocoder.yudao.module.mall.vop.common.CommonRequestHandler;
import cn.iocoder.yudao.module.mall.vop.req.VopAfsCancelAfsApplyReq;
import cn.iocoder.yudao.module.mall.vop.req.VopAfsGetAfsOutlineReq;
import cn.iocoder.yudao.module.mall.vop.req.VopAfsGetGoodsAttributesReq;
import cn.iocoder.yudao.module.mall.vop.req.VopAfsQueryAfsAddressInfosReq;
import cn.iocoder.yudao.module.mall.vop.req.VopAfsQueryLogicticsInfoReq;
import com.jd.open.api.sdk.domain.vopsh.OperaAfterSaleOpenProvider.request.createAfsApply.ApplyAfterSaleOpenReq;
import com.jd.open.api.sdk.domain.vopsh.OperaAfterSaleOpenProvider.request.updateSendInfo.UpdateAfterSaleWayBillOpenReq;
import com.jd.open.api.sdk.request.vopsh.VopAfsCancelAfsApplyRequest;
import com.jd.open.api.sdk.request.vopsh.VopAfsConfirmAfsOrderRequest;
import com.jd.open.api.sdk.request.vopsh.VopAfsCreateAfsApplyRequest;
import com.jd.open.api.sdk.request.vopsh.VopAfsFindRefundInfoRequest;
import com.jd.open.api.sdk.request.vopsh.VopAfsGetAfsOutlineRequest;
import com.jd.open.api.sdk.request.vopsh.VopAfsGetGoodsAttributesRequest;
import com.jd.open.api.sdk.request.vopsh.VopAfsQueryAfsAddressInfosRequest;
import com.jd.open.api.sdk.request.vopsh.VopAfsQueryLogicticsInfoRequest;
import com.jd.open.api.sdk.request.vopsh.VopAfsUpdateSendInfoRequest;
import com.jd.open.api.sdk.response.vopsh.VopAfsCancelAfsApplyResponse;
import com.jd.open.api.sdk.response.vopsh.VopAfsConfirmAfsOrderResponse;
import com.jd.open.api.sdk.response.vopsh.VopAfsCreateAfsApplyResponse;
import com.jd.open.api.sdk.response.vopsh.VopAfsFindRefundInfoResponse;
import com.jd.open.api.sdk.response.vopsh.VopAfsGetAfsOutlineResponse;
import com.jd.open.api.sdk.response.vopsh.VopAfsGetGoodsAttributesResponse;
import com.jd.open.api.sdk.response.vopsh.VopAfsQueryAfsAddressInfosResponse;
import com.jd.open.api.sdk.response.vopsh.VopAfsQueryLogicticsInfoResponse;
import com.jd.open.api.sdk.response.vopsh.VopAfsUpdateSendInfoResponse;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description 售后服务API
 * <AUTHOR>
 * @Date 2023/6/14 17:58
 */

@Component
public class VopAfterSalesService {

    @Autowired
    CommonRequestHandler commonRequestHandler;

    /**
     * 申请单+订单号维度查询出所对应的物流信息
     * @param thirdApplyId
     * @param orderId
     * @return
     */
    public VopAfsQueryLogicticsInfoResponse queryLogicticsInfo(String thirdApplyId, Long orderId) {
        VopAfsQueryLogicticsInfoRequest request = new VopAfsQueryLogicticsInfoRequest();
        request.setOrderId(orderId);
        request.setOriginalOrderId(orderId);
        request.setThirdApplyId(thirdApplyId);
        request.setPageNo(1);
        request.setPageSize(100);
        return (VopAfsQueryLogicticsInfoResponse) commonRequestHandler.executeHandler(request);
    }

    /**
     * 客户申请售后服务，申请售后时需要填入京东子订单号、客户联系信息、取件信息、返件信息等内容。
     *
     * @param applyAfterSaleOpenReq
     * @return
     */
    public VopAfsCreateAfsApplyResponse createAfsApply(ApplyAfterSaleOpenReq applyAfterSaleOpenReq) {
        VopAfsCreateAfsApplyRequest request = new VopAfsCreateAfsApplyRequest();
        request.setApplyAfterSaleOpenReq(applyAfterSaleOpenReq);
        return (VopAfsCreateAfsApplyResponse) commonRequestHandler.executeHandler(request);
    }

    /**
     * 批量查询订单下商品售后权益
     *
     * @param vopAfsGetGoodsAttributesReq
     * @return
     */
    public VopAfsGetGoodsAttributesResponse getGoodsAttributes(VopAfsGetGoodsAttributesReq vopAfsGetGoodsAttributesReq) {
        VopAfsGetGoodsAttributesRequest request = new VopAfsGetGoodsAttributesRequest();
        BeanUtils.copyProperties(vopAfsGetGoodsAttributesReq, request);
        return (VopAfsGetGoodsAttributesResponse) commonRequestHandler.executeHandler(request);
    }

    /**
     * 第三方申请售后单号维度取消售后申请单
     *
     * @param vopAfsCancelAfsApplyReq
     * @return
     */
    public VopAfsCancelAfsApplyResponse cancelAfsApply(VopAfsCancelAfsApplyReq vopAfsCancelAfsApplyReq) {
        VopAfsCancelAfsApplyRequest request = new VopAfsCancelAfsApplyRequest();
        BeanUtils.copyProperties(vopAfsCancelAfsApplyReq, request);
        return (VopAfsCancelAfsApplyResponse) commonRequestHandler.executeHandler(request);
    }

    /**
     * 客户寄回方式售后，查询寄回地址
     *
     * @param vopAfsQueryAfsAddressInfosReq
     * @return
     */
    public VopAfsQueryAfsAddressInfosResponse queryAfsAddressInfos(VopAfsQueryAfsAddressInfosReq vopAfsQueryAfsAddressInfosReq) {
        VopAfsQueryAfsAddressInfosRequest request = new VopAfsQueryAfsAddressInfosRequest();
        BeanUtils.copyProperties(vopAfsQueryAfsAddressInfosReq, request);
        return (VopAfsQueryAfsAddressInfosResponse) commonRequestHandler.executeHandler(request);
    }

    /**
     * 在返回京东方式为客户发货场景，客服审核通过后，会给用户通过短信推送邮寄地址，客户需要填写运单信息（运单号、货运公司、邮费、运单明细）。
     *
     * @param updateAfterSaleWayBillOpenReq
     * @return
     */
    public VopAfsUpdateSendInfoResponse updateSendInfo(UpdateAfterSaleWayBillOpenReq updateAfterSaleWayBillOpenReq) {
        VopAfsUpdateSendInfoRequest request = new VopAfsUpdateSendInfoRequest();
        request.setUpdateAfterSaleWayBillOpenReq(updateAfterSaleWayBillOpenReq);
        return (VopAfsUpdateSendInfoResponse) commonRequestHandler.executeHandler(request);
    }

    /**
     * 查询售后概要信息,根据入参不同，出参组装规则不同：
     * 【orderId= 京东订单号】此订单维度概要信息；
     * 【orderId= 京东订单号，thirdApplyId = 三方申请单号】此申请单维度概要信息；
     * 【orderId= 京东订单号，wareId= 商品ID】/【orderId=京东订单号，thirdApplyId= 三方申请单号，wareId= 商品ID】 此商品ID维度概要信息；
     * 【orderId= 京东订单号，thirdApplyId=1】此京东订单号下所有申请单概要列表；
     * 【orderId= 京东订单号，wareId= 1】此京东订单号下所有商品概要列表；
     * 【orderId= 京东订单号，thirdApplyId= 三方申请单号，wareId= 1】 三方申请单号下所有商品概要列表。
     *
     * @param vopAfsGetAfsOutlineReq
     * @return
     */
    public VopAfsGetAfsOutlineResponse getAfsOutline(VopAfsGetAfsOutlineReq vopAfsGetAfsOutlineReq) {
        VopAfsGetAfsOutlineRequest request = new VopAfsGetAfsOutlineRequest();
        BeanUtils.copyProperties(vopAfsGetAfsOutlineReq, request);
        return (VopAfsGetAfsOutlineResponse) commonRequestHandler.executeHandler(request);
    }

    /**
     * 申请单维度查询退款明细接口
     *
     * @param thirdApplyId
     * @param orderId
     * @return
     */
    public VopAfsFindRefundInfoResponse findRefundInfo(String thirdApplyId, Long orderId) {
        VopAfsFindRefundInfoRequest request = new VopAfsFindRefundInfoRequest();
        request.setThirdApplyId(thirdApplyId);
        request.setOrderId(orderId);
        return (VopAfsFindRefundInfoResponse) commonRequestHandler.executeHandler(request);
    }

    /**
     * 第三方申请售后单号维度确认售后完成。
     *
     * @param thirdApplyId
     * @param orderId
     * @return
     */
    public VopAfsConfirmAfsOrderResponse confirmAfsOrder(String thirdApplyId, Long orderId) {
        VopAfsConfirmAfsOrderRequest request = new VopAfsConfirmAfsOrderRequest();
        request.setThirdApplyId(thirdApplyId);
        request.setOrderId(orderId);
        return (VopAfsConfirmAfsOrderResponse) commonRequestHandler.executeHandler(request);
    }

}
