package cn.iocoder.yudao.module.mall.trade.controller.app.aftersale.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Schema(description = "订单明细详情参数")
@Data
public class AppOrderItemReq {

    /**
     * 订单号
     */
    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "订单号不能为空")
    private String orderNo;

    /**
     * 订单明细ID
     */
    @Schema(description = "订单明细ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "订单明细ID不能为空")
    private Long orderItemId;

}
