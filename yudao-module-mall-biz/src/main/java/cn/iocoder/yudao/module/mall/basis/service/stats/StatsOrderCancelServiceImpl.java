package cn.iocoder.yudao.module.mall.basis.service.stats;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.stats.StatsOrderCancelDO;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.stats.StatsOrderDO;
import cn.iocoder.yudao.module.mall.basis.dal.mysql.stats.StatsOrderCancelMapper;
import cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo.TradeOrderPageReqVO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderDO;
import cn.iocoder.yudao.module.mall.trade.enums.order.TradeOrderCancelTypeEnum;
import cn.iocoder.yudao.module.mall.trade.enums.order.TradeOrderSortEnum;
import cn.iocoder.yudao.module.mall.trade.enums.order.TradeOrderStatusEnum;
import cn.iocoder.yudao.module.mall.trade.service.order.TradeOrderService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertMap;

/**
 * 订单取消统计 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class StatsOrderCancelServiceImpl extends ServiceImpl<StatsOrderCancelMapper, StatsOrderCancelDO> implements StatsOrderCancelService {

    @Resource
    private TradeOrderService orderService;
    @Resource
    private StatsOrderService statsOrderService;
    @Resource
    private RedissonClient redissonClient;

    /**
     * 按日期区间收集统计信息
     * @param startDate
     * @param endDate
     */
    @Override
    public void collectByDate(LocalDateTime startDate, LocalDateTime endDate) {
        if(startDate == null || endDate == null) {
            return;
        }

        LocalDateTime startTime = startDate.toLocalDate().atStartOfDay();
        while(startTime.isBefore(endDate)) {
            collectByDate(startTime);
            startTime = startTime.plusDays(1);
        }
    }

    /**
     * 按下单时间收集订单信息，查询条件有，取消时间，
     * 售后取消的订单除外
     * @param date
     */
    @Override
    public void collectByDate(LocalDateTime date) {
        String lockKey = generateLockKey(date);
        RLock lock = redissonClient.getLock(lockKey);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        int pageSize = 100, pageNo = 1;
        LocalDateTime startTime = date.toLocalDate().atStartOfDay();
        LocalDateTime endTime = startTime.plusDays(1);
        String dateStr = DateUtil.format(date, "yyyy-MM-dd");

        try {
            if(lock.tryLock(10, 100, TimeUnit.SECONDS)) {
                TradeOrderPageReqVO reqVO = new TradeOrderPageReqVO();
                reqVO.setPageSize(pageSize);
                reqVO.setPageNo(pageNo++);
                reqVO.setParentType(0);
                reqVO.setStatus(TradeOrderStatusEnum.CANCELED.getStatus());
                reqVO.setCancelTime(new LocalDateTime[]{ startTime, endTime });
                reqVO.setSortType(TradeOrderSortEnum.CANCEL_TIME_ASC.getVal());
                PageResult<TradeOrderDO> pageResult = orderService.getOrderPage(reqVO);
                log.info("orderCancel collectByDate total: {}, {}", pageResult.getTotal(), dateStr);
                while(CollUtil.isNotEmpty(pageResult.getList())) {
                    handleOrders(startTime, pageResult.getList());
                    reqVO.setPageNo(pageNo++);
                    pageResult = orderService.getOrderPage(reqVO);
                }
            } else {
                log.info("collectByDate running now, ignore...");
            }
        } catch (Exception e) {
            log.error("collectByDate error:", e);
        } finally {
            if(lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();;
            }
        }

        stopWatch.stop();
        log.info("orderCancel collectByDate finish: {}s, {}", stopWatch.getTotalTimeSeconds(), dateStr);
    }

    /**
     * 针对售后而整单取消的可将取消订单金额置为0
     * @param statsDate
     * @param orderDOList
     */
    private void handleOrders(LocalDateTime statsDate, List<TradeOrderDO> orderDOList) {
        if(CollUtil.isEmpty(orderDOList)) {
            return;
        }
        CollUtil.sort(orderDOList, Comparator.comparing(TradeOrderDO::getCancelTime));
        List<Long> orderIdList = orderDOList.stream().map(TradeOrderDO::getId).collect(Collectors.toList());
        List<StatsOrderDO> statOrderList = statsOrderService.getByOrderIds(orderIdList);
        Map<Long, StatsOrderDO> statOrderMap = convertMap(statOrderList, StatsOrderDO::getOrderId);
        List<StatsOrderCancelDO> updates = new ArrayList<>();
        List<StatsOrderCancelDO> inserts = new ArrayList<>();
        List<StatsOrderCancelDO> all = new ArrayList<>();
        for(TradeOrderDO orderDO : orderDOList) {
            StatsOrderDO statsOrder = statOrderMap.get(orderDO.getId());
            if(statsOrder == null) {
                log.info("statsOrder null: {}", orderDO.getId());
                continue;
            }
            StatsOrderCancelDO statsCancelOrder = new StatsOrderCancelDO()
                    .setOrderId(orderDO.getId())
                    .setOrderNo(orderDO.getNo())
                    .setStatsDate(statsDate)
                    .setTotalPrice(orderDO.getOrderPrice())
                    .setCancelTime(orderDO.getCancelTime())
                    .setSupplierId(orderDO.getSupplierId())
                    .setSupplierName(orderDO.getSupplierName())
                    .setUserId(statsOrder.getUserId())
                    .setUserName(statsOrder.getUserName())
                    .setDeptCode(statsOrder.getDeptCode())
                    .setDeptId(statsOrder.getDeptId())
                    .setDeptName(statsOrder.getDeptName())
                    .setCancelType(orderDO.getCancelType())
                    .setCancelReason(orderDO.getCancelReason());
            // 如果是售后退款取消，则将订单取消金额置为0
            if(ObjectUtil.equal(TradeOrderCancelTypeEnum.AFTER_SALE_CLOSE.getType(), orderDO.getCancelType())) {
                statsCancelOrder.setTotalPrice(BigDecimal.ZERO);
            }
            all.add(statsCancelOrder);
        }

        List<StatsOrderCancelDO> persistList = baseMapper.getByOrderId(orderIdList);
        Map<Long, StatsOrderCancelDO> orderMap = convertMap(persistList, StatsOrderCancelDO::getOrderId);
        all.forEach(item -> {
            if(orderMap.containsKey(item.getOrderId())) {
                item.setId(orderMap.get(item.getOrderId()).getId());
                updates.add(item);
            } else {
                inserts.add(item);
            }
        });

        if(CollUtil.isNotEmpty(inserts)) {
            saveBatch(inserts);
        }

        if(CollUtil.isNotEmpty(updates)) {
            updateBatchById(updates);
        }
    }

    /**
     * 针对单个订单收集统计信息
     * @param orderId
     */
    @Override
    public void collectByData(Long orderId) {
        TradeOrderDO orderDO = orderService.getById(orderId);
        if(orderDO == null || ObjectUtil.notEqual(TradeOrderStatusEnum.CANCELED.getStatus(), orderDO.getStatus())) {
            return;
        }

        String lockKey = generateLockKey(orderDO.getCancelTime());
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (lock.tryLock(5, 10, TimeUnit.SECONDS)) {
                handleOrders(orderDO.getCancelTime(), Arrays.asList(orderDO));
            } else {
                log.info("collect-data lock try failed");
            }
        } catch (Exception e) {
            log.error("collectByData error: ", e);
        } finally {
            if(lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private String generateLockKey(LocalDateTime statsDate) {
        return String.format("lock:stats:order-cancel:date:%s", DateUtil.format(statsDate, "yyyyMMdd"));
    }

}
