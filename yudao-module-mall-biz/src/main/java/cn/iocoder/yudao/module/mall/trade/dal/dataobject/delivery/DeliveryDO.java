package cn.iocoder.yudao.module.mall.trade.dal.dataobject.delivery;

import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 物流信息 DO
 *
 * <AUTHOR>
 */
@TableName("trade_delivery")
@KeySequence("trade_delivery_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 是否签收标记，0未签收，1已签收，请忽略，明细状态请参考state字段
     */
    private Integer isCheck;
    /**
     * 出发行政区域编码
     */
    private String fromNumber;
    /**
     * 出发行政区域名字
     */
    private String fromName;
    /**
     * 目的地行政区域编码
     */
    private String toNumber;
    /**
     * 目的地行政区域名字
     */
    private String toName;
    /**
     * 当前行政区域编码
     */
    private String curNumber;
    /**
     * 当前行政区域名字
     */
    private String curName;
    /**
     * 订阅状态，默认为0未订阅，1订阅
     */
    private Integer subscribe;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 供应商编号
     */
    private Long supplierId;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 物流来源
     */
    private String source;
    /**
     * 物流公司编码,一律用小写字母
     */
    private String com;
    /**
     * 物流单号
     */
    private String num;
    /**
     * 物流单当前状态，默认为0在途，1揽收，2疑难，3签收，4退签，5派件，8清关，14拒签等10个基础物流状态，如需要返回高级物流状态，请参考 resultv2 传值
     */
    private Integer state;
    /**
     * 物流公司名称
     */
    private String name;

}
