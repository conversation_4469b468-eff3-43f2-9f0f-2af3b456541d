package cn.iocoder.yudao.module.mall.trade.controller.app.order.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.validation.InEnum;
import cn.iocoder.yudao.module.mall.trade.enums.order.TradeAuditStatusEnum;
import cn.iocoder.yudao.module.mall.trade.enums.order.TradeOrderStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "交易订单分页 Request VO")
@Data
public class AppTradeOrderPageReqVO extends PageParam {

    @Schema(description = "订单状态", example = "1")
    @InEnum(value = TradeOrderStatusEnum.class, message = "订单状态必须是 {value}")
    private Integer status;

    @Schema(description = "审批状态", example = "1")
    @InEnum(value = TradeAuditStatusEnum.class, message = "审批状态必须是 {value}")
    private Integer auditStatus;

    @Schema(description = "是否需要提交采购", example = "1")
    private Boolean needPurchase;

    @Schema(description = "是否线下结算", example = "1")
    private Boolean offlineSettlement;

    @Schema(description = "订单状态", example = "1")
    private List<Integer> statusList;

    private Long userId;

    private Long teamUserId;
    private String teamOrderNo;

    @Schema(description = "支付方式", example = "1")
    private Integer paymentMethod;

    @Schema(description = "订单号", example = "112211212")
    private String orderNo;

    @Schema(description = "父订单号", example = "112211212")
    private String parentNo;

    @Schema(description = "搜索关键字", example = "112211212")
    private String keyword;

}
