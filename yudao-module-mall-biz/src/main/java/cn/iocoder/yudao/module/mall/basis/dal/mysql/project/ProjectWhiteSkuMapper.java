package cn.iocoder.yudao.module.mall.basis.dal.mysql.project;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.mall.basis.controller.admin.project.vo.ProjectWhiteSkuPageReqVO;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.project.ProjectWhiteSkuDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目经费卡SKU白名单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProjectWhiteSkuMapper extends BaseMapperX<ProjectWhiteSkuDO> {

    default PageResult<ProjectWhiteSkuDO> selectPage(ProjectWhiteSkuPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProjectWhiteSkuDO>()
                .eqIfPresent(ProjectWhiteSkuDO::getSkuId, reqVO.getSkuId())
                .likeIfPresent(ProjectWhiteSkuDO::getSkuName, reqVO.getSkuName())
                .eqIfPresent(ProjectWhiteSkuDO::getSkuInnerId, reqVO.getSkuInnerId())
                .eqIfPresent(ProjectWhiteSkuDO::getSupplierId, reqVO.getSupplierId())
                .orderByDesc(ProjectWhiteSkuDO::getId));
    }

    void deleteById2(Long id);

    void deleteBatchIds2(@Param("ids") List<Long> ids);

    void deleteBySkuId(Long skuId);

}
