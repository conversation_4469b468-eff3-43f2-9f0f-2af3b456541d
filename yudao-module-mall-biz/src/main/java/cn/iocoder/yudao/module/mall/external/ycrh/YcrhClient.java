package cn.iocoder.yudao.module.mall.external.ycrh;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.map.MapUtil;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.integration.YcrhConfigDO;
import cn.iocoder.yudao.module.mall.basis.service.integration.YcrhConfigService;
import cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.mall.external.ycrh.dto.*;
import cn.iocoder.yudao.module.mall.external.ycrh.enums.YcrhMethodEnum;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 业财融合接口客户端封装
 */
@Service
@Slf4j
public class YcrhClient {

    @Resource
    private YcrhConfigService ycrhConfigService;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private HttpCommonClient httpCommonClient;

    /**
     * 获取租户的业财融合对接配置信息
     * @return 业财融合配置信息
     */
    private YcrhConfigDO getConfig() {
        YcrhConfigDO ycrhConfig = ycrhConfigService.getYcrhConfig();
        if(ycrhConfig != null) {
            if(StringUtils.isBlank(ycrhConfig.getSettlementHost())) {
                throw new ServiceException(ErrorCodeConstants.YCRH_CONFIG_NULL);
            }
            return ycrhConfig;
        } else {
            log.error("租户业财融合配置为空");
            throw new ServiceException(ErrorCodeConstants.YCRH_CONFIG_FAIL);
        }
    }

    private JSONObject assembleParam(YcrhMethodEnum ycrhMethodEnum, Object param, YcrhConfigDO ycrhConfig) {
        Map<String, Object> data = objectMapper.convertValue(param, new TypeReference<Map<String, Object>>() {});
        data = MapUtil.map(data, (k, v) -> v == null ? null : v);
        YcrhReqDTO dto = new YcrhReqDTO();
        dto.getData().putAll(data);
        dto.setMethod(ycrhMethodEnum.getMethodName());
        dto.setAppid(ycrhConfig.getAppKey());
        Map<String, Object> paramMap = objectMapper.convertValue(dto, new TypeReference<Map<String, Object>>() {});
        String sign = SignUtil.createSign(paramMap, ycrhConfig.getAppSecret());
        JSONObject obj = dto.toJsonObj();
        obj.put("sign", sign);
        return obj;
    }


    /**
     * 查询员工信息
     *
     * @param ygNo 员工工号，如06601
     * @return 员工信息
     */
    public YgInfoRespDTO getYgInfo(String ygNo) {
        YcrhConfigDO ycrhConfig = getConfig();
        Map<String, Object> data = new Tree<>();
        data.put("ygbh", ygNo);
        YcrhMethodEnum ycrhMethodEnum = YcrhMethodEnum.GET_YG_INFO;
        JSONObject param = assembleParam(ycrhMethodEnum, data, ycrhConfig);
        YcrhResp<YgInfoRespDTO> resp = httpCommonClient.sendRequest(ycrhConfig.getSettlementHost(), ycrhMethodEnum, param, YgInfoRespDTO.class);
        return resp.getData();
    }

    /**
     * 检索员工信息
     *
     * @param reqDTO 检索条件
     * @return 员工信息列表
     */
    public List<YgInfoRespDTO> searchYgInfo(SearchYgInfoReqDTO reqDTO) {
        YcrhConfigDO ycrhConfig = getConfig();
        TypeReference<YcrhResp<List<YgInfoRespDTO>>> typeReference = new TypeReference<YcrhResp<List<YgInfoRespDTO>>>() {};
        YcrhMethodEnum ycrhMethodEnum = YcrhMethodEnum.SEARCH_YG_INFO;
        JSONObject param = assembleParam(ycrhMethodEnum, reqDTO, ycrhConfig);
        YcrhResp<List<YgInfoRespDTO>> resp = httpCommonClient.sendRequest(ycrhConfig.getSettlementHost(), ycrhMethodEnum, param, typeReference, false);
        return resp.getData();
    }

    /**
     * 查询负责人项目信息
     *
     * @param reqDTO 查询条件
     * @return 负责人项目信息列表
     */
    public List<UserProjectRespDTO> getUserProjectInfo(UserProjectReqDTO reqDTO) {
        YcrhConfigDO ycrhConfig = getConfig();
        TypeReference<YcrhResp<List<UserProjectRespDTO>>> typeReference = new TypeReference<YcrhResp<List<UserProjectRespDTO>>>() {};
        YcrhMethodEnum ycrhMethodEnum = YcrhMethodEnum.GET_USER_PROJECT_INFO;
        JSONObject param = assembleParam(ycrhMethodEnum, reqDTO, ycrhConfig);
        YcrhResp<List<UserProjectRespDTO>> resp = httpCommonClient.sendRequest(ycrhConfig.getSettlementHost(), ycrhMethodEnum, param, typeReference, false);
        return resp.getData();
    }

    /**
     * 查询项目信息
     *
     * @param reqDTO 项目信息
     * @return 项目信息列表
     */
    public List<ProjectInfoRespDTO> getProjectInfo(ProjectInfoReqDTO reqDTO) {
        YcrhConfigDO ycrhConfig = getConfig();
        TypeReference<YcrhResp<List<ProjectInfoRespDTO>>> typeReference = new TypeReference<YcrhResp<List<ProjectInfoRespDTO>>>() {};
        YcrhMethodEnum ycrhMethodEnum = YcrhMethodEnum.GET_PROJECT_INFO;
        JSONObject param = assembleParam(ycrhMethodEnum, reqDTO, ycrhConfig);
        YcrhResp<List<ProjectInfoRespDTO>> resp = httpCommonClient.sendRequest(ycrhConfig.getSettlementHost(), ycrhMethodEnum, param, typeReference, false);
        return resp.getData();
    }

    public YcrhResp<Void> checkBalance(CheckBalanceReqDTO reqDTO) {
        YcrhConfigDO ycrhConfig = getConfig();
        YcrhMethodEnum ycrhMethodEnum = YcrhMethodEnum.CHECK_BALANCE;
        JSONObject param = assembleParam(ycrhMethodEnum, reqDTO, ycrhConfig);
        return httpCommonClient.sendRequest(ycrhConfig.getSettlementHost(), ycrhMethodEnum, param, Void.class, true);
    }

    public BigDecimal getBalance(BalanceQueryReqDTO reqDTO) {
        YcrhConfigDO ycrhConfig = getConfig();
        YcrhMethodEnum ycrhMethodEnum = YcrhMethodEnum.GET_BALANCE;
        JSONObject param = assembleParam(ycrhMethodEnum, reqDTO, ycrhConfig);
        YcrhResp<BigDecimal> resp = httpCommonClient.sendRequest(ycrhConfig.getSettlementHost(), ycrhMethodEnum, param, BigDecimal.class, false);
        return resp.getData();
    }

    public List<BalanceRespDTO> getBalanceBatch(BatchBalanceQueryReqDTO reqDTO) {
        YcrhConfigDO ycrhConfig = getConfig();
        TypeReference<YcrhResp<List<BalanceRespDTO>>> typeReference = new TypeReference<YcrhResp<List<BalanceRespDTO>>>() {};
        YcrhMethodEnum ycrhMethodEnum = YcrhMethodEnum.GET_BALANCE_BATCH;
        JSONObject param = assembleParam(ycrhMethodEnum, reqDTO, ycrhConfig);
        YcrhResp<List<BalanceRespDTO>> resp = httpCommonClient.sendRequest(ycrhConfig.getSettlementHost(), ycrhMethodEnum, param, typeReference, false);
        return resp.getData();
    }

    public BalanceInfoRespDTO getBalanceInfo(BalanceInfoQueryReqDTO reqDTO) {
        YcrhConfigDO ycrhConfig = getConfig();
        YcrhMethodEnum ycrhMethodEnum = YcrhMethodEnum.GET_BALANCE_INFO;
        JSONObject param = assembleParam(ycrhMethodEnum, reqDTO, ycrhConfig);
        YcrhResp<BalanceInfoRespDTO> resp = httpCommonClient.sendRequest(ycrhConfig.getSettlementHost(), ycrhMethodEnum, param, BalanceInfoRespDTO.class, false);
        return resp.getData();
    }

    public List<BalanceInfoRespDTO> getBalanceInfoBatch(BatchBalanceInfoQueryReqDTO reqDTO) {
        YcrhConfigDO ycrhConfig = getConfig();
        TypeReference<YcrhResp<List<BalanceInfoRespDTO>>> typeReference = new TypeReference<YcrhResp<List<BalanceInfoRespDTO>>>() {};
        YcrhMethodEnum ycrhMethodEnum = YcrhMethodEnum.GET_BALANCE_INFO_BATCH;
        JSONObject param = assembleParam(ycrhMethodEnum, reqDTO, ycrhConfig);
        YcrhResp<List<BalanceInfoRespDTO>> resp = httpCommonClient.sendRequest(ycrhConfig.getSettlementHost(), ycrhMethodEnum, param, typeReference, false);
        return resp.getData();
    }

    public PushOrderRespDTO pushOrder(PushOrderReqDTO reqDTO) {
        YcrhConfigDO ycrhConfig = getConfig();
        YcrhMethodEnum ycrhMethodEnum = YcrhMethodEnum.PUSH_ORDER;
        JSONObject param = assembleParam(ycrhMethodEnum, reqDTO, ycrhConfig);
        YcrhResp<PushOrderRespDTO> resp = httpCommonClient.sendRequest(ycrhConfig.getSettlementHost(), ycrhMethodEnum, param, PushOrderRespDTO.class, true);
        return resp.getData();
    }

    public YcrhResp<Void> splitOrder(SplitOrderReqDTO reqDTO) {
        YcrhConfigDO ycrhConfig = getConfig();
        YcrhMethodEnum ycrhMethodEnum = YcrhMethodEnum.SPLIT_ORDER;
        JSONObject param = assembleParam(ycrhMethodEnum, reqDTO, ycrhConfig);
        return httpCommonClient.sendRequest(ycrhConfig.getSettlementHost(), ycrhMethodEnum, param, Void.class, true);
    }

    public YcrhResp<Void> cancelOrder(OrderCancelReqDTO reqDTO) {
        YcrhConfigDO ycrhConfig = getConfig();
        YcrhMethodEnum ycrhMethodEnum = YcrhMethodEnum.CANCEL_ORDER;
        JSONObject param = assembleParam(ycrhMethodEnum, reqDTO, ycrhConfig);
        return httpCommonClient.sendRequest(ycrhConfig.getSettlementHost(), ycrhMethodEnum, param, Void.class, true);
    }

    public OrderQueryRespDTO queryOrder(OrderQueryReqDTO reqDTO) {
        YcrhConfigDO ycrhConfig = getConfig();
        YcrhMethodEnum ycrhMethodEnum = YcrhMethodEnum.QUERY_ORDER;
        JSONObject param = assembleParam(ycrhMethodEnum, reqDTO, ycrhConfig);
        YcrhResp<OrderQueryRespDTO> resp = httpCommonClient.sendRequest(ycrhConfig.getSettlementHost(), ycrhMethodEnum, param, OrderQueryRespDTO.class, false);
        return resp.getData();
    }

    public OrderBatchQueryRespDTO queryOrderList(OrderListQueryReqDTO reqDTO) {
        List<String> orderNoList = reqDTO.getOrderNoList();
        OrderBatchQueryRespDTO orderBatchQueryRespDTO = new OrderBatchQueryRespDTO();
        orderBatchQueryRespDTO.setFoundOrderList(new ArrayList<>());
        orderBatchQueryRespDTO.setNotFoundOrderList(new ArrayList<>());

        int batchSize = 100;
        int totalSize = reqDTO.getOrderNoList().size();
        for (int i = 0; i < totalSize; i += batchSize) {
            // 获取当前批次的订单号列表
            List<String> subOrderNoList = orderNoList.subList(i, Math.min(i + batchSize, totalSize));
            reqDTO.setOrderNoList(subOrderNoList);
            YcrhConfigDO ycrhConfig = getConfig();
            YcrhMethodEnum ycrhMethodEnum = YcrhMethodEnum.QUERY_ORDER_LIST;
            JSONObject param = assembleParam(ycrhMethodEnum, reqDTO, ycrhConfig);
            TypeReference<YcrhResp<List<OrderQueryRespDTO>>> typeReference = new TypeReference<YcrhResp<List<OrderQueryRespDTO>>>() {};
            YcrhResp<List<OrderQueryRespDTO>> resp = httpCommonClient.sendRequest(ycrhConfig.getSettlementHost(), ycrhMethodEnum, param, typeReference, true);
            orderBatchQueryRespDTO.getFoundOrderList().addAll(resp.getData());
            if(resp.getResultMsg().startsWith("订单号[") && resp.getResultMsg().endsWith("]不存在")){
                String ordersPart = resp.getResultMsg().substring(resp.getResultMsg().indexOf("[") + 1, resp.getResultMsg().indexOf("]"));
                String[] orderArray = ordersPart.split(",");
                List<String> orderList = new ArrayList<>(Arrays.asList(orderArray));
                List<OrderQueryRespDTO> notFoundOrderList = new ArrayList<>();
                for (String orderNo : reqDTO.getOrderNoList()) {
                    if(orderList.contains(orderNo)){
                        OrderQueryRespDTO orderQueryRespDTO = new OrderQueryRespDTO().setOrderNo(orderNo);
                        notFoundOrderList.add(orderQueryRespDTO);
                    }
                }
                if(!notFoundOrderList.isEmpty()) {
                    orderBatchQueryRespDTO.getNotFoundOrderList().addAll(notFoundOrderList);
                }
            }
        }
        log.info("查询业财订单结果：{}", orderBatchQueryRespDTO);
        return orderBatchQueryRespDTO;
    }

    public YcrhResp<Void> pushOrderAssetInfo(OrderAssetsInfoReqDTO reqDTO) {
        YcrhConfigDO ycrhConfig = getConfig();
        YcrhMethodEnum ycrhMethodEnum = YcrhMethodEnum.PUSH_ORDER_ASSET_INFO;
        JSONObject param = assembleParam(ycrhMethodEnum, reqDTO, ycrhConfig);
        return httpCommonClient.sendRequest(ycrhConfig.getSettlementHost(), ycrhMethodEnum, param, Void.class, true);
    }

    public YcrhResp<Void> uploadVoucherFile(UploadVoucherFileReqDTO reqDTO) {
        return uploadVoucherFile(reqDTO, true);
    }

    public YcrhResp<Void> uploadVoucherFile(UploadVoucherFileReqDTO reqDTO, boolean checkFail) {
        YcrhConfigDO ycrhConfig = getConfig();
        YcrhMethodEnum ycrhMethodEnum = YcrhMethodEnum.UPLOAD_VOUCHER_FILE;
        JSONObject param = assembleParam(ycrhMethodEnum, reqDTO, ycrhConfig);
        String url = ycrhConfig.getSettlementHost();
        url = url.replace("apis", "voucherUpload");
        return httpCommonClient.sendRequest(url, ycrhMethodEnum, param, Void.class, checkFail);
    }

    public YcrhResp<Void> checkInvoice(InvoiceCheckReqDTO reqDTO) {
        YcrhConfigDO ycrhConfig = getConfig();
        YcrhMethodEnum ycrhMethodEnum = YcrhMethodEnum.INVOICE_CHECK;
        JSONObject param = assembleParam(ycrhMethodEnum, reqDTO, ycrhConfig);
        return httpCommonClient.sendRequest(ycrhConfig.getSettlementHost(), ycrhMethodEnum, param, Void.class, true);
    }

    public YcrhResp<Void> orderAfterSale(OrderAfterSaleReqDTO reqDTO) {
        YcrhConfigDO ycrhConfig = getConfig();
        YcrhMethodEnum ycrhMethodEnum = YcrhMethodEnum.ORDER_AFTER_SALE;
        JSONObject param = assembleParam(ycrhMethodEnum, reqDTO, ycrhConfig);
        return httpCommonClient.sendRequest(ycrhConfig.getSettlementHost(), ycrhMethodEnum, param, Void.class, true);
    }

    public String pushBill(PushBillReqDTO reqDTO) {
        YcrhConfigDO ycrhConfig = getConfig();
        YcrhMethodEnum ycrhMethodEnum = YcrhMethodEnum.PUSH_BILL;
        JSONObject param = assembleParam(ycrhMethodEnum, reqDTO, ycrhConfig);
        YcrhResp<String> resp = httpCommonClient.sendRequest(ycrhConfig.getSettlementHost(), ycrhMethodEnum, param, String.class, true);
        return resp.getData();
    }

    public YcrhResp<Void> cancelBill(BillCancelReqDTO reqDTO, boolean checkFail) {
        YcrhConfigDO ycrhConfig = getConfig();
        YcrhMethodEnum ycrhMethodEnum = YcrhMethodEnum.CANCEL_BILL;
        JSONObject param = assembleParam(ycrhMethodEnum, reqDTO, ycrhConfig);
        return httpCommonClient.sendRequest(ycrhConfig.getSettlementHost(), ycrhMethodEnum, param, Void.class, checkFail);
    }

    public BillInfoRespDTO getBill(BillQueryReqDTO reqDTO) {
        YcrhConfigDO ycrhConfig = getConfig();
        YcrhMethodEnum ycrhMethodEnum = YcrhMethodEnum.GET_BILL;
        JSONObject param = assembleParam(ycrhMethodEnum, reqDTO, ycrhConfig);
        YcrhResp<BillInfoRespDTO> resp = httpCommonClient.sendRequest(ycrhConfig.getSettlementHost(), ycrhMethodEnum, param, BillInfoRespDTO.class, false);
        return resp.getData();
    }

    public List<BillVoucherRespDTO> getBillVoucherDetail(BillVoucherReqDTO reqDTO) {
        YcrhConfigDO ycrhConfig = getConfig();
        TypeReference<YcrhResp<List<BillVoucherRespDTO>>> typeReference = new TypeReference<YcrhResp<List<BillVoucherRespDTO>>>() {};
        YcrhMethodEnum ycrhMethodEnum = YcrhMethodEnum.BILL_VOUCHER_DETAIL;
        JSONObject param = assembleParam(ycrhMethodEnum, reqDTO, ycrhConfig);
        YcrhResp<List<BillVoucherRespDTO>> resp = httpCommonClient.sendRequest(ycrhConfig.getSettlementHost(), ycrhMethodEnum, param, typeReference, false);
        return resp.getData();
    }

    public YcrhConfigDO getYcrhConfig() {
        return ycrhConfigService.getYcrhConfig();
    }

    public boolean isYcrhConfigValid() {
        YcrhConfigDO ycrhConfig = ycrhConfigService.getYcrhConfig();
        return ycrhConfig != null && ycrhConfig.isConfigValid();
    }

    public boolean isYcrhConfigValidAndPPRelated() {
        YcrhConfigDO ycrhConfig = ycrhConfigService.getYcrhConfig();
        return ycrhConfig != null && ycrhConfig.isConfigValid() && ycrhConfig.getPpRelated();
    }

    public boolean isYcrhConfigValidAndFormEconomySwitchOn() {
        YcrhConfigDO ycrhConfig = ycrhConfigService.getYcrhConfig();
        return ycrhConfig != null && ycrhConfig.isConfigValid() && ycrhConfig.getIsFormEconomyClassOn();
    }

    public boolean isYcrhConfigValidAndEconomicMatchOn() {
        YcrhConfigDO ycrhConfig = ycrhConfigService.getYcrhConfig();
        return ycrhConfig != null && ycrhConfig.isConfigValid() && ycrhConfig.getEconomicMatchSwitch() != null && ycrhConfig.getEconomicMatchSwitch();
    }

    public YcrhResp<Object> commonRequest(YcrhMethodEnum ycrhMethodEnum, Map<String, Object> param) {
        YcrhConfigDO ycrhConfig = getConfig();
        JSONObject paramJson = assembleParam(ycrhMethodEnum, param, ycrhConfig);
        return httpCommonClient.sendRequest(ycrhConfig.getSettlementHost(), ycrhMethodEnum, paramJson, Object.class, false);
    }

}
