package cn.iocoder.yudao.module.mall.trade.dal.dataobject.order;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.mybatis.core.type.StringListTypeHandler;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.aftersale.TradeAfterSaleDO;
import cn.iocoder.yudao.module.mall.trade.enums.order.TradeOrderItemAfterSaleStatusEnum;
import cn.iocoder.yudao.module.mall.trade.enums.order.TradeOrderStatusEnum;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 交易订单项 DO
 *
 * <AUTHOR>
 */
@TableName(value = "trade_order_item", autoResultMap = true)
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class TradeOrderItemDO extends BaseDO {

    // ========== 订单项基本信息 ==========
    /**
     * 编号，使用雪花ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 用户编号
     *
     * 关联 MemberUserDO 的 id 编号
     */
    private Long userId;
    /**
     * 订单编号
     *
     * 关联 {@link TradeOrderDO#getId()}
     */
    private Long orderId;

    // ========== 商品基本信息; 冗余较多字段，减少关联查询 ==========

    /**
     * 商品 SKU 编号
     *
     * 关联 ProductSkuDO 的 id 编号
     */
    private Long skuId;

    /**
     * 商品三方 SKU 编号
     *
     * 关联 ProductSkuDO 的 skuInnerId 编号
     */
    private String skuInnerId;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 供应商类型
     */
    private Integer supplierType;

    /**
     * 商品图片
     */
    private String picUrl;

    /**
     * 购买数量
     */
    private Integer count;

    /**
     * 是否评论
     *
     * false - 未评论
     * true - 已评论
     */
    private Boolean commented;

    // ========== 价格 + 支付基本信息 ==========

    /**
     * 商品价格
     *
     */
    private BigDecimal skuPrice;

    /**
     * 商品价格
     *
     */
    private BigDecimal skuTotalPrice;

    // ========== 营销基本信息 ==========

    // TODO 芋艿：在捉摸一下

    // ========== 售后基本信息 ==========
    /**
     * 售后状态
     *
     * 枚举 {@link TradeOrderItemAfterSaleStatusEnum}
     *
     * @see TradeAfterSaleDO
     */
    private Integer afterSaleStatus;

    /**
     * 售后备注
     */
    private String afterSaleMemo;

    /**
     * 售后数量
     */
    private Integer afterSaleCount;

    /**
     * 商品分类code
     */
    private String categoryCode;

    /**
     * 商品分类名称
     */
    private String categoryName;

    /**
     * 固资建档状态：0-建档待处理, 1-建档已提交，2-无须建档 3-建档中 4-建档成功 5-建档失败
     * {@link cn.iocoder.yudao.module.mall.trade.enums.order.AssetStatusEnum}
     */
    private Integer assetStatus;

    /**
     * 是否为固定资产
     */
    private Boolean isAsset;

    /**
     * 验收状态
     * {@link cn.iocoder.yudao.module.mall.trade.enums.order.AcceptStatusEnum}
     */
    private Integer acceptStatus;

    /**
     * 商品标签
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> skuTags;

    /**
     * 获取剩余可用的售后数量
     * @return
     */
    @JsonIgnore
    public Integer getAfterSaleAvailableCount() {
        if(afterSaleCount != null) {
            int c1 = count - afterSaleCount;
            return c1 > 0 ? c1 : 0;
        }

        return count;
    }

    @JsonIgnore
    public boolean isGift() {
        return skuPrice != null && skuPrice.compareTo(BigDecimal.ZERO) <= 0;
    }

    /**
     * 是否可以售后
     * @param orderDO
     * @return
     */
    public boolean canAfterSale(TradeOrderDO orderDO) {
        boolean canAfterSaleTime = orderDO.getFinishTime() == null || orderDO.getFinishTime().plusDays(30).isAfter(LocalDateTime.now());
        boolean canAfterSaleStatus = !TradeOrderStatusEnum.CANCELED.getStatus().equals(orderDO.getStatus()) && !TradeOrderStatusEnum.HAS_SUBMIT.getStatus().equals(orderDO.getStatus());
        boolean canAfterSaleCount = this.getAfterSaleAvailableCount() > 0;

        return canAfterSaleTime && canAfterSaleStatus && canAfterSaleCount && !isGift();
    }


}

