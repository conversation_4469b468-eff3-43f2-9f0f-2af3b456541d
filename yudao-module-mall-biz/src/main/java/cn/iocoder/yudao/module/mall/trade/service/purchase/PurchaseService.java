package cn.iocoder.yudao.module.mall.trade.service.purchase;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.external.bpm.dto.BpmAuditResultDTO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.order.vo.TradeAfterSaleReqVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.purchase.vo.PurchasePageReqVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.purchase.vo.PurchaseCartDetailRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.purchase.vo.PurchaseCartItemUpdateSelectedReqVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.purchase.vo.PurchaseSubmitReqVO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.aftersale.TradeAfterSaleDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.purchase.PurchaseDO;
import cn.iocoder.yudao.module.mall.trade.enums.purchase.TradePurchaseStatusEnum;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 采购 Service 接口
 *
 * <AUTHOR>
 */
public interface PurchaseService extends IService<PurchaseDO> {

    /**
     * 获得采购列表
     *
     * @param ids 编号
     * @return 采购列表
     */
    List<PurchaseDO> getPurchaseList(Collection<Long> ids);

    /**
     * 获得采购单详情
     *
     * @param id 编号
     * @return 采购
     */
    PurchaseDO getPurchaseById(Long id);

    /**
     * 获得采购单详情
     *
     * @param id 编号
     * @param userId 用户ID
     * @return 采购
     */
    PurchaseDO getPurchaseByIdAndUser(Long id, Long userId);

    /**
     * 获得采购分页
     *
     * @param pageReqVO 分页查询
     * @return 采购分页
     */
    PageResult<PurchaseDO> getPurchasePage(PurchasePageReqVO pageReqVO);

    /**
     * 获取采购购物车详情
     * @param userId
     * @return
     */
    PurchaseCartDetailRespVO getPurchaseCartDetail(Long userId);

    /**
     * 更新采购购物车选中状态
     * @param userId
     * @param updateSelectedReqVO
     * @return
     */
    void updateCartItemSelected(Long userId, PurchaseCartItemUpdateSelectedReqVO updateSelectedReqVO);

    Boolean splitOrder4Purchase(Long parentOrderId);

    /**
     * 根据订单号查询采购单
     * @param orderId
     * @return
     */
    PurchaseDO getPurchaseByOrder(Long orderId);

    /**
     * 修复采购单信息
     * @param orderNo
     * @return
     */
    PurchaseDO fixPurchaseOrder(String orderNo);

    /**
     * 根据审批流单据号查询采购单
     * @param bpmNo
     * @return
     */
    PurchaseDO getPurchaseByBpmNo(String bpmNo);

    Map<Long, PurchaseDO> getPurchaseMap(List<Long> orderIds);

    /**
     * 取消订单
     * @param order 订单
     * @param needRetry 是否需要重试
     */
    void cancelOrder(TradeOrderDO order, boolean needRetry);

    /**
     * 取消采购单
     * @param purchaseId 采购单编号
     * @param status 状态 {@link TradePurchaseStatusEnum}，不能为 0
     */
    void cancelPurchase(Long purchaseId, TradePurchaseStatusEnum status);

    /**
     * 补偿处理采购单审批状态
     */
    void handlePurchaseAuditStatus();

    /**
     * 补偿处理推送订单到业财融合
     * @param orderId
     */
    void pushOrder2YcrhManual(Long orderId);

    void syncOrderInYcrhManual(String orderNo);

    void refreshOrderInYcrh(String orderNo);

    void cancelOrderInYcrhManual(String orderNo, String reason);

    /**
     * 同步售后信息到业财融合
     * @param afterSale
     * @return
     */
    boolean pushOrderAfterSale2Ycrh(TradeAfterSaleDO afterSale);

    /**
     * 业财订单状态检查并同步，处理补推及取消等场景
     * @param beginTime
     * @param endTime
     * @param isAsync
     */
    void SyncAndHandleOrderYcrhStatus(LocalDateTime beginTime, LocalDateTime endTime, boolean isAsync);

    /**
     * 强制同步订单消息，如果订单存在则取消后补推，然后同步售后状态
     * @param orderNo
     */
    void forceSyncOrder4Ycrh(String orderNo);

    /**
     * 售后状态同步至业财
     * @param tradeAfterSaleReqVO
     */
    void afterSaleOrder4Ycrh(TradeAfterSaleReqVO tradeAfterSaleReqVO);

    /**
     * 更新并处理采购单的审批状态
     * @param purchaseDO
     * @param bpmAuditResultDTO
     */
    void updatePurchaseAuditStatus(PurchaseDO purchaseDO, BpmAuditResultDTO bpmAuditResultDTO);

    /**
     * 按采购单摘取审批流状态
     * @param purchaseId
     */
    void pullPurchaseAuditStatus(Long purchaseId);

}
