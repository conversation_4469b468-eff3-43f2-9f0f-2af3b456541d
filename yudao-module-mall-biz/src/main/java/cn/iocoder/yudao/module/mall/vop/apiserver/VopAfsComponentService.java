package cn.iocoder.yudao.module.mall.vop.apiserver;

import cn.iocoder.yudao.module.mall.vop.common.CommonRequestHandler;
import com.alibaba.fastjson.JSON;
import com.jd.open.api.sdk.request.vopzj.VopAfsGetComponentUrlRequest;
import com.jd.open.api.sdk.response.vopzj.VopAfsGetComponentUrlResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description 组件API
 * <AUTHOR>
 * @Date 2023/6/13 16:15
 */

@Component
public class VopAfsComponentService {

    @Autowired
    CommonRequestHandler commonRequestHandler;

    /**
     * 通过该接口获取各H5页面组件的URL地址，减少前后端开发量，提高对接效率，当前已支持售后组件、发票组件、客服组件、订单物流跟踪组件、评价组件，其它组件正在开发中。
     * 相关对接文档可移步至该文档目录下查阅：https://vop.jd.com/doc/guide?id=cac051b4-dbf6-4374-a3d1-64a62833857d
     *
     * @param request
     * @return
     */
    public VopAfsGetComponentUrlResponse getComponentUrl(VopAfsGetComponentUrlRequest request) {
        return (VopAfsGetComponentUrlResponse) commonRequestHandler.executeHandler(request);
    }

}
