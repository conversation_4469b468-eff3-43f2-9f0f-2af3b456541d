package cn.iocoder.yudao.module.mall.trade.convert.aftersale;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.external.open.controller.app.vo.trade.AppSupplierAfterSaleVO;
import cn.iocoder.yudao.module.mall.member.api.user.dto.MemberUserRespDTO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.aftersale.vo.TradeAfterSaleRespPageItemVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.aftersale.vo.TradeAfterSaleRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.aftersale.vo.log.TradeAfterSaleLogRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.admin.base.member.user.MemberUserRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.aftersale.vo.AppTradeAfterSaleCreateReqVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.aftersale.vo.AppTradeAfterSaleRespVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.aftersale.vo.AppTradeOrderItemRespVO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.aftersale.TradeAfterSaleDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.aftersale.TradeAfterSaleLogDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderItemDO;
import cn.iocoder.yudao.module.mall.trade.framework.order.config.TradeOrderProperties;
import cn.iocoder.yudao.module.pay.api.refund.dto.PayRefundCreateReqDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertMap;

@Mapper
public interface  TradeAfterSaleConvert {

    TradeAfterSaleConvert INSTANCE = Mappers.getMapper(TradeAfterSaleConvert.class);

    @Mappings({
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "createTime", ignore = true),
            @Mapping(target = "updateTime", ignore = true),
            @Mapping(target = "creator", ignore = true),
            @Mapping(target = "updater", ignore = true),
            @Mapping(source = "createReqVO.count", target = "count"),
    })
    TradeAfterSaleDO convert(AppTradeAfterSaleCreateReqVO createReqVO, TradeOrderItemDO tradeOrderItem);

    @Mappings({
            @Mapping(source = "afterSale.orderNo", target = "merchantOrderId"),
            @Mapping(source = "afterSale.id", target = "merchantRefundId"),
            @Mapping(source = "afterSale.applyReason", target = "reason")
    })
    PayRefundCreateReqDTO convert(String userIp, TradeAfterSaleDO afterSale,
                                  TradeOrderProperties orderProperties);

    MemberUserRespVO convert(MemberUserRespDTO bean);

    AppTradeAfterSaleRespVO convert(TradeAfterSaleDO bean);

    TradeAfterSaleRespVO convert02(TradeAfterSaleDO bean);

    AppSupplierAfterSaleVO convert03(TradeAfterSaleDO bean);

    AppTradeOrderItemRespVO convert04(TradeOrderItemDO bean, String supplierName);

    PageResult<TradeAfterSaleRespPageItemVO> convertPage(PageResult<TradeAfterSaleDO> page);

    default PageResult<TradeAfterSaleRespPageItemVO> convertPage(PageResult<TradeAfterSaleDO> pageResult, List<TradeOrderDO> orderDOList) {
        PageResult<TradeAfterSaleRespPageItemVO> pageVOResult = convertPage(pageResult);
        Map<Long, TradeOrderDO> orderMap = convertMap(orderDOList, TradeOrderDO::getId);
        for (int i = 0; i < pageResult.getList().size(); i++) {
            TradeAfterSaleRespPageItemVO afterSaleVO = pageVOResult.getList().get(i);
            if(orderMap.containsKey(afterSaleVO.getOrderId())) {
                afterSaleVO.setPayOrderId(orderMap.get(afterSaleVO.getOrderId()).getPayOrderId());
            }
        }
        return pageVOResult;
    };

    List<TradeAfterSaleLogRespVO> convertList05(List<TradeAfterSaleLogDO> logList);

}
