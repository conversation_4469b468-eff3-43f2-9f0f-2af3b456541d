package cn.iocoder.yudao.module.mall.mq.consumer.vop;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.mq.message.vop.VopMessage;
import cn.iocoder.yudao.module.mall.vop.service.VopMessageHandleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.function.Consumer;

/**
 * 针对 VOP订单类消息 的消费者
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class VopOrderMsgConsumer implements Consumer<VopMessage> {

    @Resource
    private VopMessageHandleService vopMessageHandleService;

    @Override
    public void accept(VopMessage message) {
        log.info("[accept][消息内容-VOP订单类消息(id数量:{}),租户:{}]", CollUtil.size(message.getIds()), TenantContextHolder.getTenantId());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            vopMessageHandleService.handleMqMessage(message);
        } catch(Exception e) {
            log.error("VOP订单类消息处理失败:", e);
        }
        stopWatch.stop();
        log.info("[accept][消息内容-VOP订单类消息处理完成，耗时：{}秒", stopWatch.getTotalTimeSeconds());
    }
}
