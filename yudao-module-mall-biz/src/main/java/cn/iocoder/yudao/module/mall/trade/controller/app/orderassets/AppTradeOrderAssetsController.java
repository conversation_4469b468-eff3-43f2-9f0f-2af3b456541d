package cn.iocoder.yudao.module.mall.trade.controller.app.orderassets;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.yudao.module.mall.trade.controller.app.orderassets.vo.AppTradeOrderAssetsDetailVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.orderassets.vo.AppTradeOrderAssetsUpdateReqVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.orderassets.vo.AppTradeOrderItemAssetsPageReqVO;
import cn.iocoder.yudao.module.mall.trade.controller.app.orderassets.vo.AppTradeOrderItemAssetsRespVO;
import cn.iocoder.yudao.module.mall.trade.convert.order.TradeOrderAssetsConvert;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderAssetsDetailDO;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.order.TradeOrderItemAssetsDO;
import cn.iocoder.yudao.module.mall.trade.service.order.TradeOrderAssetsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "管理后台 - 订单明细固资信息")
@RestController
@RequestMapping("/trade/order-assets")
@Validated
public class AppTradeOrderAssetsController {

    @Resource
    private TradeOrderAssetsService orderItemAssetsService;

    @GetMapping("/get")
    @Operation(summary = "获得订单明细固资信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthenticated
    public CommonResult<AppTradeOrderItemAssetsRespVO> getOrderItemAssets(@RequestParam("id") Long id) {
        TradeOrderItemAssetsDO orderItemAssets = orderItemAssetsService.getOrderItemAssets(getLoginUserId(), id);
        List<TradeOrderAssetsDetailDO> detailList = orderItemAssetsService.getOrderAssetsDetailList(orderItemAssets.getOrderItemId());
        List<AppTradeOrderAssetsDetailVO> detailVOList = TradeOrderAssetsConvert.INSTANCE.convertList2Detail4App(detailList);

        return success(TradeOrderAssetsConvert.INSTANCE.convert2App(orderItemAssets, detailVOList));
    }

    @GetMapping("/get-by-item")
    @Operation(summary = "获得订单明细固资信息")
    @Parameter(name = "orderItemId", description = "订单明细ID", required = true, example = "1024")
    @PreAuthenticated
    public CommonResult<AppTradeOrderItemAssetsRespVO> getOrderItemAssetsByItem(@RequestParam("orderItemId") Long orderItemId) {
        TradeOrderItemAssetsDO orderItemAssets = orderItemAssetsService.getOrderItemAssetsByItem(getLoginUserId(), orderItemId);
        return success(TradeOrderAssetsConvert.INSTANCE.convert2App(orderItemAssets));
    }

    @PostMapping("/check")
    @Operation(summary = "检测订单固资规则及生成固资信息")
    @PreAuthenticated
    public CommonResult<Boolean> checkOrderAsset(@RequestParam("orderId") Long orderId) {
        orderItemAssetsService.handleOrderAsset(orderId, false);
        return success(true);
    }

    @PostMapping("/update")
    @Operation(summary = "更新订单明细固资信息")
    @PreAuthenticated
    public CommonResult<Boolean> updateOrderItemAssetInfo(@Valid @RequestBody AppTradeOrderAssetsUpdateReqVO reqVO) {
        reqVO.setUserId(getLoginUserId());
        orderItemAssetsService.updateOrderAssetInfo(reqVO);
        return success(true);
    }

    @PostMapping("/push-by-order")
    @Operation(summary = "更新订单明细固资信息")
    @PreAuthenticated
    public CommonResult<Boolean> pushByOrder(@RequestParam("orderId") Long orderId) {
        orderItemAssetsService.pushOrderAssetsByOrder(getLoginUserId(), orderId);
        return success(true);
    }

    @PostMapping("/push-by-item")
    @Operation(summary = "更新订单明细固资信息")
    @PreAuthenticated
    public CommonResult<Boolean> pushByOrderItem(@RequestParam("orderItemId") Long orderItemId) {
        orderItemAssetsService.pushOrderAssetsByItem(getLoginUserId(), orderItemId);
        return success(true);
    }

    @GetMapping("/list")
    @Operation(summary = "获得订单明细固资信息列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthenticated
    public CommonResult<List<AppTradeOrderItemAssetsRespVO>> getOrderItemAssetsList(@RequestParam("ids") Collection<Long> ids) {
        List<TradeOrderItemAssetsDO> list = orderItemAssetsService.getOrderItemAssetsList(getLoginUserId(), ids);
        return success(TradeOrderAssetsConvert.INSTANCE.convertList4App(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得订单明细固资信息分页")
    @PreAuthenticated
    public CommonResult<PageResult<AppTradeOrderItemAssetsRespVO>> getOrderItemAssetsPage(@Valid AppTradeOrderItemAssetsPageReqVO pageVO) {
        pageVO.setUserId(getLoginUserId());
        PageResult<TradeOrderItemAssetsDO> pageResult = orderItemAssetsService.getOrderItemAssetsPage(pageVO);
        return success(TradeOrderAssetsConvert.INSTANCE.convertPage4App(pageResult));
    }

}
