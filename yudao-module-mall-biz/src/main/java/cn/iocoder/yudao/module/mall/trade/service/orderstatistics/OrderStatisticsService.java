package cn.iocoder.yudao.module.mall.trade.service.orderstatistics;

import java.math.BigDecimal;
import java.util.*;
import javax.validation.*;

import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.mall.trade.controller.admin.orderstatistics.vo.*;
import cn.iocoder.yudao.module.mall.trade.dal.dataobject.orderstatistics.OrderStatisticsDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 订单统计 Service 接口
 *
 * <AUTHOR>
 */
public interface OrderStatisticsService {

    /**
     * 创建订单统计
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createOrderStatistics(@Valid OrderStatisticsCreateReqVO createReqVO);

    /**
     * 更新订单统计
     *
     * @param updateReqVO 更新信息
     */
    void updateOrderStatistics(@Valid OrderStatisticsUpdateReqVO updateReqVO);

    /**
     * 删除订单统计
     *
     * @param id 编号
     */
    void deleteOrderStatistics(Long id);

    /**
     * 获得订单统计
     *
     * @param id 编号
     * @return 订单统计
     */
    OrderStatisticsDO getOrderStatistics(Long id);

    /**
     * 获得订单统计列表
     *
     * @param ids 编号
     * @return 订单统计列表
     */
    List<OrderStatisticsDO> getOrderStatisticsList(Collection<Long> ids);

    /**
     * 获得订单统计分页
     *
     * @param pageReqVO 分页查询
     * @return 订单统计分页
     */
    PageResult<OrderStatisticsDO> getOrderStatisticsPage(OrderStatisticsPageReqVO pageReqVO);

    /**
     * 获得订单统计列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 订单统计列表
     */
    List<OrderStatisticsDO> getOrderStatisticsList(OrderStatisticsExportReqVO exportReqVO);

    void addOrderStatics(OrderStaticsReqVO orderStaticsReqVO);

    List<OrderStatisticsVO> queryOrderStatistics(OrderStatisticsQueryVO queryVO);


    OrderStatisticsTotalResultVO  queryOrderStatisticsTotalVO(Long supplierId);

    /**
     * 统计最近多少天商品销量及销售额
     * @param days 最近多少天
     * @return
     */
    List<SalesAndRevenueSummaryRespVO> getSalesAndRevenueInLastDays(Long days);

    /**
     * 查询近多少天内商品分类销量及销售额排行
     * @param days 最近多少天
     * @param categoryLevel 商品分类级别， 一级 1， 二级 2， 三级 3
     * @return
     */
    List<ProductCategorySalesAndRevenueSummaryRespVO> getSalesAndRevenueInLastDaysByProductCategory(Long days, Long categoryLevel, Long tenantId);


    /**
     * 查询日期范围内具体供应商的销售订单数、销售总金额、结算订单数、结算总金额
     * @return
     */
    SupplierSalesSettleSummaryRespVO getSupplierSalesSettleSummary(SupplierSalesSettleSummaryReqVO supplierSalesSettleSummaryReqVO);

    /**
     * 查询日期范围内每天新增的订单数量及金额、每天确认的订单数量及金额、每天发货的订单数及金额和每天完成的订单数及金额
     * @param summarizeOrderStatsByDaysReqVO
     * @return
     */
    @TenantIgnore
    List<SummarizeOrderStatsByDaysRespVO> getSummarizeOrderStatsByDays(SummarizeOrderStatsByDaysReqVO summarizeOrderStatsByDaysReqVO, Long tenantId);

    /**
     * 供应商销售额占比
     * @return
     */
    List<SupplierSellProportionRespVO> getSupplierSellProportion();

    /**
     * 商品销售数量供应商排行
     * @return
     */
    List<SupplierSellCountSummaryRespVO> getSupplierSellProductCountSummary();

    /**
     * 商品销量排行
     * @return
     */
    List<ProductSellCountSummaryRespVO> getSellProductTotalSummary();

    /**
     * 商品销售额排行
     * @return
     */
    List<ProductSellAmountSummaryRespVO> getSellProductAmountSummary();

    /**
     * 总销量
     * @return
     */
    Long getSellCount();

    /**
     * 总销售额
     * @return
     */
    BigDecimal getSellAmount();

    /**
     * 待结算金额
     * @return
     */
    BigDecimal getNotSettleAmount();

    /**
     * 订单统计：待确认订单、待发货订单、售后订单、已完成订单、待审批订单、已审批订单、待结算订单、已结算订单、已取消订单
     * @return
     */
    OrderSummaryRespVO getOrderSummary();

    /**
     * 部门销售额排行
     * @param limit
     * @return
     */
    List<OrderDeptSummaryRespVO> getOrderSummaryByDept(Integer limit);

    /**
     * 项目销售额排行
     * @return
     */
    List<OrderProjectSummaryRespVO> getOrderSummaryByProject();

    /**
     * 供应商销售额排行
     * @return
     */
    List<OrderSupplierSummaryRespVO> getOrderSummaryBySupplier();

    /**
     * 供应商售后订单量排行
     * @return
     */
    List<OrderAfterSaleSummaryRespVO> getAfterSaleOrderSummary();

    /**
     * 按天统计销售额
     * @return
     */
    List<OrderSaleDaySummaryRespVO> dailySaleSummary(OrderSaleSummaryReqVO orderSaleSummaryReqVO);

    /**
     * 按周统计销售额
     * @return
     */
    List<OrderSaleWeekSummaryRespVO> weeklySalesSummary(OrderSaleSummaryReqVO orderSaleSummaryReqVO);

    /**
     * 按月统计销售额
     * @return
     */
    List<OrderSaleMonthSummaryRespVO> monthlySalesSummary(OrderSaleMonthSummaryReqVO orderSaleMonthSummaryReqVO);
}
