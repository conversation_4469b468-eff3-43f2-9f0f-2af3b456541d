package cn.iocoder.yudao.module.mall.trade.dal.dataobject.delivery;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 平台配置物流公司 DO
 *
 * <AUTHOR>
 */
@TableName("config_delivery_company")
@KeySequence("config_delivery_company_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConfigDeliveryCompanyDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 物流公司编码
     */
    private String com;
    /**
     * 物流公司名称
     */
    private String name;
    /**
     * 物流公司类型
     */
    private String type;

}
