package cn.iocoder.yudao.module.mall.basis.service.project;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.mall.basis.controller.admin.project.vo.ProjectWhiteSkuCreateReqVO;
import cn.iocoder.yudao.module.mall.basis.controller.admin.project.vo.ProjectWhiteSkuPageReqVO;
import cn.iocoder.yudao.module.mall.basis.dal.dataobject.project.ProjectWhiteSkuDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 项目经费卡SKU白名单 Service 接口
 *
 * <AUTHOR>
 */
public interface ProjectWhiteSkuService {

    /**
     * 创建项目经费卡SKU白名单
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProjectWhiteSku(@Valid ProjectWhiteSkuCreateReqVO createReqVO);

    /**
     * 创建项目经费卡SKU白名单
     * @param skuList skuId集合
     * @return
     */
    void createProjectWhiteSku(List<String> skuList);

    /**
     * 查询SKU白名单ID列表
     * @return
     */
    List<Long> getWhiteSkuList();

    /**
     * 批量创建规则关联分类
     *
     * @param createReqVOs
     * @return
     */
    void createProjectWhiteSkuBatch(@Valid List<ProjectWhiteSkuCreateReqVO> createReqVOs);

    /**
     * 删除项目经费卡SKU白名单
     *
     * @param id 编号
     */
    void deleteProjectWhiteSku(Long id);

    /**
     * 批量删除项目经费卡SKU白名单
     *
     * @param ids 编号
     */
    void deleteProjectWhiteSkuBatch(List<Long> ids);

    /**
     * 获得项目经费卡SKU白名单分页
     *
     * @param id 编号
     * @return 项目经费卡SKU白名单分页
     */
    ProjectWhiteSkuDO getProjectWhiteSku(Long id);

    /**
     * 获得项目经费卡SKU白名单分页列表
     *
     * @param ids 编号
     * @return 项目经费卡SKU白名单列表
     */
    List<ProjectWhiteSkuDO> getProjectWhiteSkuList(Collection<Long> ids);


    /**
     * 获得项目经费卡SKU白名单分页
     *
     * @param pageReqVO 分页查询
     * @return 项目经费卡SKU白名单分页
     */
    PageResult<ProjectWhiteSkuDO> getProjectWhiteSkuPage(ProjectWhiteSkuPageReqVO pageReqVO);

}
