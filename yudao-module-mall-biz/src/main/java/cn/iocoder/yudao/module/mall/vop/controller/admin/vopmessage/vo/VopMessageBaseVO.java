package cn.iocoder.yudao.module.mall.vop.controller.admin.vopmessage.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

/**
 * VOP消息 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class VopMessageBaseVO {

    @Schema(description = "客户ID")
    private String clientId;

    @Schema(description = "是否有效")
    private Integer yn;

    @Schema(description = "VOP消息Id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "VOP消息Id不能为空")
    private Long messageId;

    @Schema(description = "消息类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "消息类型不能为空")
    private Integer type;

    @Schema(description = "消息内容")
    private String content;

    @Schema(description = "消息处理状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "消息处理状态不能为空")
    private Integer handleStatus;

    @Schema(description = "关键字")
    private String keyword;
}
