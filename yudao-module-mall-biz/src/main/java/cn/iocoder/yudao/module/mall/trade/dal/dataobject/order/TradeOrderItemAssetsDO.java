package cn.iocoder.yudao.module.mall.trade.dal.dataobject.order;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单明细固资信息 DO
 *
 * <AUTHOR>
 */
@TableName("trade_order_item_assets")
@KeySequence("trade_order_item_assets_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TradeOrderItemAssetsDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 资产办理人工号
     */
    private String userNo;
    /**
     * 资产办理人用户名
     */
    private String userName;
    /**
     * 资产办理人部门编号
     */
    private String deptNo;
    /**
     * 资产办理人部门名称
     */
    private String deptName;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 供应商名称
     */
    private String supplier;
    /**
     * 供应商ID
     */
    private Long supplierId;
    /**
     * 商品名称
     */
    private String skuName;
    /**
     * 单价
     */
    private BigDecimal price;
    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 型号
     */
    private String model;
    /**
     * 规格
     */
    private String spec;
    /**
     * 联系人手机号
     */
    private String phone;
    /**
     * 发票号
     */
    private String invoiceNo;
    /**
     * 发票地址
     */
    private String invoiceUrl;
    /**
     * 购买日期
     */
    private LocalDateTime buyDate;
    /**
     * 订单ID
     */
    private Long orderId;
    /**
     * 订单明细ID
     */
    private Long orderItemId;
    /**
     * 固资建档审批状态
     * {@link cn.iocoder.yudao.module.mall.trade.enums.order.AssetStatusEnum}
     */
    private Integer assetStatus;
    /**
     * 外部系统编码
     */
    private String sysCode;
    /**
     * 外部类别编码，如中南大学中家具，设备，低值等类别
     */
    private String extCategoryCode;
    /**
     * 外部类别名称
     */
    private String extCategoryName;
    /**
     * 建档完成日期
     */
    private LocalDateTime assetFinishTime;
    /**
     * 资产验收证明材料URL
     */
    private String assetProofFile;
    /**
     * 业财凭证名称
     */
    private String voucherName;
    /**
     * 业财凭证上传状态
     */
    private Boolean voucherStatus;

}
