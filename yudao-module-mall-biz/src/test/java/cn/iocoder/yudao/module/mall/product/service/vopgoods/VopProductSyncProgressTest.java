package cn.iocoder.yudao.module.mall.product.service.vopgoods;

import cn.iocoder.yudao.module.mall.product.controller.admin.vopgoods.vo.VopProductSyncProgressVO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * VOP商品同步进度测试
 *
 * <AUTHOR>
 */
@SpringBootTest
public class VopProductSyncProgressTest {

    @Test
    public void testProgressKeyGeneration() {
        // 测试进度Key生成
        String key1 = VopProductSyncProgressVO.generateProgressKey(1L, 100L, 50L);
        String key2 = VopProductSyncProgressVO.generateProgressKey(1L, null, 50L);
        String key3 = VopProductSyncProgressVO.generateProgressKey(1L, 100L, null);
        
        assertEquals("mall_vop_product_sync_progress:1:100:50", key1);
        assertEquals("mall_vop_product_sync_progress:1:all:50", key2);
        assertEquals("mall_vop_product_sync_progress:1:100:default", key3);
    }

    @Test
    public void testParametersMatch() {
        VopProductSyncProgressVO progress = new VopProductSyncProgressVO();
        progress.setCategoryId(100L);
        progress.setMaxPageSize(50L);
        progress.setMinPrice(new BigDecimal("10.00"));
        progress.setMaxPrice(new BigDecimal("1000.00"));
        
        // 测试参数匹配
        assertTrue(progress.isParametersMatch(100L, 50L, new BigDecimal("10.00"), new BigDecimal("1000.00")));
        assertFalse(progress.isParametersMatch(200L, 50L, new BigDecimal("10.00"), new BigDecimal("1000.00")));
        assertFalse(progress.isParametersMatch(100L, 100L, new BigDecimal("10.00"), new BigDecimal("1000.00")));
        assertFalse(progress.isParametersMatch(100L, 50L, new BigDecimal("20.00"), new BigDecimal("1000.00")));
        
        // 测试null值匹配
        progress.setCategoryId(null);
        assertTrue(progress.isParametersMatch(null, 50L, new BigDecimal("10.00"), new BigDecimal("1000.00")));
        assertFalse(progress.isParametersMatch(100L, 50L, new BigDecimal("10.00"), new BigDecimal("1000.00")));
    }

    @Test
    public void testProgressCalculation() {
        VopProductSyncProgressVO progress = new VopProductSyncProgressVO();
        
        // 测试初始状态
        assertEquals(0, progress.calculateProgressPercentage());
        
        // 设置总分类数
        progress.setTotalCategoryCount(10);
        assertEquals(0, progress.calculateProgressPercentage());
        
        // 设置当前分类索引
        progress.setCurrentCategoryIndex(3);
        assertEquals(30, progress.calculateProgressPercentage());
        
        // 设置当前分类页面信息
        progress.setCurrentCategoryPageCount(20);
        progress.setCurrentPageIndex(10);
        assertEquals(35, progress.calculateProgressPercentage()); // 30% + 5%
        
        // 测试完成状态
        progress.setCompleted(true);
        assertEquals(100, progress.calculateProgressPercentage());
    }

    @Test
    public void testUpdateProgress() {
        VopProductSyncProgressVO progress = new VopProductSyncProgressVO();
        LocalDateTime beforeUpdate = LocalDateTime.now();
        
        progress.updateProgress(5, 10, 100L);
        
        assertEquals(5, progress.getCurrentCategoryIndex());
        assertEquals(10, progress.getCurrentPageIndex());
        assertEquals(100L, progress.getSyncedProductCount());
        assertNotNull(progress.getLastUpdateTime());
        assertTrue(progress.getLastUpdateTime().isAfter(beforeUpdate));
    }

    @Test
    public void testMarkCompleted() {
        VopProductSyncProgressVO progress = new VopProductSyncProgressVO();
        LocalDateTime beforeComplete = LocalDateTime.now();
        
        progress.markCompleted();
        
        assertTrue(progress.getCompleted());
        assertNotNull(progress.getLastUpdateTime());
        assertTrue(progress.getLastUpdateTime().isAfter(beforeComplete));
    }
}
