package cn.iocoder.yudao.module.mall.product.controller.admin.vopgoods;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.mall.product.service.vopgoods.VopGoodsBridgeService;
import cn.iocoder.yudao.module.mall.product.service.vopgoods.vo.VopProductSyncProgress;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * VOP商品同步控制器并发测试
 *
 * <AUTHOR>
 */
@SpringBootTest
public class VopProductSyncControllerTest {

    @Mock
    private VopGoodsBridgeService vopGoodsBridgeService;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private RLock lock;

    @InjectMocks
    private VopProductSyncController controller;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        TenantContextHolder.setTenantId(1L);
    }

    @Test
    public void testConcurrentSyncPrevention() throws Exception {
        // 模拟锁已被占用的情况
        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock(anyLong(), any(TimeUnit.class))).thenReturn(false);

        // 调用同步方法
        CommonResult<String> result = controller.startSync(123L, 50L);

        // 验证返回结果
        assertTrue(result.isSuccess());
        assertEquals("同步任务已在运行中，请稍后再试", result.getData());

        // 验证没有调用实际的同步方法
        verify(vopGoodsBridgeService, never()).syncVopProductByCategory(anyLong(), anyLong());
    }

    @Test
    public void testSuccessfulSyncStart() throws Exception {
        // 模拟成功获取锁
        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock(anyLong(), any(TimeUnit.class))).thenReturn(true);
        when(lock.isHeldByCurrentThread()).thenReturn(true);

        // 模拟没有现有进度
        when(vopGoodsBridgeService.getSyncProgress(anyLong(), anyLong())).thenReturn(null);

        // 调用同步方法
        CommonResult<String> result = controller.startSync(123L, 50L);

        // 验证返回结果
        assertTrue(result.isSuccess());
        assertEquals("同步任务已启动", result.getData());

        // 等待异步任务启动
        Thread.sleep(100);

        // 验证锁被获取
        verify(lock).tryLock(anyLong(), any(TimeUnit.class));
    }

    @Test
    public void testContinueExistingProgress() throws Exception {
        // 模拟成功获取锁
        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock(anyLong(), any(TimeUnit.class))).thenReturn(true);
        when(lock.isHeldByCurrentThread()).thenReturn(true);

        // 模拟存在未完成的进度
        VopProductSyncProgress existingProgress = new VopProductSyncProgress();
        existingProgress.setCompleted(false);
        existingProgress.setCurrentCategoryIndex(2);
        existingProgress.setCurrentPageIndex(5);
        when(vopGoodsBridgeService.getSyncProgress(anyLong(), anyLong())).thenReturn(existingProgress);

        // 调用同步方法
        CommonResult<String> result = controller.startSync(123L, 50L);

        // 验证返回结果
        assertTrue(result.isSuccess());
        assertEquals("同步任务已启动", result.getData());
    }

    @Test
    public void testMultipleConcurrentRequests() throws Exception {
        // 模拟第一个请求获取锁成功，后续请求失败
        when(redissonClient.getLock(anyString())).thenReturn(lock);
        
        AtomicInteger lockAttempts = new AtomicInteger(0);
        when(lock.tryLock(anyLong(), any(TimeUnit.class))).thenAnswer(invocation -> {
            return lockAttempts.incrementAndGet() == 1; // 只有第一次返回true
        });
        when(lock.isHeldByCurrentThread()).thenReturn(true);
        when(vopGoodsBridgeService.getSyncProgress(anyLong(), anyLong())).thenReturn(null);

        int threadCount = 5;
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger rejectedCount = new AtomicInteger(0);

        // 启动多个并发请求
        for (int i = 0; i < threadCount; i++) {
            new Thread(() -> {
                try {
                    CommonResult<String> result = controller.startSync(123L, 50L);
                    if ("同步任务已启动".equals(result.getData())) {
                        successCount.incrementAndGet();
                    } else if ("同步任务已在运行中，请稍后再试".equals(result.getData())) {
                        rejectedCount.incrementAndGet();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    latch.countDown();
                }
            }).start();
        }

        // 等待所有线程完成
        latch.await(5, TimeUnit.SECONDS);

        // 验证只有一个请求成功，其他被拒绝
        assertEquals(1, successCount.get());
        assertEquals(threadCount - 1, rejectedCount.get());
    }

    @Test
    public void testLockKeyGeneration() {
        // 使用反射或其他方式测试锁Key生成逻辑
        // 这里简化测试，主要验证不同参数生成不同的Key
        
        // 模拟不同的参数组合
        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock(anyLong(), any(TimeUnit.class))).thenReturn(false);

        // 测试不同参数
        controller.startSync(123L, 50L);
        controller.startSync(456L, 50L);
        controller.startSync(123L, 100L);
        controller.startSync(null, 50L);

        // 验证调用了不同的锁Key
        verify(redissonClient, times(4)).getLock(anyString());
    }
}
